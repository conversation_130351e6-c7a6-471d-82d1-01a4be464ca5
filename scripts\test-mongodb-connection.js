// Test MongoDB connection
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env' });

async function testConnection() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }
  
  console.log('Testing MongoDB connection...');
  console.log('URI:', uri.replace(/\/\/[^:]+:[^@]+@/, '//****:****@')); // Hide credentials
  
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    ssl: true,
    tls: true,
    tlsAllowInvalidCertificates: true, // For testing only
    tlsAllowInvalidHostnames: true, // For testing only
  });
  
  try {
    await client.connect();
    console.log('Successfully connected to MongoDB!');
    
    // List databases
    const admin = client.db().admin();
    const dbs = await admin.listDatabases();
    console.log('Available databases:');
    dbs.databases.forEach(db => {
      console.log(`- ${db.name}`);
    });
    
    // Test specific database
    const dbName = process.env.MONGODB_DB || 'rentcentral';
    const db = client.db(dbName);
    const collections = await db.listCollections().toArray();
    console.log(`\nCollections in ${dbName}:`);
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
  } finally {
    await client.close();
  }
}

testConnection(); 