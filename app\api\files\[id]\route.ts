import { NextResponse } from 'next/server';

// Interface for file data
interface FileData {
  id: string;
  name: string;
  type: string;
  data: string;
  createdAt: Date;
}

// Get the global storage
declare global {
  var fileStorage: Map<string, FileData> | undefined;
}

// Access the shared storage
const fileStorage = global.fileStorage || new Map<string, FileData>();
if (!global.fileStorage) {
  global.fileStorage = fileStorage;
}

// Simple version to debug the issue
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Ensure params.id is properly awaited
    const fileId = params.id;
    console.log('File requested:', fileId);
    
    // Check if file exists in memory storage
    const file = fileStorage.get(fileId);
    
    if (file && file.data) {
      console.log('Found file in memory:', file.name);
      
      // Convert base64 back to buffer
      const buffer = Buffer.from(file.data, 'base64');
      
      // Return the file with proper content type
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': file.type || 'application/octet-stream',
          'Content-Disposition': `inline; filename="${file.name}"`,
        },
      });
    }
    
    // Fallback: Generate a placeholder SVG if file not found
    console.log('Generating placeholder image for ID:', fileId);
    const svgContent = `
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="#ddd"/>
        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Arial" font-size="20" fill="#333">
          Image ${fileId}
        </text>
      </svg>
    `;
    
    // Return the SVG as an image
    return new NextResponse(svgContent, {
      headers: {
        'Content-Type': 'image/svg+xml',
      },
    });
  } catch (error) {
    console.error('Error serving file:', error);
    return NextResponse.json(
      { error: 'Failed to serve file' },
      { status: 500 }
    );
  }
} 