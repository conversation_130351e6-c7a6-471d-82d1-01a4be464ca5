import { Button } from './ui/button';
import { FcGoogle } from 'react-icons/fc';

interface GoogleSignInButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

export function GoogleSignInButton({ onClick, disabled = false }: GoogleSignInButtonProps) {
  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center gap-2"
      onClick={onClick}
      disabled={disabled}
    >
      <FcGoogle className="w-5 h-5" />
      <span>Continue with Google</span>
    </Button>
  );
} 