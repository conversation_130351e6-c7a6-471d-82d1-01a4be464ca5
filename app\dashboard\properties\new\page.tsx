"use client"

import { useRouter } from "next/navigation"
import { PropertyForm } from "@/components/property-form"
import { useAuth } from "@/components/auth-provider"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NewPropertyPage() {
  const router = useRouter()
  const { user, loading } = useAuth()
  const { toast } = useToast()
  const [isAuthorized, setIsAuthorized] = useState(true)

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push("/auth/login")
      }
      // Commenting out landlord check for demo purposes
      // else if (user && !user.isLandlord) {
      //   setIsAuthorized(false)
      //   toast({
      //     title: "Access Granted",
      //     description: "For demo purposes, any user can create property listings.",
      //   })
      // }
    }
  }, [user, loading, router, toast])

  const handleSuccess = (propertyId: string) => {
    toast({
      title: "Property Listed",
      description: "Your property has been successfully listed.",
    })
    router.push("/dashboard")
  }

  if (loading) {
    return (
      <div className="container flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!isAuthorized) {
    return (
      <div className="container py-8">
        <div className="max-w-lg mx-auto text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="mb-6 text-muted-foreground">
            Only landlords can create property listings. Please contact support to upgrade your account.
          </p>
          <Button asChild>
            <Link href="/dashboard">
              Return to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" className="gap-2" asChild>
          <Link href="/dashboard" legacyBehavior>
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">List Your Property</h1>
        <p className="text-muted-foreground">
          Fill out the form below to list your property for rent
        </p>
      </div>
      <div className="mb-8">
        <PropertyForm onSuccess={handleSuccess} />
      </div>
    </div>
  );
}
