'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { Send, Paperclip, Image as ImageIcon, MapPin, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { Message } from './message';
import { TypingIndicator } from './typing-indicator';
import { MessageThread } from './message-thread';
import { AttachmentMenu } from './attachment-menu';

interface Message {
  id: string;
  content: {
    type: 'text' | 'image' | 'property' | 'location';
    text?: string;
    imageUrl?: string;
    propertyId?: string;
    location?: {
      lat: number;
      lng: number;
      address: string;
    };
  };
  senderId: string;
  senderName: string;
  senderImage?: string;
  createdAt: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
  reactions: {
    id: string;
    emoji: string;
    count: number;
    users: {
      id: string;
      name: string;
    }[];
  }[];
  replyCount: number;
}

interface ChatProps {
  conversationId: string;
  otherUser: {
    id: string;
    name: string;
    image?: string;
  };
  isTyping?: boolean;
}

export function Chat({ conversationId, otherUser, isTyping = false }: ChatProps) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [activeThreadId, setActiveThreadId] = useState<string | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchMessages();
  }, [conversationId]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const fetchMessages = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const data = await response.json();
      setMessages(data);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || isSending) return;
    
    const messageText = newMessage;
    setNewMessage('');
    
    // Optimistically add the message
    const tempId = `temp-${Date.now()}`;
    const newMsg: Message = {
      id: tempId,
      content: {
        type: 'text',
        text: messageText,
      },
      senderId: session?.user?.id || '',
      senderName: session?.user?.name || 'You',
      senderImage: session?.user?.image,
      createdAt: new Date().toISOString(),
      status: 'sending',
      reactions: [],
      replyCount: 0,
    };
    
    setMessages((prev) => [...prev, newMsg]);
    
    setIsSending(true);
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'text',
            text: messageText,
          },
        }),
      });
      
      if (!response.ok) throw new Error('Failed to send message');
      
      const data = await response.json();
      
      // Replace the temporary message with the real one
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === tempId ? { ...data, status: 'sent' } : msg
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Update the message status to error
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === tempId ? { ...msg, status: 'error' } : msg
        )
      );
    } finally {
      setIsSending(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!file || isSending) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    setIsSending(true);
    try {
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!uploadResponse.ok) throw new Error('Failed to upload file');
      
      const { url } = await uploadResponse.json();
      
      // Optimistically add the message
      const tempId = `temp-${Date.now()}`;
      const newMsg: Message = {
        id: tempId,
        content: {
          type: 'image',
          imageUrl: url,
        },
        senderId: session?.user?.id || '',
        senderName: session?.user?.name || 'You',
        senderImage: session?.user?.image,
        createdAt: new Date().toISOString(),
        status: 'sending',
        reactions: [],
        replyCount: 0,
      };
      
      setMessages((prev) => [...prev, newMsg]);
      
      // Send the message with the image URL
      const messageResponse = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'image',
            imageUrl: url,
          },
        }),
      });
      
      if (!messageResponse.ok) throw new Error('Failed to send message');
      
      const data = await messageResponse.json();
      
      // Replace the temporary message with the real one
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === tempId ? { ...data, status: 'sent' } : msg
        )
      );
    } catch (error) {
      console.error('Error sending image:', error);
      
      // Update the message status to error if it exists
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id.startsWith('temp-') ? { ...msg, status: 'error' } : msg
        )
      );
    } finally {
      setIsSending(false);
    }
  };

  const handlePropertyShare = async (propertyId: string) => {
    if (!propertyId || isSending) return;
    
    setIsSending(true);
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'property',
            propertyId,
          },
        }),
      });
      
      if (!response.ok) throw new Error('Failed to send message');
      
      const data = await response.json();
      setMessages((prev) => [...prev, data]);
    } catch (error) {
      console.error('Error sharing property:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleLocationShare = async (location: { lat: number; lng: number; address: string }) => {
    if (!location || isSending) return;
    
    setIsSending(true);
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'location',
            location,
          },
        }),
      });
      
      if (!response.ok) throw new Error('Failed to send message');
      
      const data = await response.json();
      setMessages((prev) => [...prev, data]);
    } catch (error) {
      console.error('Error sharing location:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleAddReaction = async (messageId: string, emoji: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages/${messageId}/reactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ emoji }),
      });
      
      if (!response.ok) throw new Error('Failed to add reaction');
      
      const data = await response.json();
      
      // Update the message with the new reaction
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === messageId ? { ...msg, reactions: data.reactions } : msg
        )
      );
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
  };

  const handleRemoveReaction = async (messageId: string, emoji: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages/${messageId}/reactions/${emoji}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) throw new Error('Failed to remove reaction');
      
      const data = await response.json();
      
      // Update the message with the updated reactions
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === messageId ? { ...msg, reactions: data.reactions } : msg
        )
      );
    } catch (error) {
      console.error('Error removing reaction:', error);
    }
  };

  const handleReply = (messageId: string) => {
    setActiveThreadId(messageId);
  };

  const handleCloseThread = () => {
    setActiveThreadId(null);
  };

  const handleSendReply = async (parentMessageId: string, content: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages/${parentMessageId}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'text',
            text: content,
          },
        }),
      });
      
      if (!response.ok) throw new Error('Failed to send reply');
      
      // Update the reply count for the parent message
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === parentMessageId 
            ? { ...msg, replyCount: (msg.replyCount || 0) + 1 } 
            : msg
        )
      );
      
      return await response.json();
    } catch (error) {
      console.error('Error sending reply:', error);
      throw error;
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center p-4 border-b">
        <Avatar className="h-10 w-10 mr-3">
          <AvatarImage src={otherUser.image} alt={otherUser.name} />
          <AvatarFallback>{otherUser.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <h2 className="font-medium">{otherUser.name}</h2>
          {isTyping && <p className="text-xs text-muted-foreground">Typing...</p>}
        </div>
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        <div className={`flex-1 flex flex-col ${activeThreadId ? 'w-2/3' : 'w-full'}`}>
          <ScrollArea className="flex-1 p-4" ref={scrollRef}>
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No messages yet. Start the conversation!
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <Message
                    key={message.id}
                    id={message.id}
                    content={message.content}
                    senderId={message.senderId}
                    senderName={message.senderName}
                    senderImage={message.senderImage}
                    createdAt={message.createdAt}
                    status={message.status}
                    isCurrentUser={message.senderId === session?.user?.id}
                    reactions={message.reactions}
                    replyCount={message.replyCount}
                    onReply={handleReply}
                    onAddReaction={handleAddReaction}
                    onRemoveReaction={handleRemoveReaction}
                  />
                ))}
                
                {isTyping && <TypingIndicator />}
              </div>
            )}
          </ScrollArea>
          
          <form onSubmit={handleSendMessage} className="p-4 border-t">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
                disabled={isSending}
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              
              {showAttachmentMenu && (
                <AttachmentMenu
                  onImageSelect={() => fileInputRef.current?.click()}
                  onPropertySelect={handlePropertyShare}
                  onLocationSelect={handleLocationShare}
                  onClose={() => setShowAttachmentMenu(false)}
                />
              )}
              
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileUpload(file);
                }}
              />
              
              <Textarea
                placeholder="Type a message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                className="min-h-[80px] resize-none"
                disabled={isSending}
              />
              <Button type="submit" size="icon" disabled={isSending || !newMessage.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
        
        {activeThreadId && (
          <MessageThread
            parentMessageId={activeThreadId}
            conversationId={conversationId}
            onClose={handleCloseThread}
            onSendReply={handleSendReply}
            onAddReaction={handleAddReaction}
            onRemoveReaction={handleRemoveReaction}
          />
        )}
      </div>
    </div>
  );
} 