'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { ConversationList } from '@/components/messaging/conversation-list';
import { NewConversation } from '@/components/messaging/new-conversation';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';

export default function MessagesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [conversations, setConversations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchConversations();
    }
  }, [status]);

  const fetchConversations = async () => {
    try {
      const response = await fetch('/api/conversations');
      if (!response.ok) throw new Error('Failed to fetch conversations');
      
      const data = await response.json();
      setConversations(data);
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConversationCreated = (conversationId: string) => {
    router.push(`/messages/${conversationId}`);
  };

  if (status === 'loading') {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">Messages</h1>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-3 w-[200px]" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Messages</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1 border rounded-lg overflow-hidden">
          <div className="p-4 border-b">
            <NewConversation 
              currentUserId={session.user.id} 
              onConversationCreated={handleConversationCreated} 
            />
          </div>
          <ConversationList 
            conversations={conversations} 
            currentUserId={session.user.id} 
            isLoading={isLoading} 
          />
        </div>
        
        <div className="md:col-span-2 border rounded-lg h-[calc(100vh-12rem)] flex items-center justify-center">
          <div className="text-center p-8">
            <h2 className="text-xl font-medium mb-2">Select a conversation</h2>
            <p className="text-gray-500">
              Choose a conversation from the list or start a new one to begin messaging.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 