import { useEffect, useState } from 'react';
import { Clock, Check, CheckCheck } from 'lucide-react';
import { Socket } from 'socket.io-client';
import { MessageStatus } from '@/hooks/useSocket';

interface MessageStatusProps {
  socket: Socket | null;
  messageId: string;
  conversationId: string;
  initialStatus?: MessageStatus;
}

export function MessageStatusIndicator({ 
  socket, 
  messageId, 
  conversationId, 
  initialStatus 
}: MessageStatusProps) {
  const [status, setStatus] = useState<MessageStatus | undefined>(initialStatus);
  const [statusTimestamp, setStatusTimestamp] = useState<Date | null>(null);

  useEffect(() => {
    if (!socket) return;

    const handleMessageDelivered = (data: { 
      messageId: string; 
      conversationId: string;
      timestamp: string;
    }) => {
      if (data.messageId === messageId && data.conversationId === conversationId) {
        setStatus('delivered');
        setStatusTimestamp(new Date(data.timestamp));
      }
    };

    const handleMessageRead = (data: { 
      messageId: string; 
      conversationId: string;
      timestamp: string;
    }) => {
      if (data.messageId === messageId && data.conversationId === conversationId) {
        setStatus('read');
        setStatusTimestamp(new Date(data.timestamp));
      }
    };

    socket.on('message_delivered', handleMessageDelivered);
    socket.on('message_read', handleMessageRead);

    return () => {
      socket.off('message_delivered', handleMessageDelivered);
      socket.off('message_read', handleMessageRead);
    };
  }, [socket, messageId, conversationId]);

  const getStatusIcon = () => {
    switch (status) {
      case 'sent':
        return <Check className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="h-3 w-3 text-blue-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  return (
    <div className="flex items-center">
      {getStatusIcon()}
      {statusTimestamp && (
        <span className="text-xs text-gray-400 ml-1">
          {status === 'read' ? 'Read' : status === 'delivered' ? 'Delivered' : 'Sent'}
        </span>
      )}
    </div>
  );
} 