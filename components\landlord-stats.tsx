import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Building, Users, FileText, DollarSign } from "lucide-react"

export function LandlordStats() {
  const stats = [
    {
      title: "Total Properties",
      value: "5",
      icon: <Building className="h-4 w-4 text-muted-foreground" />,
      description: "3 occupied, 2 vacant",
    },
    {
      title: "Active Tenants",
      value: "8",
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
      description: "Across all properties",
    },
    {
      title: "Pending Applications",
      value: "12",
      icon: <FileText className="h-4 w-4 text-muted-foreground" />,
      description: "Needs your review",
    },
    {
      title: "Monthly Revenue",
      value: "$9,850",
      icon: <DollarSign className="h-4 w-4 text-muted-foreground" />,
      description: "From all properties",
    },
  ]

  return (
    <>
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            {stat.icon}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </>
  )
}
