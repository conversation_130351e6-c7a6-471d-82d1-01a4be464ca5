"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, X, Upload, ImageIcon } from "lucide-react"
import Image from "next/image"
import { DndProvider, useDrag, useDrop } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"

interface PropertyUploadProps {
  onUploadComplete: (urls: string[]) => void;
  maxFiles?: number;
  initialImages?: string[];
}

interface DraggableImageProps {
  src: string
  index: number
  moveImage: (dragIndex: number, hoverIndex: number) => void
  onRemove: (index: number) => void
  isPrimary: boolean
}

const DraggableImage = ({ src, index, moveImage, onRemove, isPrimary }: DraggableImageProps) => {
  const ref = useRef<HTMLDivElement>(null)
  
  const [{ isDragging }, drag] = useDrag({
    type: "IMAGE",
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: "IMAGE",
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveImage(item.index, index)
        item.index = index
      }
    },
  })

  drag(drop(ref))

  return (
    <div
      ref={ref}
      className={`relative aspect-square rounded-md overflow-hidden border ${
        isDragging ? "opacity-50" : "opacity-100"
      } ${isPrimary ? "border-primary" : "border-border"}`}
    >
      <Image src={src} alt={`Image ${index + 1}`} fill className="object-cover" />
      <Button
        variant="destructive"
        size="icon"
        className="absolute top-1 right-1 h-6 w-6 rounded-full"
        onClick={() => onRemove(index)}
      >
        <X className="h-3 w-3" />
      </Button>
      {isPrimary && (
        <div className="absolute bottom-1 left-1 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
          Primary
        </div>
      )}
    </div>
  )
}

export default function PropertyUpload({ onUploadComplete, maxFiles = 5, initialImages = [] }: PropertyUploadProps) {
  const [files, setFiles] = useState<File[]>([])
  const [previews, setPreviews] = useState<string[]>([])
  const [uploadedImages, setUploadedImages] = useState<string[]>(initialImages)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const { toast } = useToast()

  useEffect(() => {
    if (initialImages.length > 0) {
      setUploadedImages(initialImages)
    }
  }, [initialImages])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    const selectedFiles = Array.from(e.target.files)

    if (uploadedImages.length + files.length + selectedFiles.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can only upload a maximum of ${maxFiles} images.`,
        variant: "destructive",
      })
      return
    }

    const newPreviews = selectedFiles.map((file) => URL.createObjectURL(file))

    setFiles((prev) => [...prev, ...selectedFiles])
    setPreviews((prev) => [...prev, ...newPreviews])
  }

  const removeFile = (index: number) => {
    URL.revokeObjectURL(previews[index])
    setFiles((prev) => prev.filter((_, i) => i !== index))
    setPreviews((prev) => prev.filter((_, i) => i !== index))
  }

  const removeUploadedImage = (index: number) => {
    setUploadedImages((prev) => prev.filter((_, i) => i !== index))
    onUploadComplete(uploadedImages.filter((_, i) => i !== index))
  }

  const moveImage = (dragIndex: number, hoverIndex: number) => {
    const draggedImage = uploadedImages[dragIndex]
    const updatedImages = [...uploadedImages]
    updatedImages.splice(dragIndex, 1)
    updatedImages.splice(hoverIndex, 0, draggedImage)
    setUploadedImages(updatedImages)
    onUploadComplete(updatedImages)
  }

  const handleUpload = async () => {
    if (files.length === 0) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      const newUploadedUrls: string[] = []

      for (let i = 0; i < files.length; i++) {
        const formData = new FormData()
        formData.append('file', files[i])

        console.log('Uploading file:', files[i].name)
        
        try {
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
            credentials: 'include',
          })

          // Check if response is not JSON
          const contentType = response.headers.get('content-type')
          if (!contentType || !contentType.includes('application/json')) {
            console.error('Received non-JSON response:', await response.text())
            throw new Error('Server returned invalid response format')
          }

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.error || 'Failed to upload file')
          }

          if (!data.url) {
            throw new Error('No URL returned from server')
          }

          console.log('Upload successful:', data)
          newUploadedUrls.push(data.url)
        } catch (err) {
          console.error('Error uploading individual file:', err)
          throw err
        }

        setUploadProgress(Math.round(((i + 1) / files.length) * 100))
      }

      const allUploadedImages = [...uploadedImages, ...newUploadedUrls]
      onUploadComplete(allUploadedImages)
      setUploadedImages(allUploadedImages)
      setPreviews([])
      setFiles([])

      toast({
        title: "Upload complete",
        description: `Successfully uploaded ${newUploadedUrls.length} images.`,
      })
    } catch (error) {
      console.error("Error uploading files:", error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "There was an error uploading your images. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="property-images">Property Images</Label>
          <span className="text-sm text-muted-foreground">
            {uploadedImages.length + files.length} / {maxFiles} images
          </span>
        </div>

        {uploadedImages.length > 0 && (
          <div className="space-y-2">
            <Label>Uploaded Images (drag to reorder)</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {uploadedImages.map((image, index) => (
                <DraggableImage
                  key={`${image}-${index}`}
                  src={image}
                  index={index}
                  moveImage={moveImage}
                  onRemove={removeUploadedImage}
                  isPrimary={index === 0}
                />
              ))}
            </div>
          </div>
        )}

        {previews.length > 0 && (
          <div className="space-y-2">
            <Label>Selected Images</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {previews.map((preview, index) => (
                <div key={index} className="relative aspect-square rounded-md overflow-hidden border">
                  <Image
                    src={preview}
                    alt={`Preview ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-1 right-1 h-6 w-6 rounded-full"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="space-y-4">
          {uploadedImages.length + files.length < maxFiles && (
            <div className="flex items-center gap-4">
              <label
                htmlFor="property-images"
                className="flex flex-col items-center justify-center aspect-square w-full max-w-[150px] rounded-md border border-dashed cursor-pointer hover:bg-muted/50"
              >
                <div className="flex flex-col items-center justify-center p-4">
                  <ImageIcon className="h-8 w-8 mb-2 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Add Image</span>
                </div>
                <Input
                  id="property-images"
                  type="file"
                  accept="image/*"
                  multiple
                  className="sr-only"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </label>
              <div className="text-sm text-muted-foreground">
                <p>Click to select images to upload.</p>
                <p>The first image will be used as the primary image.</p>
                <p>You can drag images to reorder them after uploading.</p>
              </div>
            </div>
          )}

          {files.length > 0 && (
            <div className="space-y-2">
              {isUploading && (
                <div className="w-full bg-muted rounded-full h-2.5">
                  <div className="bg-primary h-2.5 rounded-full" style={{ width: `${uploadProgress}%` }}></div>
                </div>
              )}
              <Button onClick={handleUpload} disabled={isUploading} className="w-full">
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading ({uploadProgress}%)
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload {files.length} Image{files.length !== 1 ? "s" : ""}
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </div>
    </DndProvider>
  )
}
