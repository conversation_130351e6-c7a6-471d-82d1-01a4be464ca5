// This script approves all properties in the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Connecting to database...');
    
    // Count properties with 'pending' status
    const pendingCount = await prisma.property.count({
      where: { status: 'pending' }
    });
    console.log(`Properties with 'pending' status: ${pendingCount}`);

    // Count properties with 'approved' status
    const approvedCount = await prisma.property.count({
      where: { status: 'approved' }
    });
    console.log(`Properties with 'approved' status: ${approvedCount}`);

    // Count properties with no status
    const noStatusCount = await prisma.property.count({
      where: { status: null }
    });
    console.log(`Properties with no status: ${noStatusCount}`);

    // Update all properties to 'approved' status
    console.log('\nUpdating all properties to approved status...');
    
    // Update properties with 'pending' status
    if (pendingCount > 0) {
      const pendingResult = await prisma.property.updateMany({
        where: { status: 'pending' },
        data: { status: 'approved' }
      });
      console.log(`Updated ${pendingResult.count} pending properties to approved`);
    }

    // Update properties with no status
    if (noStatusCount > 0) {
      const noStatusResult = await prisma.property.updateMany({
        where: { status: null },
        data: { status: 'approved' }
      });
      console.log(`Updated ${noStatusResult.count} properties with no status to approved`);
    }

    // Count properties after update
    const finalApprovedCount = await prisma.property.count({
      where: { status: 'approved' }
    });
    console.log(`\nFinal count of properties with 'approved' status: ${finalApprovedCount}`);

    console.log('\nDone!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
