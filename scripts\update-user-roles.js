// This script updates existing users with a role field
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Setting up admin user...');

    // Create admin users if they don't exist
    const adminEmails = [
      process.env.ADMIN_EMAIL || '<EMAIL>',
      '<EMAIL>'
    ];

    try {
      // Create or update each admin user
      for (const adminEmail of adminEmails) {
        const adminUser = await prisma.user.upsert({
          where: { email: adminEmail },
          update: {
            role: 'admin',
          },
          create: {
            email: adminEmail,
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
          },
        });

        console.log(`Admin user created/updated: ${adminUser.email}`);
      }

      // Update all other users to have the 'user' role
      const users = await prisma.user.findMany({
        where: {
          email: { notIn: adminEmails }
        }
      });

      for (const user of users) {
        await prisma.user.update({
          where: { id: user.id },
          data: { role: 'user' }
        });
      }

      console.log(`Updated ${users.length} regular users with 'user' role`);
      console.log('User role update completed successfully');
    } catch (dbError) {
      console.error('Database connection error - continuing without database updates:', dbError.message);
      console.log('Skipping database operations and continuing with app startup...');
    }
  } catch (error) {
    console.error('Error updating user roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();