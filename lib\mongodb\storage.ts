import { GridFSBucket, ObjectId } from 'mongodb';
import clientPromise from './client';

export async function uploadImage(
  bucketName: string,
  file: Buffer | Blob,
  filename: string,
  contentType?: string
): Promise<{ id: string; url: string }> {
  const client = await clientPromise;
  const db = client.db();
  const bucket = new GridFSBucket(db, { bucketName });
  
  // Convert Blob to Buffer if needed
  let buffer: Buffer;
  if (file instanceof Blob) {
    buffer = Buffer.from(await file.arrayBuffer());
  } else {
    buffer = file;
  }

  return new Promise((resolve, reject) => {
    const uploadStream = bucket.openUploadStream(filename, {
      contentType: contentType || 'image/jpeg',
    });

    const id = uploadStream.id.toString();
    
    // Handle events
    uploadStream.on('error', (error) => reject(error));
    uploadStream.on('finish', () => {
      // Create a URL that can be used to retrieve the file
      const url = `/api/images/${bucketName}/${id}`;
      resolve({ id, url });
    });

    // Write the buffer to the stream
    uploadStream.write(buffer);
    uploadStream.end();
  });
}

export async function deleteImage(bucketName: string, fileId: string): Promise<void> {
  const client = await clientPromise;
  const db = client.db();
  const bucket = new GridFSBucket(db, { bucketName });

  await bucket.delete(new ObjectId(fileId));
}

export async function getImageUrl(bucketName: string, fileId: string): Promise<string> {
  // This function returns a URL that can be used to retrieve the file
  return `/api/images/${bucketName}/${fileId}`;
}

export async function getImage(bucketName: string, fileId: string): Promise<{ stream: NodeJS.ReadableStream; contentType: string | null }> {
  const client = await clientPromise;
  const db = client.db();
  const bucket = new GridFSBucket(db, { bucketName });
  
  // First, retrieve the file metadata
  const file = await db.collection(`${bucketName}.files`).findOne({ _id: new ObjectId(fileId) });
  
  if (!file) {
    throw new Error('File not found');
  }
  
  // Create a readable stream
  const stream = bucket.openDownloadStream(new ObjectId(fileId));
  
  return {
    stream,
    contentType: file.contentType || null
  };
} 