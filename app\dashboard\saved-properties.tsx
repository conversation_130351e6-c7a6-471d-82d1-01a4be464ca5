import { createServerClient } from "@/lib/supabase/server"
import type { PropertyWithImages } from "@/lib/types"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bed, Bath, Home, MapPin, Calendar, Heart } from "lucide-react"

export default async function SavedProperties({ userId }: { userId: string }) {
  const supabase = createServerClient()

  // Fetch user's saved properties with images
  const { data: savedProperties, error } = await supabase
    .from("saved_properties")
    .select(`
      property_id,
      property:properties(
        *,
        images:property_images(*)
      )
    `)
    .eq("user_id", userId)

  // Transform data to match our PropertyWithImages type
  const properties = (savedProperties?.map((item) => item.property) as unknown as PropertyWithImages[]) || []

  if (properties.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-2">No saved properties</h2>
        <p className="text-muted-foreground mb-6">Browse properties and save your favorites.</p>
        <Button asChild>
          <Link href="/properties">Browse Properties</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {properties.map((property) => (
        <PropertyCard key={property.id} property={property} userId={userId} />
      ))}
    </div>
  )
}

function PropertyCard({ property, userId }: { property: PropertyWithImages; userId: string }) {
  const primaryImage = property.images.find((img) => img.is_primary) || property.images[0]

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative h-48">
        <Image
          src={primaryImage?.image_url || "/placeholder.svg?height=400&width=600&text=No+Image"}
          alt={property.title}
          fill
          className="object-cover"
        />
        <Badge className="absolute top-2 right-2">${property.price}/month</Badge>
      </div>
      <CardHeader>
        <CardTitle className="line-clamp-1">{property.title}</CardTitle>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="h-4 w-4 mr-1" />
          {property.city}, {property.province}
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="grid grid-cols-3 gap-2 text-sm">
          <div className="flex items-center">
            <Bed className="h-4 w-4 mr-1" />
            <span>
              {property.bedrooms} {property.bedrooms === 1 ? "Bed" : "Beds"}
            </span>
          </div>
          <div className="flex items-center">
            <Bath className="h-4 w-4 mr-1" />
            <span>
              {property.bathrooms} {property.bathrooms === 1 ? "Bath" : "Baths"}
            </span>
          </div>
          <div className="flex items-center">
            <Home className="h-4 w-4 mr-1" />
            <span>{property.square_feet} ft²</span>
          </div>
        </div>
        <div className="mt-4 flex items-center text-sm text-muted-foreground">
          <Calendar className="h-4 w-4 mr-1" />
          <span>Available {new Date(property.available_from).toLocaleDateString()}</span>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="w-full flex justify-between items-center">
          <RemoveFromSavedButton propertyId={property.id} userId={userId} />
          <Button size="sm" asChild>
            <Link href={`/properties/${property.id}`}>View Details</Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

function RemoveFromSavedButton({ propertyId, userId }: { propertyId: string; userId: string }) {
  return (
    <form
      action={async () => {
        "use server"
        const supabase = createServerClient()
        await supabase.from("saved_properties").delete().match({ user_id: userId, property_id: propertyId })
      }}
    >
      <Button variant="outline" size="sm" type="submit">
        <Heart className="h-4 w-4 mr-2 fill-current" />
        Remove
      </Button>
    </form>
  )
}
