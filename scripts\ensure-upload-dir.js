const fs = require('fs');
const path = require('path');

// Ensure the uploads directory exists
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');

if (!fs.existsSync(uploadsDir)) {
  console.log('Creating uploads directory...');
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Uploads directory created successfully.');
} else {
  console.log('Uploads directory already exists.');
} 