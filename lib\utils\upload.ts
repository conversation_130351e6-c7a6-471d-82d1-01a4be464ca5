import { useAuth } from "@/components/auth-provider"

export async function uploadFile(file: File): Promise<string> {
  const { user } = useAuth()
  
  if (!user) {
    throw new Error("User must be authenticated to upload files")
  }

  const formData = new FormData()
  formData.append("file", file)

  const response = await fetch("/api/upload", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${await user.getIdToken()}`,
    },
    body: formData,
  })

  if (!response.ok) {
    throw new Error("Failed to upload file")
  }

  const data = await response.json()
  return data.fileId
}

export function getFileUrl(fileId: string): string {
  return `/api/files/${fileId}`
} 