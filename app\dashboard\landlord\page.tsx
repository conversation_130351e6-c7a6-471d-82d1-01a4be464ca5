"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import Link from "next/link"
import {
  Building2,
  Users,
  FileCheck,
  TrendingUp,
  ArrowUpRight,
  Calendar,
  DollarSign,
  Plus,
  BarChart,
  ArrowLeft,
  Bell,
  ChevronRight
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

// Sample data
const recentApplications = [
  {
    id: "app-1",
    applicantName: "<PERSON>",
    propertyName: "Modern Downtown Apartment",
    date: "2023-06-10",
    status: "pending"
  },
  {
    id: "app-2",
    applicantName: "<PERSON>",
    propertyName: "Cozy Studio Loft",
    date: "2023-06-08",
    status: "pending"
  },
  {
    id: "app-3",
    applicantName: "<PERSON>",
    propertyName: "Spacious Family Home",
    date: "2023-06-05",
    status: "approved"
  }
];

const upcomingRenewals = [
  {
    id: "lease-1",
    tenantName: "Emily Davis",
    propertyName: "Downtown Loft",
    expiryDate: "2023-07-15",
    monthsLeft: 1
  },
  {
    id: "lease-2",
    tenantName: "Robert Wilson",
    propertyName: "Garden View Apartment",
    expiryDate: "2023-08-01",
    monthsLeft: 2
  }
];

export default function LandlordDashboardPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isLandlord, setIsLandlord] = useState(true) // In a real app, this would be determined by user role

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }

    // In a real application, we would verify the landlord role here
    // if (!user?.isLandlord) {
    //   router.push("/dashboard")
    // }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user || !isLandlord) {
    return null
  }

  // Sample stats for display
  const stats = [
    {
      title: "Properties",
      value: "5",
      change: "+1",
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      link: "/dashboard/landlord/properties"
    },
    {
      title: "Active Tenants",
      value: "8",
      change: "0",
      icon: Users,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
      link: "/dashboard/landlord/tenants"
    },
    {
      title: "Applications",
      value: "3",
      change: "+2",
      icon: FileCheck,
      color: "text-amber-600",
      bgColor: "bg-amber-50",
      link: "/dashboard/applications"
    },
    {
      title: "Occupancy Rate",
      value: "92%",
      change: "+5%",
      icon: TrendingUp,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      link: "/dashboard/landlord/analytics"
    },
  ];

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="mb-6">
        <Button variant="ghost" className="gap-2 mb-4" asChild>
          <Link href="/dashboard">
            <span className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Main Dashboard
            </span>
          </Link>
        </Button>
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          Landlord Dashboard
        </h1>
        <p className="text-muted-foreground">
          Manage your properties, tenants, and rental applications
        </p>
      </div>
      {/* Quick Actions */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-3">
          <Button className="gap-2" asChild>
            <Link href="/dashboard/properties/new">
              <span className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Add New Property
              </span>
            </Link>
          </Button>
          <Button variant="outline" className="gap-2" asChild>
            <Link href="/dashboard/landlord/applications">
              <span className="flex items-center">
                <FileCheck className="h-4 w-4 mr-2" />
                Review Applications
              </span>
            </Link>
          </Button>
          <Button variant="outline" className="gap-2" asChild>
            <Link href="/dashboard/landlord/payments">
              <span className="flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Manage Payments
              </span>
            </Link>
          </Button>
        </div>
      </div>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <Link
            href={stat.link}
            key={stat.title}
            className="block transition-transform hover:-translate-y-1">
            <Card className="border-0 property-card-shadow h-full">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <div className={`${stat.bgColor} p-2 rounded-full`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center mt-1">
                  <span className={`text-xs ${stat.change.startsWith('+') ? 'text-emerald-600' : stat.change.startsWith('-') ? 'text-rose-600' : 'text-gray-600'}`}>
                    {stat.change}
                  </span>
                  <span className="text-xs text-muted-foreground ml-1">
                    since last month
                  </span>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Applications */}
        <div className="lg:col-span-2">
          <Card className="border-0 property-card-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Applications</CardTitle>
                  <CardDescription>Applications that require your attention</CardDescription>
                </div>
                <FileCheck className="h-5 w-5 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant</TableHead>
                    <TableHead>Property</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentApplications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell className="font-medium">{application.applicantName}</TableCell>
                      <TableCell>{application.propertyName}</TableCell>
                      <TableCell>{new Date(application.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`
                            ${application.status === 'pending' ? 'bg-amber-50 text-amber-700 border-amber-200' : ''}
                            ${application.status === 'approved' ? 'bg-emerald-50 text-emerald-700 border-emerald-200' : ''}
                            ${application.status === 'rejected' ? 'bg-rose-50 text-rose-700 border-rose-200' : ''}
                          `}
                        >
                          {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" className="h-8" asChild>
                          <Link href={`/dashboard/landlord/applications/${application.id}`}>
                            Review
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="mt-4">
                <Button asChild variant="ghost" className="w-full">
                  <Link href="/dashboard/landlord/applications">
                    <span className="flex items-center">
                      View All Applications
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Lease Renewals */}
        <div>
          <Card className="border-0 property-card-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Lease Renewals</CardTitle>
                  <CardDescription>Upcoming lease expirations</CardDescription>
                </div>
                <Calendar className="h-5 w-5 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingRenewals.map((renewal) => (
                  <div key={renewal.id} className="flex gap-4 p-3 rounded-lg border bg-muted/30">
                    <div className={`bg-amber-50 h-12 w-12 rounded-full flex items-center justify-center flex-shrink-0 ${
                      renewal.monthsLeft <= 1 ? 'bg-rose-50' : 'bg-amber-50'
                    }`}>
                      <Calendar className={`h-5 w-5 ${
                        renewal.monthsLeft <= 1 ? 'text-rose-500' : 'text-amber-500'
                      }`} />
                    </div>
                    <div>
                      <h3 className="font-medium">{renewal.tenantName}</h3>
                      <p className="text-sm text-muted-foreground">{renewal.propertyName}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={renewal.monthsLeft <= 1 ? "destructive" : "outline"} className="text-xs">
                          Expires {new Date(renewal.expiryDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}

                <Button variant="outline" className="w-full mt-2" asChild>
                  <Link href="/dashboard/landlord/leases">
                    Manage All Leases
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Financial Summary */}
          <Card className="border-0 property-card-shadow mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Financial Summary</CardTitle>
                <BarChart className="h-5 w-5 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Monthly Revenue</span>
                  <span className="font-medium">$12,500</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Expenses</span>
                  <span className="font-medium">$3,200</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Net Income</span>
                  <span className="font-bold">$9,300</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">YTD Income</span>
                  <span className="font-medium">$56,700</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <Button variant="ghost" size="sm" className="w-full" asChild>
                <Link href="/dashboard/landlord/finances" legacyBehavior>
                  View Financial Report
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}