import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { useSession } from 'next-auth/react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface Conversation {
  id: string;
  userA: {
    id: string;
    name: string;
    image: string;
  };
  userB: {
    id: string;
    name: string;
    image: string;
  };
  lastMessage?: {
    content: {
      type: string;
      text?: string;
    };
    createdAt: string;
    senderId: string;
  };
  unreadCount: number;
  updatedAt: string;
}

interface ConversationListProps {
  conversations: Conversation[];
  currentUserId: string;
  isLoading?: boolean;
}

export function ConversationList({ conversations, currentUserId, isLoading = false }: ConversationListProps) {
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);

  // Set active conversation based on URL
  useEffect(() => {
    const path = window.location.pathname;
    const match = path.match(/\/messages\/([^\/]+)/);
    if (match && match[1]) {
      setActiveConversationId(match[1]);
    }
  }, []);

  const getOtherUser = (conversation: Conversation) => {
    return conversation.userA.id === currentUserId ? conversation.userB : conversation.userA;
  };

  const getLastMessagePreview = (conversation: Conversation) => {
    if (!conversation.lastMessage) return 'No messages yet';

    const { content, senderId } = conversation.lastMessage;
    const isCurrentUser = senderId === currentUserId;
    const prefix = isCurrentUser ? 'You: ' : '';

    switch (content.type) {
      case 'text':
        return `${prefix}${content.text}`;
      case 'image':
        return `${prefix}Sent an image`;
      case 'property':
        return `${prefix}Shared a property`;
      case 'location':
        return `${prefix}Shared a location`;
      default:
        return `${prefix}Sent a message`;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4 p-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[150px]" />
              <Skeleton className="h-3 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <p className="text-sm text-gray-500">No conversations yet.</p>
        <p className="text-xs text-gray-400 mt-1">
          Start a conversation with another user to see it here.
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {conversations.map((conversation) => {
        const otherUser = getOtherUser(conversation);
        const isActive = conversation.id === activeConversationId;

        return (
          <Link
            key={conversation.id}
            href={`/messages/${conversation.id}`}
            className={`block p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
              isActive ? 'bg-gray-100 dark:bg-gray-800' : ''
            }`}>
            <div className="flex items-center space-x-4">
              <div className="relative h-12 w-12 rounded-full overflow-hidden">
                {otherUser.image ? (
                  <Image
                    src={otherUser.image}
                    alt={otherUser.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full bg-gray-200 dark:bg-gray-700 text-gray-500">
                    {otherUser.name.charAt(0)}
                  </div>
                )}
                {conversation.unreadCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full flex items-center justify-center p-0">
                    {conversation.unreadCount}
                  </Badge>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-start">
                  <h3 className="text-sm font-medium truncate">{otherUser.name}</h3>
                  {conversation.lastMessage && (
                    <span className="text-xs text-gray-500">
                      {(() => {
                        const date = new Date(conversation.lastMessage.createdAt);
                        const now = new Date();
                        const diffInDays = Math.floor(
                          (now.setHours(0, 0, 0, 0) - new Date(date).setHours(0, 0, 0, 0)) / (1000 * 60 * 60 * 24)
                        );

                        if (diffInDays === 0) {
                          const hours = date.getHours();
                          const minutes = date.getMinutes();
                          const ampm = hours >= 12 ? 'PM' : 'AM';
                          const formattedHours = hours % 12 || 12;
                          const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
                          return `${formattedHours}:${formattedMinutes} ${ampm}`;
                        } else if (diffInDays < 7) {
                          const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                          return days[date.getDay()];
                        } else {
                          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                          return `${months[date.getMonth()]} ${date.getDate()}`;
                        }
                      })()}
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500 truncate">
                  {getLastMessagePreview(conversation)}
                </p>
              </div>
            </div>
          </Link>
        );
      })}
    </div>
  );
}