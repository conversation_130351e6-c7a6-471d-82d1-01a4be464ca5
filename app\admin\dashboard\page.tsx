"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Building, Users, FileCheck, AlertTriangle, Clock, User } from "lucide-react"
import Link from "next/link"
import { useSession } from "next-auth/react"

export default function AdminDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true)
      try {
        // Fetch stats from your API
        const response = await fetch('/api/admin/stats')
        const data = await response.json()
        setStats(data)
      } catch (error) {
        console.error("Error fetching stats:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (session?.user) {
      fetchStats()
    }
  }, [session])

  if (isLoading) {
    return (
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-4 w-96 mb-8" />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-[120px] w-full rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 py-8 md:px-6 md:py-12">
      <div className="mb-8 space-y-4">
        <h1 className="text-3xl font-bold tracking-tight md:text-4xl">Admin Dashboard</h1>
        <p className="text-muted-foreground">Manage properties, users, and approvals</p>
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.usersCount || 0}</div>
            <p className="text-xs text-muted-foreground">Registered users</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.propertiesCount || 0}</div>
            <p className="text-xs text-muted-foreground">Listed properties</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.pendingApprovalsCount || 0}</div>
            <p className="text-xs text-muted-foreground">Requires your attention</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Reported Content</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">Needs review</p>
          </CardContent>
        </Card>
      </div>
      <Tabs defaultValue="recent" className="mt-8">
        <TabsList className="mb-6">
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
          <TabsTrigger value="approvals">Pending Approvals</TabsTrigger>
          <TabsTrigger value="reports">Content Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.recentProperties && stats.recentProperties.length > 0 ? (
                  stats.recentProperties.map((property: any, i: number) => (
                    <div key={property.id} className="flex items-center gap-4 border-b pb-4 last:border-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                        <Building className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">New property listing submitted</p>
                        <p className="text-sm text-muted-foreground">
                          {property.title} by {property.users?.first_name} {property.users?.last_name}
                        </p>
                      </div>
                      <div className="text-right text-sm text-muted-foreground">
                        {new Date(property.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Clock className="mb-2 h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No recent activity to display</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approvals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Approvals</CardTitle>
            </CardHeader>
            <CardContent>
              {stats?.pendingApprovalsCount > 0 ? (
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    There are {stats.pendingApprovalsCount} properties waiting for your approval.
                  </p>
                  <Button asChild>
                    <Link href="/admin/approvals">View Pending Approvals</Link>
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <FileCheck className="mb-2 h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground">No pending approvals</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertTriangle className="mb-2 h-8 w-8 text-muted-foreground" />
                <p className="text-muted-foreground">No reported content</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      <div className="mt-8 grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2">
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/approvals" legacyBehavior>
                  <div className="flex items-center">
                    <FileCheck className="mr-2 h-4 w-4" />
                    Review Approvals
                  </div>
                </Link>
              </Button>
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/users" legacyBehavior>
                  <div className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    Manage Users
                  </div>
                </Link>
              </Button>
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/properties" legacyBehavior>
                  <div className="flex items-center">
                    <Building className="mr-2 h-4 w-4" />
                    View Properties
                  </div>
                </Link>
              </Button>
              <Button asChild variant="outline" className="justify-start">
                <Link href="/" legacyBehavior>
                  <div className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    View Public Site
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Admin Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium">{session?.user?.name || "Admin User"}</p>
                  <p className="text-sm text-muted-foreground">{session?.user?.email || "<EMAIL>"}</p>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                You have full administrative access to the Rent Central platform.
              </p>
              <Button asChild variant="outline" className="w-full">
                <Link href="/admin/settings">Admin Settings</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
