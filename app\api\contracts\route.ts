import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { connectToDatabase } from "@/lib/mongodb"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { db } = await connectToDatabase()
    const contracts = await db
      .collection("contracts")
      .find({ userId: session.user.id })
      .sort({ createdAt: -1 })
      .toArray()

    return NextResponse.json(contracts)
  } catch (error) {
    console.error("Error fetching contracts:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("file") as File
    const propertyId = formData.get("propertyId") as string
    const contractType = formData.get("contractType") as string

    if (!file || !propertyId || !contractType) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    const { db } = await connectToDatabase()
    const result = await db.collection("contracts").insertOne({
      userId: session.user.id,
      propertyId,
      contractType,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      status: "active",
      createdAt: new Date(),
    })

    return NextResponse.json({ success: true, id: result.insertedId })
  } catch (error) {
    console.error("Error creating contract:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
} 