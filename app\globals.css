@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 196 80% 40%; /* Main brand blue */
    --primary-foreground: 0 0% 98%;
    --secondary: 200 30% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;
    --accent: 196 80% 97%; /* Light version of primary */
    --accent-foreground: 196 80% 30%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 196 80% 40%;
    --radius: 0.75rem;
    
    --font-inter: "Inter", system-ui, sans-serif;
    --font-montserrat: "Montserrat", system-ui, sans-serif;
    --font-jakarta: "Plus Jakarta Sans", system-ui, sans-serif;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 196 80% 40%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 196 80% 25%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 196 80% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-jakarta);
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-montserrat);
    @apply font-bold;
  }

  /* Modern Scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer utilities {
  .animated-gradient {
    background-size: 200% 200%;
    animation: gradient-animation 6s ease infinite;
  }

  @keyframes gradient-animation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .text-balance {
    text-wrap: balance;
  }
  
  .bg-mesh {
    background-image: 
      radial-gradient(at 40% 20%, hsla(var(--primary) / 0.1) 0px, transparent 50%),
      radial-gradient(at 80% 50%, hsla(var(--accent) / 0.1) 0px, transparent 50%),
      radial-gradient(at 10% 80%, hsla(var(--primary) / 0.1) 0px, transparent 50%);
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-400 { animation-delay: 400ms; }
.animate-delay-500 { animation-delay: 500ms; }

/* Glass effect */
.glass {
  @apply bg-background/60 backdrop-blur-md border border-border/40;
}

.glass-card {
  @apply bg-card/70 backdrop-blur-sm border border-border/30 shadow-sm;
}
