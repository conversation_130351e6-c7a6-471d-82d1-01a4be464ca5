import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { writeFile } from 'fs/promises';
import { join } from 'path';

// Interface for file data
interface FileData {
  id: string;
  name: string;
  type: string;
  data: string;
  createdAt: Date;
}

// Create a global storage (will persist between requests in development)
declare global {
  var fileStorage: Map<string, FileData> | undefined;
}

// Initialize the storage
const fileStorage = global.fileStorage || new Map<string, FileData>();
if (!global.fileStorage) {
  global.fileStorage = fileStorage;
}

export async function POST(req: NextRequest) {
  try {
    // Authentication is now handled by middleware
    
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      );
    }
    
    // Generate a unique filename
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Create a unique filename
    const uniqueId = uuidv4();
    const extension = file.name.split('.').pop();
    const filename = `${uniqueId}.${extension}`;
    
    // Save to public/uploads directory
    const uploadDir = join(process.cwd(), 'public', 'uploads');
    const filePath = join(uploadDir, filename);
    
    await writeFile(filePath, buffer);
    
    // Return the URL
    const url = `/uploads/${filename}`;
    
    return NextResponse.json({ url });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
} 