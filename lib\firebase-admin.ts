import { initializeApp, getApps, cert } from "firebase-admin/app"
import { getAuth } from "firebase-admin/auth"
import { getFirestore } from "firebase-admin/firestore"

const firebaseAdminConfig = {
  credential: cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n")
  })
}

export const adminApp = getApps().length === 0 ? initializeApp(firebaseAdminConfig) : getApps()[0]
export const adminAuth = getAuth(adminApp)
export const auth = adminAuth

export const db = getFirestore(adminApp);
export default adminApp; 