'use client';

import { AuthProvider } from '@/components/auth-provider';
import { Toaster } from '@/components/ui/toaster';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthProvider>
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
          {children}
        </div>
      </div>
      <Toaster />
    </AuthProvider>
  );
} 