import { NextResponse } from "next/server"
import { auth } from "@/lib/firebase"
import { updateProfile } from "firebase/auth"

export async function GET() {
  try {
    const user = auth.currentUser
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return NextResponse.json({
      id: user.uid,
      name: user.displayName,
      email: user.email,
      image: user.photoURL,
    })
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const user = auth.currentUser
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const data = await request.json()
    await updateProfile(user, {
      displayName: data.name,
      photoURL: data.image,
    })

    return NextResponse.json({ message: "Profile updated successfully" })
  } catch (error) {
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
} 