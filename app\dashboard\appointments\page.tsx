'use client';

import React, { useState, useEffect } from 'react';
import { AppointmentCard } from '@/components/appointments/appointment-card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Plus, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';

interface Appointment {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  propertyId: string;
  propertyTitle?: string;
  propertyAddress?: string;
  googleCalendarLink?: string;
}

export default function AppointmentsPage() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/appointments');
        
        if (!response.ok) {
          throw new Error('Failed to fetch appointments');
        }
        
        const data = await response.json();
        
        // For this demo, we'll add property details
        // In a real app, these would come from the API
        const appointmentsWithDetails = data.map((appointment: any) => ({
          ...appointment,
          propertyTitle: 'Sample Property',
          propertyAddress: '123 Main St, City, State',
        }));
        
        setAppointments(appointmentsWithDetails);
      } catch (error) {
        console.error('Error fetching appointments:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchAppointments();
  }, []);

  const handleCancelAppointment = async (id: string) => {
    try {
      const response = await fetch(`/api/appointments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'cancelled' }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel appointment');
      }

      // Update the local state
      setAppointments(prevAppointments => 
        prevAppointments.map(appointment => 
          appointment.id === id 
            ? { ...appointment, status: 'cancelled' as const } 
            : appointment
        )
      );

      toast({
        title: 'Appointment Cancelled',
        description: 'The appointment has been cancelled successfully.',
      });
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel appointment',
        variant: 'destructive',
      });
    }
  };

  const handleCompleteAppointment = async (id: string) => {
    try {
      const response = await fetch(`/api/appointments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'completed' }),
      });

      if (!response.ok) {
        throw new Error('Failed to complete appointment');
      }

      // Update the local state
      setAppointments(prevAppointments => 
        prevAppointments.map(appointment => 
          appointment.id === id 
            ? { ...appointment, status: 'completed' as const } 
            : appointment
        )
      );

      toast({
        title: 'Appointment Completed',
        description: 'The appointment has been marked as completed.',
      });
    } catch (error) {
      console.error('Error completing appointment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to complete appointment',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">My Appointments</h1>
        <Link href="/dashboard/appointments/new" legacyBehavior>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Appointment
          </Button>
        </Link>
      </div>
      {loading ? (
        <div className="flex justify-center items-center h-48">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading appointments...</span>
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : appointments.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">No appointments found</h2>
          <p className="text-muted-foreground mb-6">
            You don't have any appointments scheduled yet.
          </p>
          <Link href="/dashboard/appointments/new" legacyBehavior>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule an Appointment
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {appointments.map((appointment) => (
            <AppointmentCard
              key={appointment.id}
              appointment={appointment}
              onCancel={handleCancelAppointment}
              onComplete={handleCompleteAppointment}
            />
          ))}
        </div>
      )}
    </div>
  );
} 