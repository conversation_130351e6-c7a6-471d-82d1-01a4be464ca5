import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface Property {
  id: string
  title: string
  description: string
  address: string
  city: string
  province: string
  postalCode: string
  propertyType: string
  bedrooms: number
  bathrooms: number
  squareFeet: number
  price: number
  availableFrom: string
  status: string
  images: {
    id: string
    imageUrl: string
    isPrimary: boolean
  }[]
  amenities: {
    amenity: {
      id: string
      name: string
      icon: string | null
    }
  }[]
  landlord: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
}

interface PropertyFilters {
  city?: string
  propertyType?: string
  minPrice?: number
  maxPrice?: number
}

export function useProperties(filters?: PropertyFilters) {
  const { data: session } = useSession()
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true

    async function fetchProperties() {
      try {
        const queryParams = new URLSearchParams()
        if (filters?.city) queryParams.append('city', filters.city)
        if (filters?.propertyType) queryParams.append('propertyType', filters.propertyType)
        if (filters?.minPrice) queryParams.append('minPrice', filters.minPrice.toString())
        if (filters?.maxPrice) queryParams.append('maxPrice', filters.maxPrice.toString())

        const response = await fetch(`/api/properties?${queryParams.toString()}`)
        if (!response.ok) throw new Error('Failed to fetch properties')
        const data = await response.json()
        if (mounted) {
          setProperties(data)
          setLoading(false)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to fetch properties')
          setLoading(false)
        }
      }
    }

    fetchProperties()
  }, [filters])

  const createProperty = async (propertyData: Omit<Property, 'id' | 'landlord' | 'status'>) => {
    try {
      const response = await fetch('/api/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(propertyData),
      })

      if (!response.ok) throw new Error('Failed to create property')
      const newProperty = await response.json()
      setProperties((prev) => [...prev, newProperty])
      return newProperty
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create property')
      throw err
    }
  }

  return {
    properties,
    loading,
    error,
    createProperty,
  }
} 