import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb/client";
import { auth } from "@/lib/firebase-admin";
import { ObjectId } from "mongodb";

// GET all saved properties for the current user
export async function GET(request: NextRequest) {
  try {
    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const savedPropertiesCollection = db.collection('saved_properties');
    const propertiesCollection = db.collection('properties');

    // Get all saved property IDs for the user
    const savedProperties = await savedPropertiesCollection
      .find({ user_id: userId })
      .toArray();

    // Get all saved property details
    const propertyIds = savedProperties.map(sp => new ObjectId(sp.property_id));
    
    // If no saved properties, return empty array
    if (propertyIds.length === 0) {
      return NextResponse.json([]);
    }
    
    // Get all property details
    const properties = await propertiesCollection
      .find({ _id: { $in: propertyIds } })
      .toArray();

    // Add saved_at date to each property
    const propertiesWithSavedDate = properties.map(property => {
      const savedProperty = savedProperties.find(sp => 
        sp.property_id === property._id.toString()
      );
      return {
        ...property,
        saved_at: savedProperty?.created_at || new Date()
      };
    });

    return NextResponse.json(propertiesWithSavedDate);
  } catch (error) {
    console.error("Error fetching saved properties:", error);
    return NextResponse.json({ error: "Failed to fetch saved properties" }, { status: 500 });
  }
}

// POST save a property
export async function POST(request: NextRequest) {
  try {
    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { property_id } = body;

    if (!property_id) {
      return NextResponse.json({ error: "Property ID is required" }, { status: 400 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const savedPropertiesCollection = db.collection('saved_properties');
    const propertiesCollection = db.collection('properties');

    // Check if property exists
    const property = await propertiesCollection.findOne({
      _id: new ObjectId(property_id)
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if property is already saved
    const existingSave = await savedPropertiesCollection.findOne({
      user_id: userId,
      property_id
    });

    if (existingSave) {
      return NextResponse.json({ error: "Property already saved" }, { status: 409 });
    }

    // Save property
    const savedProperty = {
      user_id: userId,
      property_id,
      created_at: new Date()
    };

    const result = await savedPropertiesCollection.insertOne(savedProperty);

    return NextResponse.json({
      _id: result.insertedId,
      ...savedProperty
    });
  } catch (error) {
    console.error("Error saving property:", error);
    return NextResponse.json({ error: "Failed to save property" }, { status: 500 });
  }
}

// DELETE remove a saved property
export async function DELETE(request: NextRequest) {
  try {
    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get property_id from query parameters
    const property_id = request.nextUrl.searchParams.get('property_id');

    if (!property_id) {
      return NextResponse.json({ error: "Property ID is required" }, { status: 400 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const savedPropertiesCollection = db.collection('saved_properties');

    // Remove saved property
    const result = await savedPropertiesCollection.deleteOne({
      user_id: userId,
      property_id
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: "Saved property not found" }, { status: 404 });
    }

    return NextResponse.json({ message: "Property removed from saved list" });
  } catch (error) {
    console.error("Error removing saved property:", error);
    return NextResponse.json({ error: "Failed to remove saved property" }, { status: 500 });
  }
} 