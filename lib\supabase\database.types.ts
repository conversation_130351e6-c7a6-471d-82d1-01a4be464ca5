export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          auth_id: string | null
          first_name: string
          last_name: string
          email: string
          phone: string | null
          postal_code: string | null
          city: string | null
          bio: string | null
          is_landlord: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          auth_id?: string | null
          first_name: string
          last_name: string
          email: string
          phone?: string | null
          postal_code?: string | null
          city?: string | null
          bio?: string | null
          is_landlord?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          auth_id?: string | null
          first_name?: string
          last_name?: string
          email?: string
          phone?: string | null
          postal_code?: string | null
          city?: string | null
          bio?: string | null
          is_landlord?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      properties: {
        Row: {
          id: string
          title: string
          description: string
          address: string
          city: string
          province: string
          postal_code: string
          property_type: string
          bedrooms: number
          bathrooms: number
          square_feet: number
          price: number
          available_from: string
          landlord_id: string
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          address: string
          city: string
          province: string
          postal_code: string
          property_type: string
          bedrooms: number
          bathrooms: number
          square_feet: number
          price: number
          available_from: string
          landlord_id: string
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          address?: string
          city?: string
          province?: string
          postal_code?: string
          property_type?: string
          bedrooms?: number
          bathrooms?: number
          square_feet?: number
          price?: number
          available_from?: string
          landlord_id?: string
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      property_images: {
        Row: {
          id: string
          property_id: string
          image_url: string
          is_primary: boolean
          created_at: string
        }
        Insert: {
          id?: string
          property_id: string
          image_url: string
          is_primary?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          image_url?: string
          is_primary?: boolean
          created_at?: string
        }
      }
      amenities: {
        Row: {
          id: string
          name: string
          icon: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          icon?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          icon?: string | null
          created_at?: string
        }
      }
      property_amenities: {
        Row: {
          property_id: string
          amenity_id: string
        }
        Insert: {
          property_id: string
          amenity_id: string
        }
        Update: {
          property_id?: string
          amenity_id?: string
        }
      }
      applications: {
        Row: {
          id: string
          property_id: string
          user_id: string
          status: string
          message: string | null
          applied_date: string
          updated_at: string
        }
        Insert: {
          id?: string
          property_id: string
          user_id: string
          status?: string
          message?: string | null
          applied_date?: string
          updated_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          user_id?: string
          status?: string
          message?: string | null
          applied_date?: string
          updated_at?: string
        }
      }
      approvals: {
        Row: {
          id: string
          property_id: string
          admin_id: string | null
          status: string
          rejection_reason: string | null
          approved_at: string | null
          rejected_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          property_id: string
          admin_id?: string | null
          status: string
          rejection_reason?: string | null
          approved_at?: string | null
          rejected_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          admin_id?: string | null
          status?: string
          rejection_reason?: string | null
          approved_at?: string | null
          rejected_at?: string | null
          created_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          application_id: string
          amount: number
          status: string
          payment_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          application_id: string
          amount: number
          status?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          application_id?: string
          amount?: number
          status?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          role: string
          created_at: string
        }
        Insert: {
          id: string
          role?: string
          created_at?: string
        }
        Update: {
          id?: string
          role?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
