// This script checks for properties with 'approved' status
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function main() {
  try {
    // Connect directly to MongoDB
    const uri = process.env.MONGODB_URI;
    if (!uri) {
      throw new Error('MONGODB_URI is not defined');
    }

    console.log('Connecting to MongoDB...');
    const client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Count properties with 'approved' status
    const approvedCount = await propertiesCollection.countDocuments({ status: 'approved' });
    console.log(`Properties with 'approved' status: ${approvedCount}`);

    // Count properties with 'pending' status
    const pendingCount = await propertiesCollection.countDocuments({ status: 'pending' });
    console.log(`Properties with 'pending' status: ${pendingCount}`);

    // Count properties with no status
    const noStatusCount = await propertiesCollection.countDocuments({ status: { $exists: false } });
    console.log(`Properties with no status: ${noStatusCount}`);

    // Get sample approved properties
    const approvedProperties = await propertiesCollection.find({ status: 'approved' }).limit(3).toArray();
    
    console.log('\nSample approved properties:');
    if (approvedProperties.length > 0) {
      approvedProperties.forEach(property => {
        console.log({
          id: property._id,
          title: property.title,
          city: property.city,
          province: property.province,
          price: property.price,
          status: property.status
        });
      });
    } else {
      console.log('No approved properties found');
    }

    // Get sample pending properties
    const pendingProperties = await propertiesCollection.find({ status: 'pending' }).limit(3).toArray();
    
    console.log('\nSample pending properties:');
    if (pendingProperties.length > 0) {
      pendingProperties.forEach(property => {
        console.log({
          id: property._id,
          title: property.title,
          city: property.city,
          province: property.province,
          price: property.price,
          status: property.status
        });
      });
    } else {
      console.log('No pending properties found');
    }

    // Update properties with no status to 'approved'
    if (noStatusCount > 0) {
      console.log('\nUpdating properties with no status to approved...');
      const updateResult = await propertiesCollection.updateMany(
        { status: { $exists: false } },
        { $set: { status: 'approved' } }
      );
      console.log(`Updated ${updateResult.modifiedCount} properties`);
    }

    await client.close();
    console.log('\nDisconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
