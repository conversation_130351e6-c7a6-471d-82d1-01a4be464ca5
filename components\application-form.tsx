"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/components/auth-provider"
import { uploadFile } from "@/lib/utils/upload"
import { Loader2, Upload, CheckCircle } from "lucide-react"

// Define the form schema with Zod
const applicationFormSchema = z.object({
  propertyId: z.string(),
  income: z.coerce.number().min(1, {
    message: "Income must be a positive number.",
  }),
  employmentStatus: z.string().min(1, {
    message: "Employment status is required.",
  }),
  references: z.array(z.object({
    name: z.string(),
    relationship: z.string(),
    contact: z.string(),
  })),
  documents: z.array(z.string()).min(1, {
    message: "At least one document is required.",
  }),
})

type ApplicationFormValues = z.infer<typeof applicationFormSchema>

interface ApplicationFormProps {
  propertyId: string
  onSuccess?: (confirmationNumber: string) => void
}

export function ApplicationForm({ propertyId, onSuccess }: ApplicationFormProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([])
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [confirmationNumber, setConfirmationNumber] = useState("")

  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      propertyId,
      income: 0,
      employmentStatus: "",
      references: [{ name: "", relationship: "", contact: "" }],
      documents: [],
    },
  })

  const handleFileUpload = async (file: File) => {
    try {
      setIsUploading(true)
      const fileId = await uploadFile(file)
      setUploadedFiles(prev => [...prev, fileId])
      form.setValue("documents", [...form.getValues("documents"), fileId])
      toast({
        title: "File uploaded successfully",
        description: "Your document has been uploaded.",
      })
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  async function onSubmit(data: ApplicationFormValues) {
    try {
      const response = await fetch("/api/applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${await user?.getIdToken()}`,
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error("Failed to submit application")
      }

      const result = await response.json()
      setConfirmationNumber(result.confirmationNumber)
      setShowConfirmation(true)
      
      if (onSuccess) {
        onSuccess(result.confirmationNumber)
      }

      toast({
        title: "Application submitted",
        description: `Your application has been submitted. Confirmation number: ${result.confirmationNumber}`,
      })
    } catch (error) {
      toast({
        title: "Submission failed",
        description: "There was an error submitting your application.",
        variant: "destructive",
      })
    }
  }

  if (showConfirmation) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            Application Submitted
          </CardTitle>
          <CardDescription>
            Thank you for submitting your application.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">Confirmation Number</h3>
              <p className="text-2xl font-bold text-primary">{confirmationNumber}</p>
            </div>
            <p className="text-sm text-muted-foreground">
              Please save this confirmation number for future reference. You can use it to track the status of your application.
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={() => router.push("/dashboard/applications")}>
            View Applications
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Application Form</CardTitle>
            <CardDescription>
              Please fill out the application form completely.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="income"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Monthly Income</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="employmentStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employment Status</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormLabel>References</FormLabel>
              {form.watch("references").map((_, index) => (
                <div key={index} className="space-y-4 p-4 border rounded-lg">
                  <FormField
                    control={form.control}
                    name={`references.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`references.${index}.relationship`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Relationship</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`references.${index}.contact`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Information</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  form.setValue("references", [
                    ...form.getValues("references"),
                    { name: "", relationship: "", contact: "" },
                  ])
                }}
              >
                Add Reference
              </Button>
            </div>

            <div className="space-y-4">
              <FormLabel>Documents</FormLabel>
              <div className="border-2 border-dashed rounded-lg p-6 text-center">
                <input
                  type="file"
                  id="documents"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      handleFileUpload(file)
                    }
                  }}
                />
                <label
                  htmlFor="documents"
                  className="cursor-pointer flex flex-col items-center gap-2"
                >
                  {isUploading ? (
                    <Loader2 className="h-8 w-8 animate-spin" />
                  ) : (
                    <Upload className="h-8 w-8" />
                  )}
                  <span className="text-sm text-muted-foreground">
                    {isUploading
                      ? "Uploading..."
                      : "Click to upload documents"}
                  </span>
                </label>
              </div>
              {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Uploaded Documents:</p>
                  <ul className="text-sm text-muted-foreground">
                    {uploadedFiles.map((fileId, index) => (
                      <li key={index}>Document {index + 1}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isUploading}>
              Submit Application
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  )
} 