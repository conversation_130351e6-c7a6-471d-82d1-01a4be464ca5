"use server"

import { createServerSupabaseClient } from "@/lib/auth"
import { revalidatePath } from "next/cache"
import { requireAdmin } from "@/lib/auth"

export async function getAllUsers() {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    const { data: users, error } = await supabase.from("users").select("*").order("created_at", { ascending: false })

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true, users }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function updateUser(userId: string, userData: any) {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    const { error } = await supabase.from("users").update(userData).eq("id", userId)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/users")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function deleteUser(userId: string) {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    // Get the auth_id for the user
    const { data: user, error: userError } = await supabase.from("users").select("auth_id").eq("id", userId).single()

    if (userError || !user) {
      return { success: false, error: userError?.message || "User not found" }
    }

    // Delete the user from the auth.users table
    // Note: This will cascade delete the user from the users table due to the foreign key constraint
    const { error } = await supabase.auth.admin.deleteUser(user.auth_id)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/users")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function promoteToAdmin(userId: string) {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    const { error } = await supabase.from("users").update({ is_admin: true }).eq("id", userId)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/users")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function suspendUser(userId: string) {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    const { error } = await supabase.from("users").update({ status: "suspended" }).eq("id", userId)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/users")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function activateUser(userId: string) {
  try {
    await requireAdmin()

    const supabase = await createServerSupabaseClient()

    const { error } = await supabase.from("users").update({ status: "active" }).eq("id", userId)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/users")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function getDashboardStats() {
  try {
    const supabase = await createServerSupabaseClient()

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return { success: false, error: "Not authenticated" }
    }

    const { data: user, error: userError } = await supabase
      .from("users")
      .select("*")
      .eq("auth_id", session.user.id)
      .single()

    if (userError || !user) {
      return { success: false, error: userError?.message || "User not found" }
    }

    // Admin stats
    if (user.is_admin) {
      const { data: totalUsers, error: usersError } = await supabase.from("users").select("id", { count: "exact" })

      const { data: newUsers, error: newUsersError } = await supabase
        .from("users")
        .select("id", { count: "exact" })
        .gte("created_at", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())

      const { data: totalProperties, error: propertiesError } = await supabase
        .from("properties")
        .select("id", { count: "exact" })

      const { data: newProperties, error: newPropertiesError } = await supabase
        .from("properties")
        .select("id", { count: "exact" })
        .gte("created_at", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())

      const { data: pendingApprovals, error: approvalsError } = await supabase
        .from("properties")
        .select("id", { count: "exact" })
        .eq("status", "pending")

      const { data: recentActivity, error: activityError } = await supabase
        .from("properties")
        .select("id, title, status, created_at, landlord:users(first_name, last_name)")
        .order("created_at", { ascending: false })
        .limit(5)

      return {
        success: true,
        stats: {
          totalUsers: totalUsers?.length || 0,
          newUsersThisMonth: newUsers?.length || 0,
          totalProperties: totalProperties?.length || 0,
          newPropertiesThisMonth: newProperties?.length || 0,
          pendingApprovals: pendingApprovals?.length || 0,
          recentActivity:
            recentActivity?.map((item) => ({
              type: "property",
              title: `New property listing submitted`,
              description: `${item.title} by ${item.landlord.first_name} ${item.landlord.last_name}`,
              time: new Date(item.created_at).toLocaleDateString(),
            })) || [],
        },
      }
    }

    // Landlord stats
    if (user.is_landlord) {
      const { data: properties, error: propertiesError } = await supabase
        .from("properties")
        .select("id, status", { count: "exact" })
        .eq("landlord_id", user.id)

      const occupiedProperties = properties?.filter((p) => p.status === "approved").length || 0
      const vacantProperties = properties?.filter((p) => p.status === "pending").length || 0

      const { data: applications, error: applicationsError } = await supabase
        .from("applications")
        .select("id, status, property:properties(id, title)", { count: "exact" })
        .in("property.landlord_id", [user.id])

      const pendingApplications = applications?.filter((a) => a.status === "pending").length || 0
      const approvedApplications = applications?.filter((a) => a.status === "approved").length || 0
      const rejectedApplications = applications?.filter((a) => a.status === "rejected").length || 0

      return {
        success: true,
        stats: {
          totalProperties: properties?.length || 0,
          occupiedProperties,
          vacantProperties,
          totalApplications: applications?.length || 0,
          pendingApplications,
          approvedApplications,
          rejectedApplications,
        },
      }
    }

    // Regular user stats
    const { data: applications, error: applicationsError } = await supabase
      .from("applications")
      .select("id, status", { count: "exact" })
      .eq("user_id", user.id)

    const pendingApplications = applications?.filter((a) => a.status === "pending").length || 0
    const approvedApplications = applications?.filter((a) => a.status === "approved").length || 0
    const rejectedApplications = applications?.filter((a) => a.status === "rejected").length || 0

    const { data: savedProperties, error: savedError } = await supabase
      .from("saved_properties")
      .select("id", { count: "exact" })
      .eq("user_id", user.id)

    return {
      success: true,
      stats: {
        totalApplications: applications?.length || 0,
        pendingApplications,
        approvedApplications,
        rejectedApplications,
        savedProperties: savedProperties?.length || 0,
      },
    }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}
