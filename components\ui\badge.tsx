import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-sm",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 hover:shadow-md hover:shadow-primary/10",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-md",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 hover:shadow-md hover:shadow-destructive/10",
        outline: "text-foreground hover:bg-muted/50",
        success: 
          "border-transparent bg-green-600 text-white hover:bg-green-700 hover:shadow-md hover:shadow-green-600/10",
        warning: 
          "border-transparent bg-yellow-500 text-white hover:bg-yellow-600 hover:shadow-md hover:shadow-yellow-500/10",
        info: 
          "border-transparent bg-blue-500 text-white hover:bg-blue-600 hover:shadow-md hover:shadow-blue-500/10",
        gradient: 
          "border-transparent bg-gradient-to-r from-primary to-accent text-white hover:opacity-90 hover:shadow-md",
      },
      size: {
        default: "h-6 px-2.5 py-0.5 text-xs",
        sm: "h-5 px-2 py-0 text-[10px]",
        lg: "h-7 px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
