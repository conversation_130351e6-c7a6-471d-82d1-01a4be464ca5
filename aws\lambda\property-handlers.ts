import type { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda"
import type { Pool } from "pg"
import { v4 as uuidv4 } from "uuid"

// Get all properties with filters
export async function getProperties(event: APIGatewayProxyEvent, pool: Pool): Promise<APIGatewayProxyResult> {
  const queryParams = event.queryStringParameters || {}

  // Build query based on filters
  let query = `
    SELECT p.*, 
           ARRAY_AGG(DISTINCT pi.image_url) AS images,
           ARRAY_AGG(DISTINCT a.name) AS amenities
    FROM properties p
    LEFT JOIN property_images pi ON p.id = pi.property_id
    LEFT JOIN property_amenities pa ON p.id = pa.property_id
    LEFT JOIN amenities a ON pa.amenity_id = a.id
    WHERE p.status = 'approved'
  `

  const values: any[] = []
  let paramIndex = 1

  // Apply filters
  if (queryParams.city) {
    query += ` AND p.city ILIKE $${paramIndex}`
    values.push(`%${queryParams.city}%`)
    paramIndex++
  }

  if (queryParams.type) {
    query += ` AND p.property_type = $${paramIndex}`
    values.push(queryParams.type)
    paramIndex++
  }

  if (queryParams.minPrice) {
    query += ` AND p.price >= $${paramIndex}`
    values.push(Number.parseInt(queryParams.minPrice))
    paramIndex++
  }

  if (queryParams.maxPrice) {
    query += ` AND p.price <= $${paramIndex}`
    values.push(Number.parseInt(queryParams.maxPrice))
    paramIndex++
  }

  if (queryParams.bedrooms) {
    query += ` AND p.bedrooms >= $${paramIndex}`
    values.push(Number.parseInt(queryParams.bedrooms))
    paramIndex++
  }

  if (queryParams.search) {
    query += ` AND (p.title ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`
    values.push(`%${queryParams.search}%`)
    paramIndex++
  }

  // Add group by and order by
  query += `
    GROUP BY p.id
    ORDER BY p.created_at DESC
  `

  // Add limit and offset for pagination
  const limit = queryParams.limit ? Number.parseInt(queryParams.limit) : 10
  const offset = queryParams.page ? (Number.parseInt(queryParams.page) - 1) * limit : 0

  query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
  values.push(limit, offset)

  try {
    const result = await pool.query(query, values)

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(result.rows),
    }
  } catch (error) {
    console.error("Error fetching properties:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to fetch properties" }),
    }
  }
}

// Get landlord properties
export async function getLandlordProperties(
  event: APIGatewayProxyEvent,
  pool: Pool,
  userId: string,
): Promise<APIGatewayProxyResult> {
  try {
    const query = `
      SELECT p.*, 
             (SELECT pi.image_url FROM property_images pi WHERE pi.property_id = p.id AND pi.is_primary = true LIMIT 1) as image
      FROM properties p
      WHERE p.landlord_id = $1
      ORDER BY p.created_at DESC
    `

    const result = await pool.query(query, [userId])

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ properties: result.rows }),
    }
  } catch (error) {
    console.error("Error fetching landlord properties:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to fetch landlord properties" }),
    }
  }
}

// Get property by ID
export async function getPropertyById(
  propertyId: string,
  event: APIGatewayProxyEvent,
  pool: Pool,
): Promise<APIGatewayProxyResult> {
  try {
    // Get property details
    const propertyQuery = `
      SELECT p.*, 
             u.first_name AS landlord_first_name,
             u.last_name AS landlord_last_name,
             u.email AS landlord_email,
             u.phone AS landlord_phone
      FROM properties p
      JOIN users u ON p.landlord_id = u.id
      WHERE p.id = $1
    `

    const propertyResult = await pool.query(propertyQuery, [propertyId])

    if (propertyResult.rows.length === 0) {
      return {
        statusCode: 404,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
        body: JSON.stringify({ error: "Property not found" }),
      }
    }

    const property = propertyResult.rows[0]

    // Get property images
    const imagesQuery = `
      SELECT * FROM property_images
      WHERE property_id = $1
      ORDER BY is_primary DESC
    `

    const imagesResult = await pool.query(imagesQuery, [propertyId])
    property.images = imagesResult.rows

    // Get property amenities
    const amenitiesQuery = `
      SELECT a.* FROM amenities a
      JOIN property_amenities pa ON a.id = pa.amenity_id
      WHERE pa.property_id = $1
      ORDER BY a.category, a.name
    `

    const amenitiesResult = await pool.query(amenitiesQuery, [propertyId])
    property.amenities = amenitiesResult.rows

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(property),
    }
  } catch (error) {
    console.error("Error fetching property:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to fetch property" }),
    }
  }
}

// Create new property
export async function createProperty(
  event: APIGatewayProxyEvent,
  pool: Pool,
  userId: string,
): Promise<APIGatewayProxyResult> {
  // Check if user is a landlord
  const userQuery = `
    SELECT is_landlord FROM users
    WHERE id = $1
  `

  const userResult = await pool.query(userQuery, [userId])

  if (userResult.rows.length === 0 || !userResult.rows[0].is_landlord) {
    return {
      statusCode: 403,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Only landlords can create properties" }),
    }
  }

  // Parse request body
  const propertyData = JSON.parse(event.body || "{}")

  // Validate required fields
  const requiredFields = [
    "title",
    "description",
    "address",
    "city",
    "province",
    "postal_code",
    "property_type",
    "bedrooms",
    "bathrooms",
    "square_feet",
    "price",
    "available_from",
  ]

  for (const field of requiredFields) {
    if (!propertyData[field]) {
      return {
        statusCode: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
        body: JSON.stringify({ error: `Missing required field: ${field}` }),
      }
    }
  }

  try {
    // Start transaction
    await pool.query("BEGIN")

    // Insert property
    const propertyId = uuidv4()
    const insertPropertyQuery = `
      INSERT INTO properties (
        id, title, description, address, city, province, postal_code,
        property_type, bedrooms, bathrooms, square_feet, price,
        available_from, landlord_id, status
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      RETURNING *
    `

    const propertyValues = [
      propertyId,
      propertyData.title,
      propertyData.description,
      propertyData.address,
      propertyData.city,
      propertyData.province,
      propertyData.postal_code,
      propertyData.property_type,
      propertyData.bedrooms,
      propertyData.bathrooms,
      propertyData.square_feet,
      propertyData.price,
      propertyData.available_from,
      userId,
      "pending", // New properties start as pending
    ]

    const propertyResult = await pool.query(insertPropertyQuery, propertyValues)
    const property = propertyResult.rows[0]

    // Insert property images if provided
    if (propertyData.images && Array.isArray(propertyData.images)) {
      const insertImageQuery = `
        INSERT INTO property_images (id, property_id, image_url, is_primary)
        VALUES ($1, $2, $3, $4)
      `

      for (let i = 0; i < propertyData.images.length; i++) {
        const image = propertyData.images[i]
        await pool.query(insertImageQuery, [
          uuidv4(),
          propertyId,
          image.url,
          i === 0, // First image is primary
        ])
      }
    }

    // Insert property amenities if provided
    if (propertyData.amenities && Array.isArray(propertyData.amenities)) {
      const insertAmenityQuery = `
        INSERT INTO property_amenities (property_id, amenity_id)
        VALUES ($1, $2)
      `

      for (const amenityId of propertyData.amenities) {
        await pool.query(insertAmenityQuery, [propertyId, amenityId])
      }
    }

    // Commit transaction
    await pool.query("COMMIT")

    return {
      statusCode: 201,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(property),
    }
  } catch (error) {
    // Rollback transaction on error
    await pool.query("ROLLBACK")

    console.error("Error creating property:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to create property" }),
    }
  }
}

// Update property
export async function updateProperty(
  propertyId: string,
  event: APIGatewayProxyEvent,
  pool: Pool,
  userId: string,
): Promise<APIGatewayProxyResult> {
  // Check if property exists and belongs to the user
  const propertyQuery = `
    SELECT * FROM properties
    WHERE id = $1
  `

  const propertyResult = await pool.query(propertyQuery, [propertyId])

  if (propertyResult.rows.length === 0) {
    return {
      statusCode: 404,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Property not found" }),
    }
  }

  const property = propertyResult.rows[0]

  // Check if user is the landlord
  if (property.landlord_id !== userId) {
    return {
      statusCode: 403,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "You do not have permission to update this property" }),
    }
  }

  // Parse request body
  const propertyData = JSON.parse(event.body || "{}")

  try {
    // Start transaction
    await pool.query("BEGIN")

    // Update property
    const updatePropertyQuery = `
      UPDATE properties
      SET 
        title = $1,
        description = $2,
        address = $3,
        city = $4,
        province = $5,
        postal_code = $6,
        property_type = $7,
        bedrooms = $8,
        bathrooms = $9,
        square_feet = $10,
        price = $11,
        available_from = $12,
        updated_at = NOW(),
        status = $13
      WHERE id = $14
      RETURNING *
    `

    const updateValues = [
      propertyData.title || property.title,
      propertyData.description || property.description,
      propertyData.address || property.address,
      propertyData.city || property.city,
      propertyData.province || property.province,
      propertyData.postal_code || property.postal_code,
      propertyData.property_type || property.property_type,
      propertyData.bedrooms || property.bedrooms,
      propertyData.bathrooms || property.bathrooms,
      propertyData.square_feet || property.square_feet,
      propertyData.price || property.price,
      propertyData.available_from || property.available_from,
      property.status === "rejected" ? "pending" : property.status, // If rejected, set back to pending on update
      propertyId,
    ]

    const updatedPropertyResult = await pool.query(updatePropertyQuery, updateValues)
    const updatedProperty = updatedPropertyResult.rows[0]

    // Update images if provided
    if (propertyData.images && Array.isArray(propertyData.images)) {
      // Delete existing images
      await pool.query("DELETE FROM property_images WHERE property_id = $1", [propertyId])

      // Insert new images
      const insertImageQuery = `
        INSERT INTO property_images (id, property_id, image_url, is_primary)
        VALUES ($1, $2, $3, $4)
      `

      for (let i = 0; i < propertyData.images.length; i++) {
        const image = propertyData.images[i]
        await pool.query(insertImageQuery, [
          uuidv4(),
          propertyId,
          image.url,
          i === 0, // First image is primary
        ])
      }
    }

    // Update amenities if provided
    if (propertyData.amenities && Array.isArray(propertyData.amenities)) {
      // Delete existing amenities
      await pool.query("DELETE FROM property_amenities WHERE property_id = $1", [propertyId])

      // Insert new amenities
      const insertAmenityQuery = `
        INSERT INTO property_amenities (property_id, amenity_id)
        VALUES ($1, $2)
      `

      for (const amenityId of propertyData.amenities) {
        await pool.query(insertAmenityQuery, [propertyId, amenityId])
      }
    }

    // Commit transaction
    await pool.query("COMMIT")

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(updatedProperty),
    }
  } catch (error) {
    // Rollback transaction on error
    await pool.query("ROLLBACK")

    console.error("Error updating property:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to update property" }),
    }
  }
}

// Delete property
export async function deleteProperty(
  propertyId: string,
  event: APIGatewayProxyEvent,
  pool: Pool,
  userId: string,
): Promise<APIGatewayProxyResult> {
  // Check if property exists and belongs to the user
  const propertyQuery = `
    SELECT * FROM properties
    WHERE id = $1
  `

  const propertyResult = await pool.query(propertyQuery, [propertyId])

  if (propertyResult.rows.length === 0) {
    return {
      statusCode: 404,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Property not found" }),
    }
  }

  const property = propertyResult.rows[0]

  // Check if user is the landlord
  if (property.landlord_id !== userId) {
    return {
      statusCode: 403,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "You do not have permission to delete this property" }),
    }
  }

  try {
    // Start transaction
    await pool.query("BEGIN")

    // Delete property images
    await pool.query("DELETE FROM property_images WHERE property_id = $1", [propertyId])

    // Delete property amenities
    await pool.query("DELETE FROM property_amenities WHERE property_id = $1", [propertyId])

    // Delete property applications
    await pool.query("DELETE FROM applications WHERE property_id = $1", [propertyId])

    // Delete saved properties
    await pool.query("DELETE FROM saved_properties WHERE property_id = $1", [propertyId])

    // Delete property
    await pool.query("DELETE FROM properties WHERE id = $1", [propertyId])

    // Commit transaction
    await pool.query("COMMIT")

    return {
      statusCode: 204,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: "",
    }
  } catch (error) {
    // Rollback transaction on error
    await pool.query("ROLLBACK")

    console.error("Error deleting property:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to delete property" }),
    }
  }
}
