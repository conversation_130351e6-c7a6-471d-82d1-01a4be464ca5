"use server"

import {
  generateRentEstimate,
  generateMarketTrendsForCity,
  searchPropertiesByLocation,
  CANADIAN_CITIES,
} from "@/lib/api/canadian-rental-data"

export async function fetchRentEstimate(address: string, bedrooms?: number, bathrooms?: number, squareFeet?: number) {
  try {
    // Use our Canadian data service instead of Rentcast API
    return generateRentEstimate(address, bedrooms, bathrooms, squareFeet)
  } catch (error) {
    console.error("Error fetching rent estimate:", error)
    return { error: "Failed to fetch rent estimate" }
  }
}

export async function fetchMarketTrends(location: string, propertyType?: string, bedrooms?: number) {
  try {
    // Check if location is a Canadian city
    const matchedCity = CANADIAN_CITIES.find((city) => city.toLowerCase() === location.toLowerCase())

    // If not an exact match, look for partial matches
    const cityToUse =
      matchedCity || CANADIAN_CITIES.find((city) => city.toLowerCase().includes(location.toLowerCase())) || "Toronto" // Default to Toronto if no match

    return generateMarketTrendsForCity(cityToUse, propertyType, bedrooms)
  } catch (error) {
    console.error("Error fetching market trends:", error)
    return { error: "Failed to fetch market trends" }
  }
}

export async function fetchComparableProperties(address: string, radius = 1, limit = 5) {
  try {
    // Extract city from address
    const addressParts = address.split(",").map((part) => part.trim())
    let city = "Toronto" // Default

    // Try to find a known city in the address
    for (const part of addressParts) {
      if (CANADIAN_CITIES.includes(part)) {
        city = part
        break
      }
    }

    // Generate rent estimate which includes comparables
    const rentEstimate = generateRentEstimate(address, 2, 1, 800)

    return {
      address,
      radius,
      comparables: rentEstimate.comparables,
    }
  } catch (error) {
    console.error("Error fetching comparable properties:", error)
    return { error: "Failed to fetch comparable properties" }
  }
}

export async function fetchPropertiesByLocation(location: string, limit = 10) {
  try {
    return searchPropertiesByLocation(location, limit)
  } catch (error) {
    console.error("Error searching properties:", error)
    return { error: "Failed to fetch properties" }
  }
}
