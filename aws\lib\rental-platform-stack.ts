import * as cdk from "aws-cdk-lib"
import type { Construct } from "constructs"
import * as s3 from "aws-cdk-lib/aws-s3"
import * as iam from "aws-cdk-lib/aws-iam"
import * as lambda from "aws-cdk-lib/aws-lambda"
import * as apigateway from "aws-cdk-lib/aws-apigateway"
import * as rds from "aws-cdk-lib/aws-rds"
import * as ec2 from "aws-cdk-lib/aws-ec2"
import * as cognito from "aws-cdk-lib/aws-cognito"

export class RentalPlatformStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props)

    // Create VPC for RDS
    const vpc = new ec2.Vpc(this, "RentalPlatformVPC", {
      maxAzs: 2,
      natGateways: 1,
    })

    // Create S3 bucket for property images
    const propertyImagesBucket = new s3.Bucket(this, "PropertyImagesBucket", {
      removalPolicy: cdk.RemovalPolicy.RETAIN,
      cors: [
        {
          allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.POST, s3.HttpMethods.PUT],
          allowedOrigins: ["*"], // Restrict to your domain in production
          allowedHeaders: ["*"],
          exposedHeaders: ["x-amz-server-side-encryption", "x-amz-request-id", "x-amz-id-2", "ETag"],
          maxAge: 3000,
        },
      ],
    })

    // Create Cognito User Pool for authentication
    const userPool = new cognito.UserPool(this, "RentalPlatformUserPool", {
      selfSignUpEnabled: true,
      autoVerify: { email: true },
      standardAttributes: {
        email: { required: true, mutable: true },
        givenName: { required: true, mutable: true },
        familyName: { required: true, mutable: true },
        phoneNumber: { required: false, mutable: true },
      },
      customAttributes: {
        isLandlord: new cognito.StringAttribute({ mutable: true }),
        city: new cognito.StringAttribute({ mutable: true }),
        postalCode: new cognito.StringAttribute({ mutable: true }),
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: true,
      },
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
    })

    // Create Cognito User Pool Client
    const userPoolClient = new cognito.UserPoolClient(this, "RentalPlatformUserPoolClient", {
      userPool,
      generateSecret: false,
      authFlows: {
        userPassword: true,
        userSrp: true,
      },
    })

    // Create RDS PostgreSQL instance
    const dbInstance = new rds.DatabaseInstance(this, "RentalPlatformDatabase", {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_14,
      }),
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.BURSTABLE3, ec2.InstanceSize.SMALL),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      allocatedStorage: 20,
      maxAllocatedStorage: 100,
      storageType: rds.StorageType.GP2,
      backupRetention: cdk.Duration.days(7),
      deleteAutomatedBackups: true,
      deletionProtection: false, // Set to true for production
      databaseName: "rentalplatform",
      credentials: rds.Credentials.fromGeneratedSecret("postgres"),
      parameterGroup: new rds.ParameterGroup(this, "RentalPlatformParameterGroup", {
        engine: rds.DatabaseInstanceEngine.postgres({
          version: rds.PostgresEngineVersion.VER_14,
        }),
        parameters: {
          max_connections: "100",
        },
      }),
    })

    // Create Lambda execution role
    const lambdaExecutionRole = new iam.Role(this, "LambdaExecutionRole", {
      assumedBy: new iam.ServicePrincipal("lambda.amazonaws.com"),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSLambdaBasicExecutionRole")],
    })

    // Grant Lambda access to S3 bucket
    propertyImagesBucket.grantReadWrite(lambdaExecutionRole)

    // Create Lambda function for API
    const apiLambda = new lambda.Function(this, "RentalPlatformApiLambda", {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: "index.handler",
      code: lambda.Code.fromAsset("lambda"),
      environment: {
        DB_SECRET_ARN: dbInstance.secret?.secretArn || "",
        USER_POOL_ID: userPool.userPoolId,
        USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
        PROPERTY_IMAGES_BUCKET: propertyImagesBucket.bucketName,
      },
      timeout: cdk.Duration.seconds(30),
      memorySize: 1024,
      role: lambdaExecutionRole,
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
    })

    // Grant Lambda access to RDS secret
    dbInstance.secret?.grantRead(apiLambda)

    // Create API Gateway
    const api = new apigateway.RestApi(this, "RentalPlatformApi", {
      restApiName: "Rental Platform API",
      description: "API for the rental property platform",
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: apigateway.Cors.DEFAULT_HEADERS,
        maxAge: cdk.Duration.days(1),
      },
    })

    // Create API Gateway authorizer with Cognito
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, "RentalPlatformAuthorizer", {
      cognitoUserPools: [userPool],
    })

    // Create API Gateway resources and methods
    const apiResource = api.root.addResource("api")

    // Properties resource
    const propertiesResource = apiResource.addResource("properties")
    propertiesResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda))
    propertiesResource.addMethod("POST", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    const propertyResource = propertiesResource.addResource("{id}")
    propertyResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda))
    propertyResource.addMethod("PUT", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })
    propertyResource.addMethod("DELETE", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    // Users resource
    const usersResource = apiResource.addResource("users")
    usersResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    const userResource = usersResource.addResource("{id}")
    userResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })
    userResource.addMethod("PUT", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    // Applications resource
    const applicationsResource = apiResource.addResource("applications")
    applicationsResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })
    applicationsResource.addMethod("POST", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    const applicationResource = applicationsResource.addResource("{id}")
    applicationResource.addMethod("GET", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })
    applicationResource.addMethod("PUT", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    // S3 presigned URL resource
    const uploadResource = apiResource.addResource("upload")
    uploadResource.addMethod("POST", new apigateway.LambdaIntegration(apiLambda), {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    })

    // Output important resources
    new cdk.CfnOutput(this, "UserPoolId", {
      value: userPool.userPoolId,
    })

    new cdk.CfnOutput(this, "UserPoolClientId", {
      value: userPoolClient.userPoolClientId,
    })

    new cdk.CfnOutput(this, "ApiUrl", {
      value: api.url,
    })

    new cdk.CfnOutput(this, "PropertyImagesBucketName", {
      value: propertyImagesBucket.bucketName,
    })
  }
}
