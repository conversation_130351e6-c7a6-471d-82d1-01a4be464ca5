"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { PropertyCarousel } from "@/components/property-carousel"
import { CheckCircle, XCircle, User, Calendar, DollarSign, Bed, Bath, Eye } from "lucide-react"
import { approveProperty, rejectProperty } from "@/app/actions/property-actions"

export default function ApprovalsPage() {
  const [properties, setProperties] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedProperty, setSelectedProperty] = useState<any>(null)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    const fetchProperties = async () => {
      setIsLoading(true)
      try {
        // In a real app, you would fetch this data from your API
        // For now, we'll use mock data
        const mockProperties = [
          {
            id: "prop-1",
            title: "Modern Downtown Apartment",
            address: "123 Main Street, Vancouver, BC",
            landlord: {
              id: "user-1",
              name: "John Smith",
              email: "<EMAIL>",
            },
            submittedDate: "2023-05-15",
            price: 1800,
            bedrooms: 2,
            bathrooms: 1,
            squareFeet: 850,
            images: Array(3).fill("/placeholder.svg?height=200&width=300"),
            status: "pending",
            description:
              "A beautiful modern apartment in the heart of downtown. Features include hardwood floors, stainless steel appliances, and a private balcony with city views.",
          },
          {
            id: "prop-2",
            title: "Spacious Family Home",
            address: "456 Oak Avenue, Toronto, ON",
            landlord: {
              id: "user-2",
              name: "Sarah Johnson",
              email: "<EMAIL>",
            },
            submittedDate: "2023-05-14",
            price: 2200,
            bedrooms: 3,
            bathrooms: 2,
            squareFeet: 1200,
            images: Array(4).fill("/placeholder.svg?height=200&width=300"),
            status: "pending",
            description:
              "Perfect for families, this spacious home features a large backyard, updated kitchen, and is located in a quiet neighborhood close to schools and parks.",
          },
          {
            id: "prop-3",
            title: "Cozy Studio Apartment",
            address: "789 Pine Street, Montreal, QC",
            landlord: {
              id: "user-3",
              name: "Michael Brown",
              email: "<EMAIL>",
            },
            submittedDate: "2023-05-13",
            price: 1200,
            bedrooms: 1,
            bathrooms: 1,
            squareFeet: 500,
            images: Array(2).fill("/placeholder.svg?height=200&width=300"),
            status: "pending",
            description:
              "Charming studio apartment in a historic building. Recently renovated with modern amenities while preserving its original character. Perfect for students or young professionals.",
          },
        ]

        setProperties(mockProperties)
      } catch (error) {
        console.error("Error fetching properties:", error)
        toast({
          title: "Error",
          description: "Failed to load properties for approval.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProperties()
  }, [])

  const handleApprove = async (property: any) => {
    setIsSubmitting(true)
    try {
      // In a real app, you would call your API to approve the property
      await approveProperty(property.id)

      // Update the local state
      setProperties(properties.filter((p) => p.id !== property.id))

      toast({
        title: "Property approved",
        description: `${property.title} has been approved and is now live.`,
      })
    } catch (error) {
      console.error("Error approving property:", error)
      toast({
        title: "Error",
        description: "Failed to approve property. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setIsApproveDialogOpen(false)
    }
  }

  const handleReject = async (property: any) => {
    if (!rejectionReason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for rejection.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      // In a real app, you would call your API to reject the property
      await rejectProperty(property.id, rejectionReason)

      // Update the local state
      setProperties(properties.filter((p) => p.id !== property.id))

      toast({
        title: "Property rejected",
        description: `${property.title} has been rejected.`,
      })
    } catch (error) {
      console.error("Error rejecting property:", error)
      toast({
        title: "Error",
        description: "Failed to reject property. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setRejectionReason("")
      setIsRejectDialogOpen(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <div className="mb-8 space-y-4">
          <h1 className="text-3xl font-bold tracking-tight md:text-4xl">Property Approvals</h1>
          <p className="text-muted-foreground">Review and manage property listings submitted by landlords</p>
        </div>

        <Tabs defaultValue="pending" className="space-y-8">
          <TabsList>
            <TabsTrigger value="pending">Pending ({properties.length})</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-6">
            {isLoading ? (
              Array.from({ length: 3 }).map((_, i) => <Skeleton key={i} className="h-[300px] w-full rounded-lg" />)
            ) : properties.length > 0 ? (
              properties.map((property) => (
                <Card key={property.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="grid gap-6 md:grid-cols-[300px_1fr]">
                      <div>
                        <PropertyCarousel images={property.images} />
                        <div className="mt-4 flex flex-wrap gap-2">
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Bed className="h-3 w-3" />
                            {property.bedrooms} {property.bedrooms === 1 ? "Bedroom" : "Bedrooms"}
                          </Badge>
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Bath className="h-3 w-3" />
                            {property.bathrooms} {property.bathrooms === 1 ? "Bathroom" : "Bathrooms"}
                          </Badge>
                          <Badge variant="outline" className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />${property.price}/month
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-xl font-bold">{property.title}</h3>
                            <Badge
                              variant="outline"
                              className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100"
                            >
                              Pending Review
                            </Badge>
                          </div>
                          <p className="text-muted-foreground">{property.address}</p>
                        </div>

                        <div className="grid gap-4 sm:grid-cols-2">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              Landlord: <span className="font-medium">{property.landlord.name}</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              Submitted: <span className="font-medium">{property.submittedDate}</span>
                            </span>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium">Description</h4>
                          <p className="mt-1 text-sm text-muted-foreground">{property.description}</p>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          <Dialog
                            open={isApproveDialogOpen && selectedProperty?.id === property.id}
                            onOpenChange={(open) => {
                              setIsApproveDialogOpen(open)
                              if (!open) setSelectedProperty(null)
                            }}
                          >
                            <DialogTrigger asChild>
                              <Button onClick={() => setSelectedProperty(property)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approve
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Approve Property Listing</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to approve this property listing? It will be visible to all
                                  users.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                  <h4 className="font-medium">{property.title}</h4>
                                  <p className="text-sm text-muted-foreground">{property.address}</p>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button onClick={() => handleApprove(property)} disabled={isSubmitting}>
                                  {isSubmitting ? "Approving..." : "Approve Listing"}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <Dialog
                            open={isRejectDialogOpen && selectedProperty?.id === property.id}
                            onOpenChange={(open) => {
                              setIsRejectDialogOpen(open)
                              if (!open) {
                                setSelectedProperty(null)
                                setRejectionReason("")
                              }
                            }}
                          >
                            <DialogTrigger asChild>
                              <Button variant="outline" onClick={() => setSelectedProperty(property)}>
                                <XCircle className="mr-2 h-4 w-4" />
                                Reject
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Reject Property Listing</DialogTitle>
                                <DialogDescription>
                                  Please provide a reason for rejecting this property listing. This will be shared with
                                  the landlord.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                  <h4 className="font-medium">{property.title}</h4>
                                  <p className="text-sm text-muted-foreground">{property.address}</p>
                                </div>
                                <Textarea
                                  placeholder="Reason for rejection..."
                                  value={rejectionReason}
                                  onChange={(e) => setRejectionReason(e.target.value)}
                                  className="min-h-[100px]"
                                />
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button
                                  variant="destructive"
                                  onClick={() => handleReject(property)}
                                  disabled={isSubmitting}
                                >
                                  {isSubmitting ? "Rejecting..." : "Reject Listing"}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <Button variant="outline" asChild>
                            <a href={`/dashboard/properties/${property.id}`} target="_blank" rel="noopener noreferrer">
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </a>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                  <CheckCircle className="mb-4 h-12 w-12 text-green-500" />
                  <p className="mb-2 text-lg font-medium">No pending approvals</p>
                  <p className="text-muted-foreground">All property listings have been reviewed.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="approved" className="space-y-6">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <p className="mb-2 text-lg font-medium">Approved properties</p>
                <p className="text-muted-foreground">Approved properties will appear here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rejected" className="space-y-6">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <p className="mb-2 text-lg font-medium">Rejected properties</p>
                <p className="text-muted-foreground">Rejected properties will appear here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
