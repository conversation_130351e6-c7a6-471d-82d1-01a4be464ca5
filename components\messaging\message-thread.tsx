'use client';

import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { MessageContent } from './message-content';
import { MessageReactions } from './message-reactions';

interface ThreadMessage {
  id: string;
  content: {
    type: string;
    text?: string;
    imageUrl?: string;
    propertyId?: string;
    location?: {
      lat: number;
      lng: number;
      address: string;
    };
  };
  senderId: string;
  senderName: string;
  senderImage?: string;
  createdAt: string;
  reactions: {
    id: string;
    emoji: string;
    count: number;
    users: {
      id: string;
      name: string;
    }[];
  }[];
}

interface MessageThreadProps {
  parentMessageId: string;
  conversationId: string;
  onClose: () => void;
  onSendReply: (parentMessageId: string, content: string) => Promise<void>;
  onAddReaction: (messageId: string, emoji: string) => Promise<void>;
  onRemoveReaction: (messageId: string, emoji: string) => Promise<void>;
}

export function MessageThread({
  parentMessageId,
  conversationId,
  onClose,
  onSendReply,
  onAddReaction,
  onRemoveReaction,
}: MessageThreadProps) {
  const [threadMessages, setThreadMessages] = useState<ThreadMessage[]>([]);
  const [newReply, setNewReply] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchThreadMessages();
  }, [parentMessageId, conversationId]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [threadMessages]);

  const fetchThreadMessages = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages/${parentMessageId}/thread`);
      if (!response.ok) throw new Error('Failed to fetch thread messages');

      const data = await response.json();
      setThreadMessages(data);
    } catch (error) {
      console.error('Error fetching thread messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendReply = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newReply.trim() || isSending) return;

    setIsSending(true);
    try {
      await onSendReply(parentMessageId, newReply);
      setNewReply('');
      // Refresh thread messages to include the new reply
      fetchThreadMessages();
    } catch (error) {
      console.error('Error sending reply:', error);
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="flex flex-col h-full border-l">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          <h3 className="font-medium">Thread</h3>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4" ref={scrollRef}>
        {isLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : threadMessages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No replies yet. Start the conversation!
          </div>
        ) : (
          <div className="space-y-4">
            {threadMessages.map((message) => (
              <div key={message.id} className="flex gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={message.senderImage} alt={message.senderName} />
                  <AvatarFallback>{message.senderName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">{message.senderName}</span>
                    <span className="text-xs text-gray-500">
                      {(() => {
                        const date = new Date(message.createdAt);
                        const hours = date.getHours();
                        const minutes = date.getMinutes();
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const formattedHours = hours % 12 || 12;
                        const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
                        return `${formattedHours}:${formattedMinutes} ${ampm}`;
                      })()}
                    </span>
                  </div>
                  <div className="bg-muted rounded-lg p-3">
                    <MessageContent content={message.content} />
                  </div>
                  <MessageReactions
                    messageId={message.id}
                    reactions={message.reactions}
                    onAddReaction={onAddReaction}
                    onRemoveReaction={onRemoveReaction}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      <form onSubmit={handleSendReply} className="p-4 border-t">
        <div className="flex gap-2">
          <Textarea
            placeholder="Reply to thread..."
            value={newReply}
            onChange={(e) => setNewReply(e.target.value)}
            className="min-h-[80px] resize-none"
            disabled={isSending}
          />
          <Button type="submit" size="icon" disabled={isSending || !newReply.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}