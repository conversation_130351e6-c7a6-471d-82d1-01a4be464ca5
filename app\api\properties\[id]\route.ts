import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb/client";
import { auth } from "@/lib/firebase-admin";
import { ObjectId } from "mongodb";

// Helper function to convert MongoDB _id to string id
function formatProperty(property) {
  if (!property) return null;

  return {
    ...property,
    id: property._id.toString(),
    _id: undefined
  };
}

// Helper function to sanitize image URLs
function sanitizeImageUrls(images: string[]): string[] {
  if (!Array.isArray(images)) return [];

  return images
    .filter(url => typeof url === 'string' && url.trim() !== '')
    .map(url => {
      // Strip any query parameters
      const cleanUrl = url.split('?')[0];
      return cleanUrl;
    })
    .filter(Boolean); // Remove any empty strings
}

// Error response wrapper for consistent error handling
function errorResponse(error: any, message: string = 'An error occurred', status: number = 500) {
  console.error(`API Error - ${message}:`, error);
  return NextResponse.json({ error: message, details: error.message }, { status });
}

// GET a property by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching property with ID: ${params.id}`);
    const propertyId = params.id;

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Get property by ID
    let property;
    try {
      property = await propertiesCollection.findOne({
        _id: new ObjectId(propertyId)
      });
    } catch (error) {
      console.error(`Error parsing ObjectId: ${propertyId}`, error);
      return NextResponse.json({ error: "Invalid property ID format" }, { status: 400 });
    }

    if (!property) {
      console.log(`Property not found with ID: ${propertyId}`);
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    console.log(`Found property: ${property.title}`);

    // Clean image URLs if they exist
    if (property.images && Array.isArray(property.images)) {
      property.images = sanitizeImageUrls(property.images);
    }

    // Format the property for response
    const formattedProperty = formatProperty(property);

    return NextResponse.json(formattedProperty);
  } catch (error) {
    console.error("Error fetching property:", error);
    return NextResponse.json({ error: "Failed to fetch property" }, { status: 500 });
  }
}

// PUT update a property
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;

    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];

    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Get property by ID
    const property = await propertiesCollection.findOne({
      _id: new ObjectId(propertyId)
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if user is the property owner or an admin
    if (property.landlord_id !== userId && !decodedToken.admin) {
      return NextResponse.json({ error: "Unauthorized to edit this property" }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();

    // Update property object
    const updateData = {
      title: body.title,
      description: body.description,
      address: body.address,
      city: body.city,
      province: body.province,
      postal_code: body.postal_code,
      property_type: body.property_type,
      bedrooms: body.bedrooms,
      bathrooms: body.bathrooms,
      square_feet: body.square_feet,
      price: body.price,
      available_from: new Date(body.available_from),
      amenities: body.amenities || [],
      images: body.images || [],
      updated_at: new Date()
    };

    // Update property
    const result = await propertiesCollection.updateOne(
      { _id: new ObjectId(propertyId) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    return NextResponse.json({
      _id: propertyId,
      ...updateData
    });
  } catch (error) {
    console.error("Error updating property:", error);
    return NextResponse.json({ error: "Failed to update property" }, { status: 500 });
  }
}

// DELETE a property
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;

    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];

    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Get property by ID
    const property = await propertiesCollection.findOne({
      _id: new ObjectId(propertyId)
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if user is the property owner or an admin
    if (property.landlord_id !== userId && !decodedToken.admin) {
      return NextResponse.json({ error: "Unauthorized to delete this property" }, { status: 403 });
    }

    // Delete property
    const result = await propertiesCollection.deleteOne({
      _id: new ObjectId(propertyId)
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: "Property not found or already deleted" }, { status: 404 });
    }

    return NextResponse.json({ message: "Property deleted successfully" });
  } catch (error) {
    console.error("Error deleting property:", error);
    return NextResponse.json({ error: "Failed to delete property" }, { status: 500 });
  }
}