import { NextResponse } from 'next/server';
import { JsonStorage } from '@/lib/json-storage';
import { Appointment } from '@/lib/types';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

// Create a storage instance for appointments
const appointmentsStorage = new JsonStorage<Appointment>('appointments');

// Initialize Google Calendar API
const oauth2Client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI
);

// Set the credentials (in a real app, these would come from user authentication)
oauth2Client.setCredentials({
  refresh_token: process.env.GOOGLE_REFRESH_TOKEN
});

const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

// Google Calendar API integration
const googleCalendar = {
  createEvent: async (appointment: Appointment) => {
    try {
      const event = {
        summary: appointment.title,
        description: appointment.description,
        start: {
          dateTime: appointment.startTime,
          timeZone: 'UTC',
        },
        end: {
          dateTime: appointment.endTime,
          timeZone: 'UTC',
        },
      };

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
      });

      return {
        id: response.data.id,
        htmlLink: response.data.htmlLink
      };
    } catch (error) {
      console.error('Error creating Google Calendar event:', error);
      throw error;
    }
  },
  
  updateEvent: async (eventId: string, appointment: Appointment) => {
    try {
      const event = {
        summary: appointment.title,
        description: appointment.description,
        start: {
          dateTime: appointment.startTime,
          timeZone: 'UTC',
        },
        end: {
          dateTime: appointment.endTime,
          timeZone: 'UTC',
        },
      };

      await calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: event,
      });

      return { success: true };
    } catch (error) {
      console.error('Error updating Google Calendar event:', error);
      throw error;
    }
  },
  
  deleteEvent: async (eventId: string) => {
    try {
      await calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
      });

      return { success: true };
    } catch (error) {
      console.error('Error deleting Google Calendar event:', error);
      throw error;
    }
  }
};

export async function GET() {
  try {
    // In a real app, get user ID from session/auth
    const userId = "mock-user-id";
    
    // Get all appointments for this user
    const appointments = await appointmentsStorage.query(item => item.userId === userId);
    return NextResponse.json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json({ error: 'Failed to fetch appointments' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'> = await request.json();
    
    // In a real app, get user ID from session/auth and validate it
    const userId = "mock-user-id";
    
    // Create the appointment in our local storage
    const newAppointment = await appointmentsStorage.create({
      ...appointmentData,
      userId,
      status: appointmentData.status || 'scheduled'
    });
    
    // Create the event in Google Calendar
    const calendarEvent = await googleCalendar.createEvent(newAppointment);
    
    // Update the appointment with the Google Calendar event ID
    const updatedAppointment = await appointmentsStorage.update(newAppointment.id!, {
      googleCalendarEventId: calendarEvent.id,
      googleCalendarLink: calendarEvent.htmlLink
    });

    return NextResponse.json({
      success: true,
      appointment: updatedAppointment,
      googleCalendarLink: calendarEvent.htmlLink
    });
  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json({ error: 'Failed to create appointment' }, { status: 500 });
  }
} 