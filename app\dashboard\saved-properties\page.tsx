"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import {
  Card,
  CardContent
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Heart } from "lucide-react"
import Link from "next/link"
import { PropertyCard } from "@/components/property-card"

// Define property type based on PropertyCard component requirements
interface SavedProperty {
  id: string
  title: string
  address: string
  city: string
  province: string
  price: number
  bedrooms: number
  bathrooms: number
  squareFeet: number
  imageUrl?: string
  isNew?: boolean
  isFeatured?: boolean
}

export default function SavedPropertiesPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [properties, setProperties] = useState<SavedProperty[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
      return
    }

    if (user) {
      fetchSavedProperties()
    }
  }, [user, loading, router])

  const fetchSavedProperties = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/properties/saved")
      if (!response.ok) {
        throw new Error("Failed to fetch saved properties")
      }
      const data = await response.json()

      // Ensure all required properties exist in the API response
      const formattedData = data.map((property: any) => ({
        id: property.id,
        title: property.title,
        address: property.address || "",
        city: property.city || "",
        province: property.province || "",
        price: property.price || 0,
        bedrooms: property.bedrooms || 0,
        bathrooms: property.bathrooms || 0,
        squareFeet: property.squareFeet || 0,
        imageUrl: property.image || property.imageUrl,
        isNew: property.isNew || false,
        isFeatured: property.isFeatured || false
      }))

      setProperties(formattedData)
    } catch (error) {
      console.error("Error fetching saved properties:", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" className="gap-2" asChild>
          <Link href="/dashboard">
            <span className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </span>
          </Link>
        </Button>
      </div>
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          <span className="flex items-center gap-2">
            <Heart className="h-6 w-6 text-rose-500" />
            Saved Properties
          </span>
        </h1>
        <p className="text-muted-foreground">
          Your favorite properties saved for later
        </p>
      </div>
      {properties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {properties.map(property => (
            <PropertyCard key={property.id} {...property} />
          ))}
        </div>
      ) : (
        <Card className="border-0 property-card-shadow">
          <CardContent className="py-12 text-center">
            <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No saved properties yet</h3>
            <p className="text-muted-foreground mb-6">
              Properties you save will appear here for easy access
            </p>
            <Button asChild>
              <Link href="/properties">
                Browse Properties
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}