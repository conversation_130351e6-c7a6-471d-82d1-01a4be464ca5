// Simple script to list properties in the database
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function listProperties() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }
  
  console.log('Connecting to MongoDB...');
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');
    
    // Count properties
    const count = await propertiesCollection.countDocuments();
    console.log(`Found ${count} properties in the database`);
    
    // List 5 properties
    const properties = await propertiesCollection.find().limit(5).toArray();
    
    console.log('\nSample properties:');
    properties.forEach((property, index) => {
      console.log(`\n${index + 1}. ${property.title}`);
      console.log(`   City: ${property.city}, Province: ${property.province}`);
      console.log(`   Price: $${property.price}, Bedrooms: ${property.bedrooms}, Bathrooms: ${property.bathrooms}`);
      console.log(`   Status: ${property.status}`);
    });
    
  } catch (error) {
    console.error('Error listing properties:', error);
  } finally {
    await client.close();
    console.log('\nDatabase connection closed');
  }
}

listProperties().catch(console.error);
