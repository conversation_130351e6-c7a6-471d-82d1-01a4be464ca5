import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // Only apply to API routes and admin dashboard
  if (request.nextUrl.pathname.startsWith('/api/') || request.nextUrl.pathname.startsWith('/dashboard/admin')) {
    // Skip authentication for public API routes
    if (
      request.nextUrl.pathname.startsWith('/api/auth/') ||
      request.nextUrl.pathname === '/api/health' ||
      request.nextUrl.pathname.startsWith('/api/properties') ||
      request.nextUrl.pathname.startsWith('/api/test-db')
    ) {
      return NextResponse.next();
    }

    // Check for authentication token
    const token = await getToken({ req: request });

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // For admin routes, check if user has admin role
    if (request.nextUrl.pathname.startsWith('/dashboard/admin')) {
      if (token.role !== 'admin') {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/api/:path*', '/dashboard/admin/:path*'],
};