"use client"

import { useEffect, useRef, useState } from "react"
import { Loader } from "@googlemaps/js-api-loader"
import { getGoogleMapsApiKey } from "@/app/actions/maps-actions"
import { Skeleton } from "@/components/ui/skeleton"

interface PropertyMapProps {
  postalCode?: string
  singleProperty?: {
    id: string
    title: string
    location: {
      lat: number
      lng: number
    }
  }
}

export function PropertyMap({ postalCode, singleProperty }: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true

    const initializeMap = async () => {
      try {
        // Get API key from server action
        const { apiKey } = await getGoogleMapsApiKey()

        if (!apiKey) {
          throw new Error("Google Maps API key not available")
        }

        const loader = new Loader({
          api<PERSON><PERSON>,
          version: "weekly",
        })

        const google = await loader.load()

        if (!isMounted || !mapRef.current) return

        // Default to Vancouver if no postal code or property is provided
        const defaultLocation = { lat: 49.2827, lng: -123.1207 }

        const map = new google.maps.Map(mapRef.current, {
          center: singleProperty ? singleProperty.location : defaultLocation,
          zoom: singleProperty ? 15 : 12,
          mapTypeControl: false,
        })

        if (singleProperty) {
          new google.maps.Marker({
            position: singleProperty.location,
            map,
            title: singleProperty.title,
          })
        } else {
          // In a real app, you would geocode the postal code and add markers for properties
          // This is a placeholder for demonstration
          const mockProperties = [
            { lat: 49.2827, lng: -123.1207, title: "Property 1" },
            { lat: 49.276, lng: -123.13, title: "Property 2" },
            { lat: 49.29, lng: -123.11, title: "Property 3" },
          ]

          mockProperties.forEach((prop) => {
            new google.maps.Marker({
              position: { lat: prop.lat, lng: prop.lng },
              map,
              title: prop.title,
            })
          })
        }

        setIsLoading(false)
      } catch (err) {
        console.error("Error loading Google Maps:", err)
        if (isMounted) {
          setError("Failed to load map. Please try again later.")
          setIsLoading(false)
        }
      }
    }

    initializeMap()

    return () => {
      isMounted = false
    }
  }, [postalCode, singleProperty])

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full rounded-md" aria-label="Loading map..." />
  }

  if (error) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center rounded-md border bg-muted/50 text-muted-foreground">
        {error}
      </div>
    )
  }

  return <div ref={mapRef} className="h-[400px] w-full rounded-md border" aria-label="Map showing property locations" />
}
