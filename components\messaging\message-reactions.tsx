'use client';

import { useState } from 'react';
import { Smile, ThumbsUp, Heart, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSession } from 'next-auth/react';

interface Reaction {
  id: string;
  emoji: string;
  count: number;
  users: {
    id: string;
    name: string;
  }[];
}

interface MessageReactionsProps {
  messageId: string;
  reactions: Reaction[];
  onAddReaction: (messageId: string, emoji: string) => Promise<void>;
  onRemoveReaction: (messageId: string, emoji: string) => Promise<void>;
}

const REACTION_OPTIONS = [
  { emoji: '👍', label: 'Thumbs Up', icon: ThumbsUp },
  { emoji: '❤️', label: 'Heart', icon: Heart },
  { emoji: '😊', label: 'Smile', icon: Smile },
  { emoji: '💬', label: 'Comment', icon: MessageCircle },
];

export function MessageReactions({
  messageId,
  reactions,
  onAddReaction,
  onRemoveReaction,
}: MessageReactionsProps) {
  const { data: session } = useSession();
  const [isAddingReaction, setIsAddingReaction] = useState(false);
  const [showReactionPicker, setShowReactionPicker] = useState(false);

  const handleAddReaction = async (emoji: string) => {
    if (!session?.user) return;
    
    setIsAddingReaction(true);
    try {
      await onAddReaction(messageId, emoji);
      setShowReactionPicker(false);
    } catch (error) {
      console.error('Error adding reaction:', error);
    } finally {
      setIsAddingReaction(false);
    }
  };

  const handleRemoveReaction = async (emoji: string) => {
    if (!session?.user) return;
    
    try {
      await onRemoveReaction(messageId, emoji);
    } catch (error) {
      console.error('Error removing reaction:', error);
    }
  };

  const hasUserReacted = (emoji: string) => {
    if (!session?.user) return false;
    return reactions.some(
      (reaction) => 
        reaction.emoji === emoji && 
        reaction.users.some(user => user.id === session.user.id)
    );
  };

  return (
    <div className="flex items-center gap-1 mt-1">
      {reactions.map((reaction) => {
        const hasReacted = hasUserReacted(reaction.emoji);
        const Icon = REACTION_OPTIONS.find(opt => opt.emoji === reaction.emoji)?.icon || Smile;
        
        return (
          <TooltipProvider key={reaction.id}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={hasReacted ? "default" : "outline"}
                  size="sm"
                  className={`h-6 px-2 text-xs ${hasReacted ? 'bg-primary/10 hover:bg-primary/20' : ''}`}
                  onClick={() => hasReacted 
                    ? handleRemoveReaction(reaction.emoji) 
                    : handleAddReaction(reaction.emoji)
                  }
                >
                  <span className="mr-1">{reaction.emoji}</span>
                  <span>{reaction.count}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">
                  {reaction.users.map(user => user.name).join(', ')}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
      
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => setShowReactionPicker(!showReactionPicker)}
          disabled={isAddingReaction}
        >
          <Smile className="h-4 w-4" />
        </Button>
        
        {showReactionPicker && (
          <div className="absolute bottom-full left-0 mb-1 bg-background border rounded-md shadow-md p-1 flex gap-1">
            {REACTION_OPTIONS.map((option) => (
              <Button
                key={option.emoji}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleAddReaction(option.emoji)}
                disabled={isAddingReaction}
              >
                {option.emoji}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 