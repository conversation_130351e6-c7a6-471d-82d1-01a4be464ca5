import { NextRequest, NextResponse } from 'next/server';
import { JsonStorage } from '@/lib/json-storage';
import { Appointment } from '@/lib/types';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

// Create a storage instance for appointments
const appointmentsStorage = new JsonStorage<Appointment>('appointments');

// Initialize Google Calendar API
const oauth2Client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI
);

// Set the credentials (in a real app, these would come from user authentication)
oauth2Client.setCredentials({
  refresh_token: process.env.GOOGLE_REFRESH_TOKEN
});

const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appointmentId = params.id;
    
    if (!appointmentId) {
      return NextResponse.json({ error: 'Appointment ID is required' }, { status: 400 });
    }

    // Get the appointment from our database
    const appointment = await appointmentsStorage.getById(appointmentId);
    
    if (!appointment) {
      return NextResponse.json({ error: 'Appointment not found' }, { status: 404 });
    }
    
    // Check if this appointment already has a Google Calendar event
    if (appointment.googleCalendarEventId && appointment.googleCalendarLink) {
      return NextResponse.json({
        success: true,
        appointment,
        googleCalendarLink: appointment.googleCalendarLink,
        message: 'Appointment already has a Google Calendar event'
      });
    }
    
    // Create the event in Google Calendar
    const event = {
      summary: appointment.title,
      description: appointment.description || '',
      start: {
        dateTime: appointment.startTime,
        timeZone: 'UTC',
      },
      end: {
        dateTime: appointment.endTime,
        timeZone: 'UTC',
      },
      // Location is not in our Appointment type, so we're not setting it
    };

    const response = await calendar.events.insert({
      calendarId: 'primary',
      requestBody: event,
    });

    const calendarEvent = {
      id: response.data.id || '',
      htmlLink: response.data.htmlLink || ''
    };
    
    // Update the appointment with the Google Calendar event ID
    const updatedAppointment = await appointmentsStorage.update(appointmentId, {
      googleCalendarEventId: calendarEvent.id,
      googleCalendarLink: calendarEvent.htmlLink
    });

    return NextResponse.json({
      success: true,
      appointment: updatedAppointment,
      googleCalendarLink: calendarEvent.htmlLink
    });
  } catch (error) {
    console.error('Error adding appointment to Google Calendar:', error);
    return NextResponse.json({ 
      error: 'Failed to add appointment to Google Calendar',
      details: error instanceof Error ? error.message : undefined
    }, { status: 500 });
  }
} 