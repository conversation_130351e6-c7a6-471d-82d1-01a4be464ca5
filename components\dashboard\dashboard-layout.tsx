"use client"

import { type <PERSON><PERSON>N<PERSON>, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { Toaster } from "@/components/ui/toaster"
import { Skeleton } from "@/components/ui/skeleton"

interface DashboardLayoutProps {
  children: ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true)

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false)
        setIsCheckingAdmin(false)
        return
      }

      try {
        // In a real app, you would check if the user is an admin
        // For now, we'll use a simple check based on email domain
        setIsAdmin(user.email.endsWith("@rentcentral.com"))
      } catch (error) {
        console.error("Error checking admin status:", error)
        setIsAdmin(false)
      } finally {
        setIsCheckingAdmin(false)
      }
    }

    checkAdminStatus()
  }, [user])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user && !pathname.startsWith("/auth")) {
      router.push("/auth/login?redirect=" + encodeURIComponent(pathname))
    }
  }, [isLoading, user, router, pathname])

  if (isLoading || isCheckingAdmin) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="space-y-4 text-center">
          <Skeleton className="mx-auto h-12 w-12 rounded-full" />
          <Skeleton className="mx-auto h-4 w-32" />
          <Skeleton className="mx-auto h-4 w-48" />
        </div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen">
        <DashboardSidebar isAdmin={isAdmin || false} isLandlord={user?.isLandlord || false} />
        <main className="flex-1 overflow-x-hidden">{children}</main>
      </div>
      <Toaster />
    </SidebarProvider>
  )
}
