// This script checks the properties in the database and their status
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function main() {
  try {
    // Connect directly to MongoDB
    const uri = process.env.MONGODB_URI;
    if (!uri) {
      throw new Error('MONGODB_URI is not defined');
    }

    console.log('Connecting to MongoDB...');
    const client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Count total properties
    const totalCount = await propertiesCollection.countDocuments();
    console.log(`Total properties: ${totalCount}`);

    // Count properties by status
    const statusCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    console.log('\nProperties by status:');
    statusCounts.forEach(status => {
      console.log(`- ${status._id || 'undefined'}: ${status.count}`);
    });

    // Get sample properties
    const approvedProperties = await propertiesCollection.find({ status: 'approved' }).limit(3).toArray();
    
    console.log('\nSample approved properties:');
    if (approvedProperties.length > 0) {
      approvedProperties.forEach(property => {
        console.log({
          id: property._id,
          title: property.title,
          city: property.city,
          province: property.province,
          price: property.price,
          status: property.status
        });
      });
    } else {
      console.log('No approved properties found');
    }

    // Check if properties have images
    const propertiesWithImages = await propertiesCollection.aggregate([
      { $lookup: {
          from: "propertyImages",
          localField: "_id",
          foreignField: "propertyId",
          as: "images"
        }
      },
      { $match: { status: 'approved' } },
      { $project: {
          _id: 1,
          title: 1,
          imageCount: { $size: "$images" }
        }
      },
      { $limit: 5 }
    ]).toArray();

    console.log('\nApproved properties with image counts:');
    propertiesWithImages.forEach(property => {
      console.log(`- ${property.title}: ${property.imageCount} images`);
    });

    await client.close();
    console.log('\nDisconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
