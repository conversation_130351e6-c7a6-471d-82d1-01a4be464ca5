import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb/client";
import { auth } from "@/lib/firebase-admin";
import { ObjectId } from "mongodb";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const { appealReason } = await request.json();

    if (!appealReason) {
      return NextResponse.json({ error: "Appeal reason is required" }, { status: 400 });
    }

    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Get the property
    const property = await propertiesCollection.findOne({
      _id: new ObjectId(propertyId)
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if user is the property owner
    if (property.landlord_id !== decodedToken.uid) {
      return NextResponse.json({ error: "Unauthorized - Only the property owner can appeal" }, { status: 403 });
    }

    // Check if property is declined
    if (property.status !== 'declined') {
      return NextResponse.json({ error: "Only declined properties can be appealed" }, { status: 400 });
    }

    // Update property status
    const result = await propertiesCollection.updateOne(
      { _id: new ObjectId(propertyId) },
      { 
        $set: { 
          status: 'appealed',
          updatedAt: new Date(),
          appealedAt: new Date(),
          appealReason: appealReason
        } 
      }
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error appealing property:", error);
    return NextResponse.json({ error: "Failed to appeal property" }, { status: 500 });
  }
} 