import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb/client";
import { ObjectId } from "mongodb";
import { getServerSession } from 'next-auth'
import { connectToDatabase } from '@/lib/mongodb'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'

// Helper function to sanitize image URLs
function sanitizeImageUrls(images: string[]): string[] {
  if (!Array.isArray(images)) return [];

  return images
    .filter(url => typeof url === 'string' && url.trim() !== '')
    .map(url => {
      // Strip any query parameters
      const cleanUrl = url.split('?')[0];
      return cleanUrl;
    })
    .filter(Boolean); // Remove any empty strings
}

// Helper function for consistent error responses
function errorResponse(error: any, message: string = 'An error occurred', status: number = 500) {
  console.error(`${message}:`, error);
  return NextResponse.json(
    {
      error: message,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    },
    { status }
  );
}

// GET all properties or filter by query parameters
export async function GET(request: Request) {
  try {
    // No authentication check for public properties

    const { searchParams } = new URL(request.url)
    const city = searchParams.get('city')
    const propertyType = searchParams.get('propertyType')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const search = searchParams.get('search')

    console.log('API Request params:', {
      city,
      propertyType,
      minPrice,
      maxPrice,
      search,
      allParams: Object.fromEntries(searchParams.entries())
    })

    // Connect to MongoDB directly
    console.log('Connecting to MongoDB...');
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Build MongoDB query
    const query: any = {
      // Include all properties for now (for testing)
      // status: 'approved'
    };

    // Add filters
    if (city) {
      query.city = { $regex: city, $options: 'i' };
    }

    if (propertyType) {
      query.propertyType = { $regex: propertyType, $options: 'i' };
    }

    if (minPrice) {
      query.price = { ...(query.price || {}), $gte: parseFloat(minPrice) };
    }

    if (maxPrice) {
      query.price = { ...(query.price || {}), $lte: parseFloat(maxPrice) };
    }

    // Add search conditions
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { city: { $regex: search, $options: 'i' } },
        { province: { $regex: search, $options: 'i' } },
        { address: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('MongoDB query:', JSON.stringify(query, null, 2));

    // Execute query
    console.log('Executing MongoDB query...');
    const properties = await propertiesCollection.find(query).sort({ createdAt: -1 }).toArray();

    console.log(`Found ${properties.length} properties`);

    if (properties.length > 0) {
      console.log('First property:', {
        id: properties[0]._id,
        title: properties[0].title,
        status: properties[0].status
      });
    }

    // Clean up property IDs for JSON response
    const cleanedProperties = properties.map(property => ({
      ...property,
      id: property._id.toString(),
      _id: undefined
    }));

    return NextResponse.json(cleanedProperties)
  } catch (error) {
    console.error('Error fetching properties:', error)
    return NextResponse.json(
      { error: 'Failed to fetch properties' },
      { status: 500 }
    )
  }
}

// POST a new property
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const data = await request.json()
    console.log('Creating property with data:', data)

    // Extract images from the data
    const images = data.images || []

    // Map property fields to match the database schema
    const propertyData = {
      title: data.title,
      description: data.description,
      address: data.address,
      city: data.city,
      province: data.province,
      postalCode: data.postal_code,
      propertyType: data.propertyType || data.property_type,
      bedrooms: Number(data.bedrooms),
      bathrooms: Number(data.bathrooms),
      squareFeet: Number(data.square_feet),
      price: Number(data.price),
      availableFrom: new Date(data.available_from),
      status: 'pending',
      landlordId: session.user.id,
      // Additional fields
      parkingSpots: Number(data.parking_spots || 0),
      yearBuilt: data.year_built ? Number(data.year_built) : undefined,
      petsAllowed: Boolean(data.pets_allowed),
      smokingAllowed: Boolean(data.smoking_allowed),
      furnished: Boolean(data.furnished),
      utilitiesIncluded: Boolean(data.utilities_included),
      leaseTerm: data.lease_term || '12_months',
      securityDeposit: Number(data.security_deposit || 0),
    }

    console.log('Transformed property data:', propertyData)

    // Create the property
    const property = await prisma.property.create({
      data: propertyData,
    })

    console.log('Property created:', property)

    // Add images if provided
    if (images.length > 0) {
      const imagePromises = images.map((imageUrl: string, index: number) => {
        return prisma.propertyImage.create({
          data: {
            imageUrl,
            isPrimary: index === 0,
            propertyId: property.id,
          }
        })
      })

      await Promise.all(imagePromises)
    }

    // Get the complete property with images
    const completeProperty = await prisma.property.findUnique({
      where: { id: property.id },
      include: {
        images: true,
      },
    })

    return NextResponse.json(completeProperty)
  } catch (error) {
    console.error('Error creating property:', error)
    return NextResponse.json(
      { error: 'Failed to create property' },
      { status: 500 }
    )
  }
}