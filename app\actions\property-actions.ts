"use server"

import { revalidatePath } from "next/cache"
import { z } from "zod"
import { connectToDatabase } from "@/lib/mongodb"
import { getServerSession } from "next-auth/next"
import { ObjectId } from "mongodb"
import { authOptions } from "@/lib/auth"

const propertySchema = z.object({
  title: z.string().min(1, {
    message: "Title is required.",
  }),
  description: z.string().min(1, {
    message: "Description is required.",
  }),
  price: z.number().min(0, {
    message: "Price must be a positive number.",
  }),
  bedrooms: z.number().min(0, {
    message: "Number of bedrooms must be a positive number.",
  }),
  bathrooms: z.number().min(0, {
    message: "Number of bathrooms must be a positive number.",
  }),
  address: z.string().min(1, {
    message: "Address is required.",
  }),
  city: z.string().min(1, {
    message: "City is required.",
  }),
  state: z.string().min(1, {
    message: "State is required.",
  }),
  postalCode: z.string().min(1, {
    message: "Postal code is required.",
  }),
  images: z.array(z.string()).optional(),
})

export async function createProperty(formData: FormData) {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return { success: false, error: "You must be logged in to create a property" }
    }

    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      price: Number(formData.get("price")),
      bedrooms: Number(formData.get("bedrooms")),
      bathrooms: Number(formData.get("bathrooms")),
      address: formData.get("address") as string,
      city: formData.get("city") as string,
      state: formData.get("state") as string,
      postalCode: formData.get("postalCode") as string,
      images: formData.getAll("images") as string[],
    }

    const validatedData = propertySchema.parse(rawData)
    const { db } = await connectToDatabase()

    const property = {
      ...validatedData,
      landlordId: new ObjectId(session.user.id),
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const result = await db.collection("properties").insertOne(property)

    revalidatePath("/landlord/properties")
    return { success: true, propertyId: result.insertedId }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0].message }
    }
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function updateProperty(propertyId: string, formData: FormData) {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return { success: false, error: "You must be logged in to update a property" }
    }

    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      price: Number(formData.get("price")),
      bedrooms: Number(formData.get("bedrooms")),
      bathrooms: Number(formData.get("bathrooms")),
      address: formData.get("address") as string,
      city: formData.get("city") as string,
      state: formData.get("state") as string,
      postalCode: formData.get("postalCode") as string,
      images: formData.getAll("images") as string[],
    }

    const validatedData = propertySchema.parse(rawData)
    const { db } = await connectToDatabase()

    const updateData = {
      ...validatedData,
      updatedAt: new Date(),
    }

    const result = await db.collection("properties").updateOne(
      { _id: new ObjectId(propertyId), landlordId: new ObjectId(session.user.id) },
      { $set: updateData }
    )

    if (result.matchedCount === 0) {
      return { success: false, error: "Property not found or you don't have permission to update it" }
    }

    revalidatePath("/landlord/properties")
    return { success: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0].message }
    }
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function deleteProperty(propertyId: string) {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return { success: false, error: "You must be logged in to delete a property" }
    }

    const { db } = await connectToDatabase()

    const result = await db.collection("properties").deleteOne({
      _id: new ObjectId(propertyId),
      landlordId: new ObjectId(session.user.id),
    })

    if (result.deletedCount === 0) {
      return { success: false, error: "Property not found or you don't have permission to delete it" }
    }

    revalidatePath("/landlord/properties")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function getProperty(propertyId: string) {
  try {
    const { db } = await connectToDatabase()
    const property = await db.collection("properties").findOne({ _id: new ObjectId(propertyId) })
    
    if (!property) {
      return { success: false, error: "Property not found" }
    }

    return { success: true, property }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function getLandlordProperties() {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return { success: false, error: "You must be logged in to view your properties" }
    }

    const { db } = await connectToDatabase()
    const properties = await db
      .collection("properties")
      .find({ landlordId: new ObjectId(session.user.id) })
      .toArray()

    return { success: true, properties }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function searchProperties(query: string) {
  try {
    const { db } = await connectToDatabase()
    const properties = await db
      .collection("properties")
      .find({
        $or: [
          { title: { $regex: query, $options: "i" } },
          { description: { $regex: query, $options: "i" } },
          { city: { $regex: query, $options: "i" } },
          { state: { $regex: query, $options: "i" } },
          { postalCode: { $regex: query, $options: "i" } },
        ],
      })
      .toArray()

    return { success: true, properties }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function submitProperty(data: any) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      throw new Error("Not authenticated")
    }

    const { db } = await connectToDatabase()
    const result = await db.collection("properties").insertOne({
      ...data,
      userId: session.user.id,
      status: "pending",
      createdAt: new Date(),
    })

    revalidatePath("/dashboard")
    return { success: true, id: result.insertedId }
  } catch (error) {
    console.error("Error submitting property:", error)
    throw error
  }
}

export async function approveProperty(id: string) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isAdmin) {
      throw new Error("Not authorized")
    }

    const { db } = await connectToDatabase()
    await db.collection("properties").updateOne(
      { _id: id },
      { $set: { status: "approved", approvedAt: new Date() } }
    )

    revalidatePath("/dashboard/approvals")
    return { success: true }
  } catch (error) {
    console.error("Error approving property:", error)
    throw error
  }
}

export async function rejectProperty(id: string, reason: string) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isAdmin) {
      throw new Error("Not authorized")
    }

    const { db } = await connectToDatabase()
    await db.collection("properties").updateOne(
      { _id: id },
      { $set: { status: "rejected", rejectionReason: reason, rejectedAt: new Date() } }
    )

    revalidatePath("/dashboard/approvals")
    return { success: true }
  } catch (error) {
    console.error("Error rejecting property:", error)
    throw error
  }
}
