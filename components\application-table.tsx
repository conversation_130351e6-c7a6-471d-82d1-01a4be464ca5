import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ApplicationStatus } from "@/components/application-status"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export function ApplicationTable() {
  // Mock data for applications
  const applications = [
    {
      id: "app-1",
      applicant: "<PERSON>",
      property: "Modern Downtown Apartment",
      date: "2023-04-15",
      status: "pending" as const,
    },
    {
      id: "app-2",
      applicant: "<PERSON>",
      property: "Spacious Family Home",
      date: "2023-04-10",
      status: "approved" as const,
    },
    {
      id: "app-3",
      applicant: "<PERSON>",
      property: "Cozy Studio Apartment",
      date: "2023-04-05",
      status: "rejected" as const,
    },
    {
      id: "app-4",
      applicant: "<PERSON>",
      property: "Luxury Penthouse",
      date: "2023-04-01",
      status: "pending" as const,
    },
    {
      id: "app-5",
      applicant: "<PERSON>",
      property: "Suburban Townhouse",
      date: "2023-03-28",
      status: "approved" as const,
    },
  ]

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Applicant</TableHead>
            <TableHead>Property</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {applications.map((application) => (
            <TableRow key={application.id}>
              <TableCell className="font-medium">{application.applicant}</TableCell>
              <TableCell>{application.property}</TableCell>
              <TableCell>{application.date}</TableCell>
              <TableCell>
                <ApplicationStatus status={application.status} />
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/landlord/applications/${application.id}`}>View</Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
