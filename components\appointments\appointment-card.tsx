'use client';

import React from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from '../ui/card';
import { format, parseISO } from 'date-fns';
import {
  Clock,
  Calendar as CalendarIcon,
  Check,
  X,
  MapPin
} from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { GoogleCalendarButton } from '../ui/google-calendar-button';

interface AppointmentCardProps {
  appointment: {
    id: string;
    title: string;
    description?: string;
    startTime: string;
    endTime: string;
    status: 'scheduled' | 'completed' | 'cancelled';
    propertyId: string;
    propertyTitle?: string;
    propertyAddress?: string;
    googleCalendarLink?: string;
  };
  onCancel?: (id: string) => void;
  onComplete?: (id: string) => void;
}

export function AppointmentCard({ appointment, onCancel, onComplete }: AppointmentCardProps) {
  const statusColors = {
    scheduled: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
  };

  const formatDate = (dateString: string) => {
    const date = parseISO(dateString);
    return format(date, 'MMM d, yyyy');
  };

  const formatTime = (dateString: string) => {
    const date = parseISO(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    return `${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  return (
    <Card className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{appointment.title}</CardTitle>
            <CardDescription>{formatDate(appointment.startTime)}</CardDescription>
          </div>
          <Badge className={statusColors[appointment.status]}>
            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        {appointment.description && (
          <p className="text-sm text-gray-600 mb-3">{appointment.description}</p>
        )}

        <div className="space-y-2">
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 mr-2 text-gray-500" />
            <span>
              {formatTime(appointment.startTime)} - {formatTime(appointment.endTime)}
            </span>
          </div>

          {appointment.propertyTitle && (
            <div className="flex items-center text-sm">
              <CalendarIcon className="h-4 w-4 mr-2 text-gray-500" />
              <span>{appointment.propertyTitle}</span>
            </div>
          )}

          {appointment.propertyAddress && (
            <div className="flex items-center text-sm">
              <MapPin className="h-4 w-4 mr-2 text-gray-500" />
              <span>{appointment.propertyAddress}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-0 flex justify-between gap-2">
        <div className="flex gap-2">
          {appointment.status === 'scheduled' && (
            <>
              {onComplete && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => onComplete(appointment.id)}
                >
                  <Check className="h-4 w-4" />
                  <span>Complete</span>
                </Button>
              )}

              {onCancel && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                  onClick={() => onCancel(appointment.id)}
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </Button>
              )}
            </>
          )}
        </div>

        {appointment.status !== 'cancelled' && (
          <GoogleCalendarButton
            appointmentId={appointment.id}
            title={appointment.title}
            description={appointment.description || ''}
            startTime={appointment.startTime}
            endTime={appointment.endTime}
          />
        )}
      </CardFooter>
    </Card>
  );
}