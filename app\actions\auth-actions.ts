"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { z } from "zod"
import { signIn, signOut } from "next-auth/react"
import { getServerSession } from "next-auth/next"

const signupSchema = z.object({
  firstName: z.string().min(2, {
    message: "First name must be at least 2 characters.",
  }),
  lastName: z.string().min(2, {
    message: "Last name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  postalCode: z.string().min(5, {
    message: "Postal code must be at least 5 characters.",
  }),
  city: z.string().min(2, {
    message: "City must be at least 2 characters.",
  }),
  accountType: z.enum(["renter", "landlord"]),
})

export async function signup(formData: FormData) {
  try {
    const rawData = {
      firstName: formData.get("firstName") as string,
      lastName: formData.get("lastName") as string,
      email: formData.get("email") as string,
      password: formData.get("password") as string,
      postalCode: formData.get("postalCode") as string,
      city: formData.get("city") as string,
      accountType: formData.get("accountType") as string,
    }

    const validatedData = signupSchema.parse(rawData)
    
    // Send signup request to your backend API
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validatedData),
    })

    const data = await response.json()
    
    if (!response.ok) {
      return { success: false, error: data.message || "Failed to sign up" }
    }

    revalidatePath("/dashboard")
    return { success: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0].message }
    }
    return { success: false, error: "An unexpected error occurred" }
  }
}

const loginSchema = z.object({
  email: z.string().min(1, {
    message: "Email is required.",
  }),
  password: z.string().min(1, {
    message: "Password is required.",
  }),
})

export async function login(formData: FormData) {
  try {
    const rawData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    }

    const validatedData = loginSchema.parse(rawData)

    // Special case for admin login
    if (validatedData.email === "admin" && validatedData.password === "admin") {
      const result = await signIn("credentials", {
        email: "<EMAIL>",
        password: "admin",
        redirect: false,
      })

      if (result?.error) {
        return { success: false, error: result.error }
      }

      revalidatePath("/dashboard")
      redirect("/admin/dashboard")
      return { success: true }
    }

    const result = await signIn("credentials", {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    })

    if (result?.error) {
      return { success: false, error: result.error }
    }

    revalidatePath("/dashboard")
    return { success: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0].message }
    }
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function logout() {
  await signOut({ redirect: true, callbackUrl: "/" })
  revalidatePath("/")
}

export async function updateProfile(formData: FormData) {
  try {
    const session = await getServerSession()

    if (!session?.user) {
      return { success: false, error: "You must be logged in to update your profile" }
    }

    const updateData = {
      firstName: formData.get("firstName") as string,
      lastName: formData.get("lastName") as string,
      phone: formData.get("phone") as string,
      postalCode: formData.get("postalCode") as string,
      city: formData.get("city") as string,
      bio: formData.get("bio") as string,
    }

    // Send update request to your backend API
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/users/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.user.id}`,
      },
      body: JSON.stringify(updateData),
    })

    const data = await response.json()
    
    if (!response.ok) {
      return { success: false, error: data.message || "Failed to update profile" }
    }

    revalidatePath("/dashboard/profile")
    return { success: true }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred" }
  }
}
