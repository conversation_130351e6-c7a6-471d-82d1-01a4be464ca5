"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AnalyticsClient from "./analytics-client"
import { useEffect, useState } from "react"

export default function AnalyticsPage() {
  const { data: session, status } = useSession()
  const [propertyStats, setPropertyStats] = useState({
    total: 0,
    approved: 0,
    pending: 0,
    rented: 0
  })
  const [applicationStats, setApplicationStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  })

  useEffect(() => {
    if (status === "unauthenticated") {
      redirect("/auth/login?redirect=/dashboard/analytics")
    }

    if (session?.user) {
      // Fetch property stats
      fetch('/api/properties/stats')
        .then(res => res.json())
        .then(data => setPropertyStats(data))

      // Fetch application stats
      fetch('/api/applications/stats')
        .then(res => res.json())
        .then(data => setApplicationStats(data))
    }
  }, [session, status])

  if (status === "loading") {
    return <div>Loading...</div>
  }

  return (
    <DashboardLayout>
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <h1 className="text-3xl font-bold mb-8">Analytics Dashboard</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Property Overview</CardTitle>
              <CardDescription>Summary of your property listings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Total Properties</p>
                  <p className="text-2xl font-bold">{propertyStats.total}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Active Listings</p>
                  <p className="text-2xl font-bold">{propertyStats.approved}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Pending Approval</p>
                  <p className="text-2xl font-bold">{propertyStats.pending}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Rented</p>
                  <p className="text-2xl font-bold">{propertyStats.rented}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Application Overview</CardTitle>
              <CardDescription>Summary of rental applications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                  <p className="text-2xl font-bold">{applicationStats.total}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                  <p className="text-2xl font-bold">{applicationStats.pending}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Approved</p>
                  <p className="text-2xl font-bold">{applicationStats.approved}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                  <p className="text-2xl font-bold">{applicationStats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Client component for interactive charts */}
        <AnalyticsClient propertyStats={propertyStats} applicationStats={applicationStats} />
      </div>
    </DashboardLayout>
  )
}
