import { useEffect, useState } from 'react';
import { Socket } from 'socket.io-client';

interface TypingIndicatorProps {
  socket: Socket | null;
  conversationId: string;
  otherUserId: string;
}

export function TypingIndicator({ socket, conversationId, otherUserId }: TypingIndicatorProps) {
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!socket) return;

    const handleTypingStart = (data: { userId: string; conversationId: string }) => {
      if (data.userId === otherUserId && data.conversationId === conversationId) {
        setIsTyping(true);
        
        // Clear existing timeout
        if (typingTimeout) {
          clearTimeout(typingTimeout);
        }
        
        // Set new timeout to clear typing indicator after 3 seconds
        const timeout = setTimeout(() => {
          setIsTyping(false);
        }, 3000);
        
        setTypingTimeout(timeout);
      }
    };

    const handleTypingStop = (data: { userId: string; conversationId: string }) => {
      if (data.userId === otherUserId && data.conversationId === conversationId) {
        setIsTyping(false);
      }
    };

    socket.on('typing_start', handleTypingStart);
    socket.on('typing_stop', handleTypingStop);

    return () => {
      socket.off('typing_start', handleTypingStart);
      socket.off('typing_stop', handleTypingStop);
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    };
  }, [socket, conversationId, otherUserId, typingTimeout]);

  if (!isTyping) return null;

  return (
    <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 p-2">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
      <span>typing...</span>
    </div>
  );
} 