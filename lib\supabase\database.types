export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          auth_id: string | null
          first_name: string | null
          last_name: string | null
          email: string | null
          phone: string | null
          postal_code: string | null
          city: string | null
          bio: string | null
          is_landlord: boolean | null
          created_at: string | null
          updated_at: string | null
          is_admin: boolean | null
          status: string | null
        }
        Insert: {
          id?: string
          auth_id?: string | null
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          phone?: string | null
          postal_code?: string | null
          city?: string | null
          bio?: string | null
          is_landlord?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          is_admin?: boolean | null
          status?: string | null
        }
        Update: {
          id?: string
          auth_id?: string | null
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          phone?: string | null
          postal_code?: string | null
          city?: string | null
          bio?: string | null
          is_landlord?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          is_admin?: boolean | null
          status?: string | null
        }
      }
      properties: {
        Row: {
          id: string
          title: string | null
          description: string | null
          address: string | null
          city: string | null
          province: string | null
          postal_code: string | null
          property_type: string | null
          bedrooms: number | null
          bathrooms: number | null
          square_feet: number | null
          price: number | null
          available_from: string | null
          landlord_id: string | null
          status: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          title?: string | null
          description?: string | null
          address?: string | null
          city?: string | null
          province?: string | null
          postal_code?: string | null
          property_type?: string | null
          bedrooms?: number | null
          bathrooms?: number | null
          square_feet?: number | null
          price?: number | null
          available_from?: string | null
          landlord_id?: string | null
          status?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          title?: string | null
          description?: string | null
          address?: string | null
          city?: string | null
          province?: string | null
          postal_code?: string | null
          property_type?: string | null
          bedrooms?: number | null
          bathrooms?: number | null
          square_feet?: number | null
          price?: number | null
          available_from?: string | null
          landlord_id?: string | null
          status?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      property_images: {
        Row: {
          id: string
          property_id: string | null
          image_url: string | null
          is_primary: boolean | null
          created_at: string | null
        }
        Insert: {
          id?: string
          property_id?: string | null
          image_url?: string | null
          is_primary?: boolean | null
          created_at?: string | null
        }
        Update: {
          id?: string
          property_id?: string | null
          image_url?: string | null
          is_primary?: boolean | null
          created_at?: string | null
        }
      }
      amenities: {
        Row: {
          id: string
          name: string | null
          icon: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          name?: string | null
          icon?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          name?: string | null
          icon?: string | null
          created_at?: string | null
        }
      }
      property_amenities: {
        Row: {
          property_id: string | null
          amenity_id: string | null
        }
        Insert: {
          property_id?: string | null
          amenity_id?: string | null
        }
        Update: {
          property_id?: string | null
          amenity_id?: string | null
        }
      }
      applications: {
        Row: {
          id: string
          property_id: string | null
          user_id: string | null
          status: string | null
          message: string | null
          applied_date: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          property_id?: string | null
          user_id?: string | null
          status?: string | null
          message?: string | null
          applied_date?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          property_id?: string | null
          user_id?: string | null
          status?: string | null
          message?: string | null
          applied_date?: string | null
          updated_at?: string | null
        }
      }
      approvals: {
        Row: {
          id: string
          property_id: string | null
          admin_id: string | null
          status: string | null
          rejection_reason: string | null
          approved_at: string | null
          rejected_at: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          property_id?: string | null
          admin_id?: string | null
          status?: string | null
          rejection_reason?: string | null
          approved_at?: string | null
          rejected_at?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          property_id?: string | null
          admin_id?: string | null
          status?: string | null
          rejection_reason?: string | null
          approved_at?: string | null
          rejected_at?: string | null
          created_at?: string | null
        }
      }
      payments: {
        Row: {
          id: string
          application_id: string | null
          amount: number | null
          status: string | null
          payment_date: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          application_id?: string | null
          amount?: number | null
          status?: string | null
          payment_date?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          application_id?: string | null
          amount?: number | null
          status?: string | null
          payment_date?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      admin_users: {
        Row: {
          id: string
          role: string | null
          created_at: string | null
        }
        Insert: {
          id: string
          role?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          role?: string | null
          created_at?: string | null
        }
      }
      saved_properties: {
        Row: {
          id: string
          user_id: string | null
          property_id: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          property_id?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          property_id?: string | null
          created_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
