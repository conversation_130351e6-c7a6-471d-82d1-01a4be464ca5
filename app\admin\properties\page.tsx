'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { Loader2, ArrowLeft, CheckCircle, XCircle, Eye } from 'lucide-react'
import Link from 'next/link'

interface Property {
  id: string
  title: string
  city: string
  province: string
  propertyType: string
  price: number
  status: 'pending' | 'approved' | 'declined'
  createdAt: string
}

export default function AdminPropertiesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [properties, setProperties] = useState<Property[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/login')
      return
    }

    // Check if user is admin
    const checkAdmin = async () => {
      try {
        const response = await fetch('/api/auth/check-admin')
        const data = await response.json()
        setIsAdmin(data.isAdmin)

        if (data.isAdmin) {
          fetchProperties()
        }
      } catch (error) {
        console.error('Error checking admin status:', error)
        setIsAdmin(false)
        setIsLoading(false)
      }
    }

    checkAdmin()
  }, [session, status, router])

  const fetchProperties = async () => {
    try {
      // Use the admin-specific API route that returns all properties regardless of status
      const response = await fetch('/api/admin/properties')
      const data = await response.json()
      setProperties(data)
    } catch (error) {
      console.error('Error fetching properties:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch properties',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleApprove = async (propertyId: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/properties/${propertyId}/approve`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to approve property')
      }

      toast({
        title: 'Success',
        description: 'Property approved successfully',
      })

      fetchProperties()
    } catch (error) {
      console.error('Error approving property:', error)
      toast({
        title: 'Error',
        description: 'Failed to approve property',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDecline = async (propertyId: string) => {
    try {
      setIsLoading(true)
      const reason = prompt('Please enter a reason for declining:')

      if (!reason) return

      const response = await fetch(`/api/properties/${propertyId}/decline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      })

      if (!response.ok) {
        throw new Error('Failed to decline property')
      }

      toast({
        title: 'Success',
        description: 'Property declined successfully',
      })

      fetchProperties()
    } catch (error) {
      console.error('Error declining property:', error)
      toast({
        title: 'Error',
        description: 'Failed to decline property',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="container py-8">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>You do not have permission to access this page.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/">Return to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="flex items-center mb-8">
        <Button variant="ghost" className="mr-4" asChild>
          <Link href="/admin" legacyBehavior>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin Dashboard
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Manage Properties</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Properties</CardTitle>
          <CardDescription>View and manage all properties in the system</CardDescription>
        </CardHeader>
        <CardContent>
          {properties.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No properties found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.map((property) => (
                  <TableRow key={property.id}>
                    <TableCell className="font-medium">{property.title}</TableCell>
                    <TableCell>{property.city}, {property.province}</TableCell>
                    <TableCell>{property.propertyType}</TableCell>
                    <TableCell>${property.price}/mo</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          property.status === 'approved' ? 'success' :
                          property.status === 'declined' ? 'destructive' :
                          'default'
                        }
                      >
                        {property.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" asChild>
                          <Link href={`/properties/${property.id}`} legacyBehavior>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Link>
                        </Button>
                        {property.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              variant="default"
                              onClick={() => handleApprove(property.id)}
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDecline(property.id)}
                            >
                              <XCircle className="mr-2 h-4 w-4" />
                              Decline
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
