const ALPHA_VANTAGE_API_KEY = "demo" // Using demo key for development

export interface MarketData {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  timestamp: string
}

export async function getMarketData(symbol: string): Promise<MarketData> {
  try {
    const response = await fetch(
      `https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${ALPHA_VANTAGE_API_KEY}`
    )

    if (!response.ok) {
      throw new Error("Failed to fetch market data")
    }

    const data = await response.json()
    const quote = data["Global Quote"]

    return {
      symbol: quote["01. symbol"],
      price: parseFloat(quote["05. price"]),
      change: parseFloat(quote["09. change"]),
      changePercent: parseFloat(quote["10. change percent"]),
      volume: parseInt(quote["06. volume"]),
      timestamp: quote["07. latest trading day"],
    }
  } catch (error) {
    console.error("Error fetching market data:", error)
    throw error
  }
}

export async function getMarketOverview(): Promise<MarketData[]> {
  const symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "META"]
  const promises = symbols.map((symbol) => getMarketData(symbol))
  return Promise.all(promises)
} 