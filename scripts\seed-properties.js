// Script to seed the database with sample rental properties across Canada
require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const path = require('path');
const fs = require('fs');

// Since the canadian-rental-data.ts is an ES module, we'll define the data here
// These are copied from lib/api/canadian-rental-data.ts
const CANADIAN_CITIES = [
  "Toronto",
  "Vancouver",
  "Montreal",
  "Calgary",
  "Edmonton",
  "Ottawa",
  "Winnipeg",
  "Quebec City",
  "Hamilton",
  "Halifax",
  "Victoria",
  "Kitchener",
  "London",
  "Oshawa",
  "Windsor",
  "Saskatoon",
  "Regina",
  "St. John's",
  "Kelowna",
  "Abbotsford",
];

// Base rent by city (approximate average 2-bedroom rents)
const CITY_BASE_RENTS = {
  Toronto: 2800,
  Vancouver: 3000,
  Montreal: 1800,
  Calgary: 1600,
  Edmonton: 1500,
  Ottawa: 1900,
  Winnipeg: 1400,
  "Quebec City": 1300,
  Hamilton: 1800,
  Halifax: 1700,
  Victoria: 2200,
  Kitchener: 1700,
  London: 1600,
  Oshawa: 1700,
  Windsor: 1300,
  Saskatoon: 1300,
  Regina: 1200,
  "St. John's": 1300,
  Kelowna: 1900,
  Abbotsford: 1700,
};

// Provinces by city
const CITY_PROVINCES = {
  Toronto: "ON",
  Vancouver: "BC",
  Montreal: "QC",
  Calgary: "AB",
  Edmonton: "AB",
  Ottawa: "ON",
  Winnipeg: "MB",
  "Quebec City": "QC",
  Hamilton: "ON",
  Halifax: "NS",
  Victoria: "BC",
  Kitchener: "ON",
  London: "ON",
  Oshawa: "ON",
  Windsor: "ON",
  Saskatoon: "SK",
  Regina: "SK",
  "St. John's": "NL",
  Kelowna: "BC",
  Abbotsford: "BC",
};

// Common street names by province
const STREET_NAMES_BY_PROVINCE = {
  ON: ["Yonge", "Queen", "King", "Bloor", "Dundas", "Bay", "College", "Spadina", "Parliament", "Jarvis"],
  BC: ["Robson", "Granville", "Davie", "Denman", "Burrard", "Hastings", "Main", "Broadway", "Cambie", "Georgia"],
  QC: [
    "Saint-Laurent",
    "Sainte-Catherine",
    "Sherbrooke",
    "René-Lévesque",
    "Crescent",
    "Peel",
    "Saint-Denis",
    "Mont-Royal",
    "Maisonneuve",
    "Papineau",
  ],
  AB: [
    "Jasper",
    "Whyte",
    "Calgary Trail",
    "Stony Plain",
    "17 Avenue",
    "Macleod Trail",
    "Deerfoot Trail",
    "Crowchild",
    "Memorial",
    "Bow Trail",
  ],
  MB: [
    "Portage",
    "Main",
    "Pembina",
    "Henderson",
    "Osborne",
    "Corydon",
    "St. Mary's",
    "Regent",
    "St. Anne's",
    "Kenaston",
  ],
  NS: [
    "Spring Garden",
    "Barrington",
    "Quinpool",
    "Robie",
    "Gottingen",
    "Young",
    "Windsor",
    "Oxford",
    "Coburg",
    "Jubilee",
  ],
  default: ["Main", "First", "Second", "Third", "Fourth", "Fifth", "Maple", "Oak", "Pine", "Cedar"],
};

// Property types with realistic distribution
const PROPERTY_TYPES = [
  { type: "Apartment", weight: 50 },
  { type: "Condo", weight: 20 },
  { type: "House", weight: 15 },
  { type: "Townhouse", weight: 10 },
  { type: "Basement Suite", weight: 5 },
];

// Common amenities
const AMENITIES = [
  { name: "In-unit Laundry", probability: 0.7 },
  { name: "Dishwasher", probability: 0.8 },
  { name: "Air Conditioning", probability: 0.5 },
  { name: "Balcony", probability: 0.6 },
  { name: "Parking", probability: 0.75 },
  { name: "Pet Friendly", probability: 0.4 },
  { name: "Gym", probability: 0.3 },
  { name: "Pool", probability: 0.15 },
  { name: "Storage", probability: 0.65 },
  { name: "Elevator", probability: 0.5 },
  { name: "Security System", probability: 0.45 },
  { name: "Furnished", probability: 0.2 },
];

// Property descriptions
const PROPERTY_DESCRIPTIONS = [
  "Bright and spacious {bedrooms} bedroom {type} in the heart of {city}. Features include hardwood floors, stainless steel appliances, and a private balcony with city views.",
  "Beautiful {bedrooms} bedroom {type} in a quiet neighborhood. Recently renovated with modern finishes, open concept living area, and updated kitchen.",
  "Charming {bedrooms} bedroom {type} in a well-maintained building. Includes in-suite laundry, updated bathroom, and a spacious kitchen with breakfast bar.",
  "Stunning {bedrooms} bedroom {type} with panoramic views. Features high ceilings, large windows, and a gourmet kitchen with granite countertops.",
  "Cozy {bedrooms} bedroom {type} in a convenient location. Walking distance to shops, restaurants, and public transit. Includes parking and storage locker.",
  "Modern {bedrooms} bedroom {type} with open concept design. Features include quartz countertops, stainless steel appliances, and in-suite laundry.",
  "Spacious {bedrooms} bedroom {type} in a family-friendly neighborhood. Close to schools, parks, and shopping. Includes private yard and garage.",
  "Luxurious {bedrooms} bedroom {type} with high-end finishes. Features include hardwood floors, custom cabinetry, and a spa-like bathroom.",
  "Bright corner {bedrooms} bedroom {type} with lots of natural light. Recently updated with new flooring, paint, and appliances.",
  "Character-filled {bedrooms} bedroom {type} in a heritage building. Features original hardwood floors, high ceilings, and modern updates.",
];

// Helper function to get a weighted random property type
function getRandomPropertyType() {
  const totalWeight = PROPERTY_TYPES.reduce((sum, item) => sum + item.weight, 0);
  let random = Math.random() * totalWeight;

  for (const item of PROPERTY_TYPES) {
    random -= item.weight;
    if (random <= 0) {
      return item.type;
    }
  }

  return PROPERTY_TYPES[0].type;
}

// Helper function to get random amenities
function getRandomAmenities() {
  return AMENITIES.filter((amenity) => Math.random() < amenity.probability).map((amenity) => amenity.name);
}

// Helper function to generate a property description
function generatePropertyDescription(bedrooms, type, city) {
  const template = PROPERTY_DESCRIPTIONS[Math.floor(Math.random() * PROPERTY_DESCRIPTIONS.length)];
  return template
    .replace("{bedrooms}", bedrooms.toString())
    .replace("{type}", type.toLowerCase())
    .replace("{city}", city);
}

// Helper function to generate a random postal code for a given province
function generatePostalCode(province) {
  const firstLetterByProvince = {
    ON: "KLMNP",
    QC: "GHJ",
    BC: "VX",
    AB: "T",
    MB: "R",
    SK: "S",
    NS: "B",
    NB: "E",
    NL: "A",
    PE: "C",
    YT: "Y",
    NT: "X",
    NU: "X",
  };

  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const firstLetter = firstLetterByProvince[province]
    ? firstLetterByProvince[province][Math.floor(Math.random() * firstLetterByProvince[province].length)]
    : letters[Math.floor(Math.random() * letters.length)];

  const number1 = Math.floor(Math.random() * 10);
  const letter2 = letters[Math.floor(Math.random() * letters.length)];
  const number2 = Math.floor(Math.random() * 10);
  const letter3 = letters[Math.floor(Math.random() * letters.length)];
  const number3 = Math.floor(Math.random() * 10);

  return `${firstLetter}${number1}${letter2} ${number2}${letter3}${number3}`;
}

// Helper function to generate a random street address
function generateStreetAddress(province) {
  const streetNames = STREET_NAMES_BY_PROVINCE[province] || STREET_NAMES_BY_PROVINCE["default"];
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  const streetNumber = Math.floor(Math.random() * 9000) + 1000;

  const streetTypes = ["St", "Ave", "Rd", "Blvd", "Cres", "Dr", "Way"];
  const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)];

  const units = ["", "", "", "", "Unit " + Math.floor(Math.random() * 100 + 1) + ", "];
  const unit = units[Math.floor(Math.random() * units.length)];

  return `${unit}${streetNumber} ${streetName} ${streetType}`;
}

// Generate a list of mock properties for a given city
function generatePropertiesForCity(city, count = 10) {
  const province = CITY_PROVINCES[city] || "ON";
  const baseRent = CITY_BASE_RENTS[city] || 1800;

  return Array.from({ length: count }, (_, i) => {
    const bedrooms = Math.floor(Math.random() * 3) + 1; // 1-3 bedrooms
    const bathrooms = Math.min(bedrooms, Math.floor(Math.random() * 2) + 1); // 1-2 bathrooms, not more than bedrooms
    const propertyType = getRandomPropertyType();
    const squareFeet = 500 + bedrooms * 200 + Math.floor(Math.random() * 300);

    // Adjust rent based on property characteristics
    const bedroomFactor = (bedrooms - 1) * 300;
    const bathroomFactor = (bathrooms - 1) * 200;
    const sizeFactor = ((squareFeet - 700) / 100) * 50;
    const propertyTypeFactor =
      propertyType === "Apartment"
        ? 0
        : propertyType === "Condo"
          ? 200
          : propertyType === "Townhouse"
            ? 300
            : propertyType === "House"
              ? 500
              : -100; // Basement Suite

    const rent = Math.round(baseRent + bedroomFactor + bathroomFactor + sizeFactor + propertyTypeFactor);

    // Add some randomness (±10%)
    const randomFactor = 1 + (Math.random() * 0.2 - 0.1);
    const finalRent = Math.round(rent * randomFactor);

    const streetAddress = generateStreetAddress(province);
    const postalCode = generatePostalCode(province);

    return {
      id: `prop-${city.toLowerCase().replace(/\s+/g, "-")}-${i + 1}`,
      title: `${bedrooms} Bedroom ${propertyType} in ${city}`,
      description: generatePropertyDescription(bedrooms, propertyType, city),
      address: streetAddress,
      city: city,
      province: province,
      postalCode: postalCode,
      propertyType: propertyType,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      squareFeet: squareFeet,
      price: finalRent,
      amenities: getRandomAmenities(),
      availableFrom: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // Random date in next 30 days
      images: Array.from(
        { length: Math.floor(Math.random() * 4) + 2 },
        (_, i) => `/placeholder.svg?height=400&width=600&text=${encodeURIComponent(`${city} Property ${i + 1}`)}`,
      ),
      status: "available",
      createdAt: new Date().toISOString(),
    };
  });
}

// MongoDB connection
async function connectToMongoDB() {
  const uri = process.env.MONGODB_URI;

  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }

  console.log('Connecting to MongoDB...');
  console.log('URI:', uri.replace(/\/\/[^:]+:[^@]+@/, '//****:****@')); // Hide credentials

  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    await client.connect();
    console.log('Successfully connected to MongoDB!');
    return client;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    throw error;
  }
}

// Create a landlord user if one doesn't exist
async function ensureLandlordExists(db) {
  const usersCollection = db.collection('users');

  // Check if we have a landlord user
  const existingLandlord = await usersCollection.findOne({ isLandlord: true });

  if (existingLandlord) {
    console.log(`Using existing landlord: ${existingLandlord.firstName} ${existingLandlord.lastName} (${existingLandlord._id})`);
    return existingLandlord;
  }

  // Create a new landlord user
  const newLandlord = {
    firstName: 'Sample',
    lastName: 'Landlord',
    email: '<EMAIL>',
    isLandlord: true,
    role: 'landlord',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await usersCollection.insertOne(newLandlord);
  console.log(`Created new landlord user with ID: ${result.insertedId}`);

  return { ...newLandlord, _id: result.insertedId };
}

// Ensure amenities exist in the database
async function ensureAmenitiesExist(db) {
  const amenitiesCollection = db.collection('amenities');

  // Get existing amenities
  const existingAmenities = await amenitiesCollection.find({}).toArray();

  if (existingAmenities.length > 0) {
    console.log(`Found ${existingAmenities.length} existing amenities`);
    return existingAmenities;
  }

  // Create amenities
  const amenities = [
    { name: 'Air Conditioning', icon: 'ac_unit', category: 'Climate Control' },
    { name: 'Heating', icon: 'local_fire_department', category: 'Climate Control' },
    { name: 'Washer/Dryer', icon: 'local_laundry_service', category: 'Appliances' },
    { name: 'Dishwasher', icon: 'countertops', category: 'Appliances' },
    { name: 'Refrigerator', icon: 'kitchen', category: 'Appliances' },
    { name: 'Stove/Oven', icon: 'microwave', category: 'Appliances' },
    { name: 'Microwave', icon: 'microwave', category: 'Appliances' },
    { name: 'Parking', icon: 'local_parking', category: 'Exterior' },
    { name: 'Garage', icon: 'garage', category: 'Exterior' },
    { name: 'Balcony', icon: 'deck', category: 'Exterior' },
    { name: 'Patio', icon: 'deck', category: 'Exterior' },
    { name: 'Garden', icon: 'yard', category: 'Exterior' },
    { name: 'Pool', icon: 'pool', category: 'Recreation' },
    { name: 'Gym', icon: 'fitness_center', category: 'Recreation' },
    { name: 'Pet Friendly', icon: 'pets', category: 'Policies' },
    { name: 'Furnished', icon: 'chair', category: 'Interior' },
    { name: 'Hardwood Floors', icon: 'weekend', category: 'Interior' },
    { name: 'Carpet', icon: 'weekend', category: 'Interior' },
    { name: 'High Ceilings', icon: 'height', category: 'Interior' },
    { name: 'Fireplace', icon: 'fireplace', category: 'Interior' },
    { name: 'Storage Space', icon: 'inventory_2', category: 'Storage' },
    { name: 'Walk-in Closet', icon: 'door_sliding', category: 'Storage' },
    { name: 'Wheelchair Accessible', icon: 'accessible', category: 'Accessibility' },
    { name: 'In-unit Laundry', icon: 'local_laundry_service', category: 'Appliances' },
    { name: 'Security System', icon: 'security', category: 'Security' },
    { name: 'Elevator', icon: 'elevator', category: 'Building' },
  ];

  const result = await amenitiesCollection.insertMany(amenities);
  console.log(`Created ${result.insertedCount} amenities`);

  return await amenitiesCollection.find({}).toArray();
}

// Generate realistic image URLs for properties
function generateImageUrls(city, propertyType, count = 5) {
  const baseUrls = [
    'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267', // Apartment 1
    'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688', // Apartment 2
    'https://images.unsplash.com/photo-1493809842364-78817add7ffb', // Apartment 3
    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750', // House 1
    'https://images.unsplash.com/photo-1570129477492-45c003edd2be', // House 2
    'https://images.unsplash.com/photo-1568605114967-8130f3a36994', // House 3
    'https://images.unsplash.com/photo-1554995207-c18c203602cb', // Condo 1
    'https://images.unsplash.com/photo-1536376072261-38c75010e6c9', // Condo 2
    'https://images.unsplash.com/photo-1594484208280-efa00f96fc21', // Townhouse
    'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2', // Basement
  ];

  // Select appropriate images based on property type
  let typeSpecificUrls = [];
  if (propertyType.toLowerCase().includes('apartment')) {
    typeSpecificUrls = baseUrls.slice(0, 3);
  } else if (propertyType.toLowerCase().includes('house')) {
    typeSpecificUrls = baseUrls.slice(3, 6);
  } else if (propertyType.toLowerCase().includes('condo')) {
    typeSpecificUrls = baseUrls.slice(6, 8);
  } else if (propertyType.toLowerCase().includes('townhouse')) {
    typeSpecificUrls = [baseUrls[8]];
  } else if (propertyType.toLowerCase().includes('basement')) {
    typeSpecificUrls = [baseUrls[9]];
  } else {
    // Mix of all
    typeSpecificUrls = baseUrls;
  }

  // Generate the requested number of images
  const images = [];
  for (let i = 0; i < count; i++) {
    const baseUrl = typeSpecificUrls[i % typeSpecificUrls.length];
    // Add some query params to make each URL unique
    const url = `${baseUrl}?w=800&h=600&fit=crop&city=${encodeURIComponent(city)}&unit=${i}`;
    images.push({
      imageUrl: url,
      isPrimary: i === 0, // First image is primary
    });
  }

  return images;
}

// Map amenity names to IDs
function mapAmenitiesToIds(amenityNames, allAmenities) {
  return amenityNames.map(name => {
    const amenity = allAmenities.find(a => a.name === name);
    return amenity ? amenity._id : null;
  }).filter(id => id !== null);
}

// Main function to seed the database
async function seedDatabase() {
  let client;

  try {
    console.log('Starting database seeding process...');
    client = await connectToMongoDB();
    console.log('Getting database reference...');
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    console.log(`Using database: ${process.env.MONGODB_DB || 'rentcentral'}`);

    // Ensure we have a landlord user
    console.log('Ensuring landlord user exists...');
    const landlord = await ensureLandlordExists(db);
    console.log(`Landlord user: ${landlord._id}`);

    // Ensure amenities exist
    console.log('Ensuring amenities exist...');
    const amenities = await ensureAmenitiesExist(db);
    console.log(`Found ${amenities.length} amenities`);

    // Get collections
    console.log('Getting collection references...');
    const propertiesCollection = db.collection('properties');
    const propertyImagesCollection = db.collection('propertyImages');
    const propertyAmenitiesCollection = db.collection('propertyAmenities');

    // Check if we already have properties
    console.log('Checking for existing properties...');
    const existingPropertiesCount = await propertiesCollection.countDocuments();
    if (existingPropertiesCount > 0) {
      console.log(`Database already has ${existingPropertiesCount} properties.`);
      const proceed = process.argv.includes('--force');

      if (!proceed) {
        console.log('Use --force to add more properties anyway.');
        return;
      }

      console.log('Proceeding to add more properties...');
    }

    // Generate properties for each city
    let totalPropertiesAdded = 0;

    // For testing, let's just use a few cities first
    const citiesToSeed = process.argv.includes('--all') ? CANADIAN_CITIES : CANADIAN_CITIES.slice(0, 3);
    console.log(`Will seed properties for ${citiesToSeed.length} cities: ${citiesToSeed.join(', ')}`);

    for (const city of citiesToSeed) {
      try {
        // Generate 2-4 properties per city for testing
        const count = Math.floor(Math.random() * 3) + 2; // 2-4 properties
        console.log(`Generating ${count} properties for ${city}...`);

        const cityProperties = generatePropertiesForCity(city, count);
        console.log(`Generated ${cityProperties.length} properties for ${city}`);

        // Insert each property
        for (const property of cityProperties) {
          try {
            console.log(`Adding property: ${property.title}`);

            // Format property for MongoDB
            const formattedProperty = {
              title: property.title,
              description: property.description,
              address: property.address,
              city: property.city,
              province: property.province,
              postalCode: property.postalCode,
              propertyType: property.propertyType,
              bedrooms: property.bedrooms,
              bathrooms: property.bathrooms,
              squareFeet: property.squareFeet,
              price: property.price,
              availableFrom: new Date(property.availableFrom),
              landlordId: landlord._id,
              status: Math.random() > 0.3 ? 'approved' : 'pending', // 70% approved, 30% pending
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            // Insert property
            console.log('Inserting property into database...');
            const result = await propertiesCollection.insertOne(formattedProperty);
            const propertyId = result.insertedId;
            console.log(`Property inserted with ID: ${propertyId}`);

            // Generate and insert images
            console.log('Generating property images...');
            const images = generateImageUrls(city, property.propertyType, Math.floor(Math.random() * 2) + 2); // 2-3 images
            console.log(`Generated ${images.length} images for property`);

            for (const image of images) {
              await propertyImagesCollection.insertOne({
                propertyId: propertyId,
                imageUrl: image.imageUrl,
                isPrimary: image.isPrimary,
                createdAt: new Date(),
              });
            }

            // Map amenities and insert property amenities
            console.log('Adding property amenities...');
            const amenityIds = mapAmenitiesToIds(property.amenities, amenities);
            console.log(`Adding ${amenityIds.length} amenities to property`);

            for (const amenityId of amenityIds) {
              await propertyAmenitiesCollection.insertOne({
                propertyId: propertyId,
                amenityId: amenityId,
                createdAt: new Date(),
              });
            }

            totalPropertiesAdded++;
            console.log(`Successfully added property ${totalPropertiesAdded}`);
          } catch (propertyError) {
            console.error(`Error adding property ${property.title}:`, propertyError);
          }
        }
      } catch (cityError) {
        console.error(`Error processing city ${city}:`, cityError);
      }
    }

    console.log(`Successfully added ${totalPropertiesAdded} properties across ${CANADIAN_CITIES.length} Canadian cities!`);

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('Database connection closed.');
    }
  }
}

// Run the seed function
seedDatabase().catch(console.error);
