// Script to approve all properties in the database using Prisma
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

async function approveProperties() {
  console.log('Initializing Prisma client...');
  const prisma = new PrismaClient();
  
  try {
    // Count pending properties
    const pendingCount = await prisma.property.count({
      where: { status: 'pending' }
    });
    
    console.log(`Found ${pendingCount} pending properties`);
    
    if (pendingCount > 0) {
      // Update all pending properties to approved
      const result = await prisma.property.updateMany({
        where: { status: 'pending' },
        data: { status: 'approved' }
      });
      
      console.log(`Updated ${result.count} properties to 'approved' status`);
    }
    
    // Count properties by status
    const statusCounts = await prisma.property.groupBy({
      by: ['status'],
      _count: true
    });
    
    console.log('\nProperties by status:');
    statusCounts.forEach(status => {
      console.log(`- ${status.status}: ${status._count}`);
    });
    
  } catch (error) {
    console.error('Error approving properties:', error);
  } finally {
    await prisma.$disconnect();
    console.log('\nPrisma client disconnected');
  }
}

approveProperties().catch(console.error);
