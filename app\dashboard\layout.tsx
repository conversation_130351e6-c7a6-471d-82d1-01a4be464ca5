"use client"

import { useAuth } from "@/components/auth-provider"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, Building } from "lucide-react"
import Link from "next/link"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { logout } = useAuth()

  return (
    <div className="min-h-screen bg-[#0f172a]">
      <header className="bg-[#1e293b] border-b border-primary/20 shadow-md sticky top-0 z-30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <Link href="/" className="flex items-center gap-2">
            <Building className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold font-montserrat text-white">
              Rent<span className="text-primary">Central</span>
            </h1>
          </Link>
          <Button
            variant="outline"
            onClick={logout}
            className="flex items-center gap-2 border-primary/20 hover:bg-primary/20 text-white"
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </header>
      <div className="bg-[url('/dashboard-pattern.svg')] bg-fixed bg-repeat opacity-10 absolute inset-0 pointer-events-none"></div>
      <main className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  );
}