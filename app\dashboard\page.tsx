"use client"

import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import Link from "next/link"
import {
  Building2,
  FileCheck,
  Heart,
  TrendingUp,
  Home,
  Calendar,
  Clock,
  Plus,
  ChevronRight,
  Activity
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { PropertyCard } from "@/components/property-card"

// Sample property for recent activity
const recentProperties = [
  {
    id: "recent-1",
    title: "Modern Loft in Gastown",
    address: "123 Water Street",
    city: "Vancouver",
    province: "BC",
    price: 2100,
    bedrooms: 1,
    bathrooms: 1,
    squareFeet: 750,
    imageUrl: "https://images.unsplash.com/photo-1502672023488-70e25813eb80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8YXBhcnRtZW50JTIwbG9mdHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=1000&q=60",
    isNew: true,
  },
];

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse text-white">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // Sample stats for display
  const stats = [
    {
      title: "Saved Properties",
      value: "12",
      icon: Heart,
      color: "text-primary",
      bgColor: "bg-primary/10",
      borderColor: "border-primary/30",
      link: "/dashboard/saved-properties"
    },
    {
      title: "Applications",
      value: "3",
      icon: FileCheck,
      color: "text-primary",
      bgColor: "bg-primary/10",
      borderColor: "border-primary/30",
      link: "/dashboard/applications"
    },
    {
      title: "Your Properties",
      value: "2",
      icon: Home,
      color: "text-primary",
      bgColor: "bg-primary/10",
      borderColor: "border-primary/30",
      link: "/dashboard/user-properties"
    },
    {
      title: "Market Insights",
      value: "View",
      icon: TrendingUp,
      color: "text-primary",
      bgColor: "bg-primary/10",
      borderColor: "border-primary/30",
      link: "/market-insights"
    },
  ];

  return (
    <div className="container py-8">
      {/* Welcome Banner */}
      <div className="bg-card rounded-xl p-6 mb-8 shadow-sm border border-border">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2 text-card-foreground">
              Welcome back, {user.displayName?.split(' ')[0] || user.email?.split('@')[0] || 'User'}!
            </h1>
            <p className="text-muted-foreground">Here's what's happening with your rental journey</p>
          </div>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" className="gap-2" asChild>
              <Link href="/properties" legacyBehavior>
                <span className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2" />
                  Browse Properties
                </span>
              </Link>
            </Button>
            <Button className="gap-2" asChild>
              <Link href="/dashboard/properties/new" legacyBehavior>
                <span className="flex items-center">
                  <Plus className="h-4 w-4 mr-2" />
                  List a Property
                </span>
              </Link>
            </Button>
          </div>
        </div>
      </div>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <Link
            href={stat.link}
            key={stat.title}
            className="block transition-transform hover:-translate-y-1">
            <Card className="border shadow-md h-full bg-card">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-card-foreground">{stat.title}</CardTitle>
                <div className={`${stat.bgColor} p-2 rounded-full ${stat.borderColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-card-foreground">{stat.value}</div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card className="border shadow-md bg-card">
            <CardHeader className="border-b border-border">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-card-foreground">Recent Activity</CardTitle>
                  <CardDescription>Your latest interactions and updates</CardDescription>
                </div>
                <Activity className="h-5 w-5 text-primary" />
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-8">
                <div className="border-l-2 border-primary pl-4 relative">
                  <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1"></div>
                  <h3 className="font-medium text-base text-card-foreground">New property viewed</h3>
                  <p className="text-sm text-muted-foreground mb-2">You viewed this property yesterday</p>
                  <div className="mt-3">
                    {recentProperties.map(property => (
                      <PropertyCard key={property.id} {...property} />
                    ))}
                  </div>
                </div>

                <div className="border-l-2 border-primary pl-4 relative">
                  <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1"></div>
                  <h3 className="font-medium text-base text-card-foreground">Application status update</h3>
                  <p className="text-sm text-muted-foreground">Your application for 123 Main Street has been reviewed</p>
                  <Button variant="link" className="p-0 h-auto mt-1 text-primary">
                    View details <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>

                <div className="border-l-2 border-primary pl-4 relative">
                  <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1"></div>
                  <h3 className="font-medium text-base text-card-foreground">Saved property price drop</h3>
                  <p className="text-sm text-muted-foreground">Price dropped by $150 for a property in your saved list</p>
                  <Button variant="link" className="p-0 h-auto mt-1 text-primary" asChild>
                    <Link href="/dashboard/saved-properties">
                      <span className="flex items-center">
                        View property <ChevronRight className="h-3 w-3 ml-1" />
                      </span>
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Schedule */}
        <div>
          <Card className="border shadow-md bg-card">
            <CardHeader className="border-b border-border">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-card-foreground">Upcoming Schedule</CardTitle>
                  <CardDescription>Your property viewings and appointments</CardDescription>
                </div>
                <Calendar className="h-5 w-5 text-primary" />
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex gap-4 p-3 rounded-lg border border-primary/30 bg-primary/10">
                  <div className="bg-primary/20 h-12 w-12 rounded-full flex items-center justify-center flex-shrink-0 border border-primary/30">
                    <Calendar className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-card-foreground">Property Viewing</h3>
                    <p className="text-sm text-muted-foreground">Modern Loft in Gastown</p>
                    <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>Tomorrow, 2:00 PM</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4 p-3 rounded-lg border border-primary/30 bg-primary/10">
                  <div className="bg-primary/20 h-12 w-12 rounded-full flex items-center justify-center flex-shrink-0 border border-primary/30">
                    <FileCheck className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-card-foreground">Landlord Interview</h3>
                    <p className="text-sm text-muted-foreground">Via Zoom meeting</p>
                    <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>Friday, 10:30 AM</span>
                    </div>
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-2">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Appointment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
