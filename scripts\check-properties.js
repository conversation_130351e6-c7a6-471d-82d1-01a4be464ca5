// Script to check properties in the database
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function checkProperties() {
  const uri = process.env.MONGODB_URI;

  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }

  console.log('Connecting to MongoDB...');

  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    await client.connect();
    console.log('Successfully connected to MongoDB!');

    const db = client.db(process.env.MONGODB_DB || 'rentcentral');

    // Get collections
    const propertiesCollection = db.collection('properties');
    const propertyImagesCollection = db.collection('propertyImages');
    const propertyAmenitiesCollection = db.collection('propertyAmenities');

    // Count properties
    const propertyCount = await propertiesCollection.countDocuments();
    console.log(`Total properties in database: ${propertyCount}`);

    // Count by city
    const cityCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$city", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    console.log('\nProperties by city:');
    cityCounts.forEach(city => {
      console.log(`${city._id}: ${city.count} properties`);
    });

    // Count by property type
    const typeCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$propertyType", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    console.log('\nProperties by type:');
    typeCounts.forEach(type => {
      console.log(`${type._id}: ${type.count} properties`);
    });

    // Count by status
    const statusCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    console.log('\nProperties by status:');
    statusCounts.forEach(status => {
      console.log(`${status._id || 'undefined'}: ${status.count} properties`);
    });

    // Count images
    const imageCount = await propertyImagesCollection.countDocuments();
    console.log(`\nTotal property images: ${imageCount}`);

    // Count amenities
    const amenityCount = await propertyAmenitiesCollection.countDocuments();
    console.log(`Total property amenities: ${amenityCount}`);

    // Sample property
    const sampleProperty = await propertiesCollection.findOne({});
    console.log('\nSample property:');
    console.log(JSON.stringify(sampleProperty, null, 2));

    // Get images for sample property
    const propertyImages = await propertyImagesCollection.find({ propertyId: sampleProperty._id }).toArray();
    console.log(`\nImages for sample property (${propertyImages.length}):`);
    propertyImages.forEach(image => {
      console.log(`- ${image.imageUrl} (Primary: ${image.isPrimary})`);
    });

  } catch (error) {
    console.error('Error checking properties:', error);
  } finally {
    await client.close();
    console.log('Database connection closed.');
  }
}

// Run the check function
checkProperties().catch(console.error);
