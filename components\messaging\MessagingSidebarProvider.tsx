'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the MessagingSidebar wrapper with client-side only rendering
const MessagingSidebarWrapper = dynamic(
  () => import('./MessagingSidebarWrapper'),
  { 
    ssr: false,
    loading: () => null
  }
);

// Create a dummy user for testing
const dummyUser = {
  id: "test-user-id",
  name: "Test User",
  email: "<EMAIL>",
  image: null
};

export default function MessagingSidebarProvider() {
  // For testing, always show the sidebar with a dummy user
  return <MessagingSidebarWrapper currentUser={dummyUser} />;
} 