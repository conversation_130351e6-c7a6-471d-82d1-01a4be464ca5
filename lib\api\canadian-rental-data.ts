/**
 * Canadian Rental Data Service
 *
 * This service provides realistic mock data for Canadian rental properties
 * based on actual market conditions in different cities.
 */

// Canadian cities with realistic data
export const CANADIAN_CITIES = [
  "Toronto",
  "Vancouver",
  "Montreal",
  "Calgary",
  "Edmonton",
  "Ottawa",
  "Winnipeg",
  "Quebec City",
  "Hamilton",
  "Halifax",
  "Victoria",
  "Kitchener",
  "London",
  "Oshawa",
  "Windsor",
  "Saskatoon",
  "Regina",
  "St. John's",
  "Kelowna",
  "Abbotsford",
]

// Base rent by city (approximate average 2-bedroom rents)
export const CITY_BASE_RENTS: Record<string, number> = {
  Toronto: 2800,
  Vancouver: 3000,
  Montreal: 1800,
  Calgary: 1600,
  Edmonton: 1500,
  Ottawa: 1900,
  Winnipeg: 1400,
  "Quebec City": 1300,
  Hamilton: 1800,
  Halifax: 1700,
  Victoria: 2200,
  Kitchener: 1700,
  London: 1600,
  Oshawa: 1700,
  Windsor: 1300,
  Saskatoon: 1300,
  Regina: 1200,
  "St. John's": 1300,
  Kelowna: 1900,
  Abbotsford: 1700,
}

// Vacancy rates by city (approximate %)
export const CITY_VACANCY_RATES: Record<string, number> = {
  Toronto: 1.7,
  Vancouver: 1.1,
  Montreal: 3.0,
  Calgary: 5.2,
  Edmonton: 4.9,
  Ottawa: 3.4,
  Winnipeg: 3.8,
  "Quebec City": 2.7,
  Hamilton: 3.5,
  Halifax: 1.9,
  Victoria: 1.0,
  Kitchener: 2.2,
  London: 3.4,
  Oshawa: 2.8,
  Windsor: 3.6,
  Saskatoon: 5.7,
  Regina: 7.8,
  "St. John's": 7.2,
  Kelowna: 2.7,
  Abbotsford: 1.6,
}

// Rent growth rates by city (approximate annual %)
export const CITY_RENT_GROWTH: Record<string, number> = {
  Toronto: 4.8,
  Vancouver: 5.2,
  Montreal: 3.5,
  Calgary: 2.1,
  Edmonton: 1.8,
  Ottawa: 3.9,
  Winnipeg: 2.5,
  "Quebec City": 2.3,
  Hamilton: 4.5,
  Halifax: 4.2,
  Victoria: 5.0,
  Kitchener: 4.7,
  London: 3.8,
  Oshawa: 4.1,
  Windsor: 3.2,
  Saskatoon: 1.5,
  Regina: 1.2,
  "St. John's": 1.0,
  Kelowna: 4.8,
  Abbotsford: 4.3,
}

// Common street names by province
export const STREET_NAMES_BY_PROVINCE: Record<string, string[]> = {
  ON: ["Yonge", "Queen", "King", "Bloor", "Dundas", "Bay", "College", "Spadina", "Parliament", "Jarvis"],
  BC: ["Robson", "Granville", "Davie", "Denman", "Burrard", "Hastings", "Main", "Broadway", "Cambie", "Georgia"],
  QC: [
    "Saint-Laurent",
    "Sainte-Catherine",
    "Sherbrooke",
    "René-Lévesque",
    "Crescent",
    "Peel",
    "Saint-Denis",
    "Mont-Royal",
    "Maisonneuve",
    "Papineau",
  ],
  AB: [
    "Jasper",
    "Whyte",
    "Calgary Trail",
    "Stony Plain",
    "17 Avenue",
    "Macleod Trail",
    "Deerfoot Trail",
    "Crowchild",
    "Memorial",
    "Bow Trail",
  ],
  MB: [
    "Portage",
    "Main",
    "Pembina",
    "Henderson",
    "Osborne",
    "Corydon",
    "St. Mary's",
    "Regent",
    "St. Anne's",
    "Kenaston",
  ],
  NS: [
    "Spring Garden",
    "Barrington",
    "Quinpool",
    "Robie",
    "Gottingen",
    "Young",
    "Windsor",
    "Oxford",
    "Coburg",
    "Jubilee",
  ],
  default: ["Main", "First", "Second", "Third", "Fourth", "Fifth", "Maple", "Oak", "Pine", "Cedar"],
}

// Property types with realistic distribution
export const PROPERTY_TYPES = [
  { type: "Apartment", weight: 50 },
  { type: "Condo", weight: 20 },
  { type: "House", weight: 15 },
  { type: "Townhouse", weight: 10 },
  { type: "Basement Suite", weight: 5 },
]

// Common amenities
export const AMENITIES = [
  { name: "In-unit Laundry", probability: 0.7 },
  { name: "Dishwasher", probability: 0.8 },
  { name: "Air Conditioning", probability: 0.5 },
  { name: "Balcony", probability: 0.6 },
  { name: "Parking", probability: 0.75 },
  { name: "Pet Friendly", probability: 0.4 },
  { name: "Gym", probability: 0.3 },
  { name: "Pool", probability: 0.15 },
  { name: "Storage", probability: 0.65 },
  { name: "Elevator", probability: 0.5 },
  { name: "Security System", probability: 0.45 },
  { name: "Furnished", probability: 0.2 },
]

// Property descriptions
export const PROPERTY_DESCRIPTIONS = [
  "Bright and spacious {bedrooms} bedroom {type} in the heart of {city}. Features include hardwood floors, stainless steel appliances, and a private balcony with city views.",
  "Beautiful {bedrooms} bedroom {type} in a quiet neighborhood. Recently renovated with modern finishes, open concept living area, and updated kitchen.",
  "Charming {bedrooms} bedroom {type} in a well-maintained building. Includes in-suite laundry, updated bathroom, and a spacious kitchen with breakfast bar.",
  "Stunning {bedrooms} bedroom {type} with panoramic views. Features high ceilings, large windows, and a gourmet kitchen with granite countertops.",
  "Cozy {bedrooms} bedroom {type} in a convenient location. Walking distance to shops, restaurants, and public transit. Includes parking and storage locker.",
  "Modern {bedrooms} bedroom {type} with open concept design. Features include quartz countertops, stainless steel appliances, and in-suite laundry.",
  "Spacious {bedrooms} bedroom {type} in a family-friendly neighborhood. Close to schools, parks, and shopping. Includes private yard and garage.",
  "Luxurious {bedrooms} bedroom {type} with high-end finishes. Features include hardwood floors, custom cabinetry, and a spa-like bathroom.",
  "Bright corner {bedrooms} bedroom {type} with lots of natural light. Recently updated with new flooring, paint, and appliances.",
  "Character-filled {bedrooms} bedroom {type} in a heritage building. Features original hardwood floors, high ceilings, and modern updates.",
]

// Provinces by city
export const CITY_PROVINCES: Record<string, string> = {
  Toronto: "ON",
  Vancouver: "BC",
  Montreal: "QC",
  Calgary: "AB",
  Edmonton: "AB",
  Ottawa: "ON",
  Winnipeg: "MB",
  "Quebec City": "QC",
  Hamilton: "ON",
  Halifax: "NS",
  Victoria: "BC",
  Kitchener: "ON",
  London: "ON",
  Oshawa: "ON",
  Windsor: "ON",
  Saskatoon: "SK",
  Regina: "SK",
  "St. John's": "NL",
  Kelowna: "BC",
  Abbotsford: "BC",
}

// Helper function to get a weighted random property type
export function getRandomPropertyType(): string {
  const totalWeight = PROPERTY_TYPES.reduce((sum, item) => sum + item.weight, 0)
  let random = Math.random() * totalWeight

  for (const item of PROPERTY_TYPES) {
    random -= item.weight
    if (random <= 0) {
      return item.type
    }
  }

  return PROPERTY_TYPES[0].type
}

// Helper function to get random amenities
export function getRandomAmenities(): string[] {
  return AMENITIES.filter((amenity) => Math.random() < amenity.probability).map((amenity) => amenity.name)
}

// Helper function to generate a property description
export function generatePropertyDescription(bedrooms: number, type: string, city: string): string {
  const template = PROPERTY_DESCRIPTIONS[Math.floor(Math.random() * PROPERTY_DESCRIPTIONS.length)]
  return template
    .replace("{bedrooms}", bedrooms.toString())
    .replace("{type}", type.toLowerCase())
    .replace("{city}", city)
}

// Helper function to generate a random postal code for a given province
export function generatePostalCode(province: string): string {
  const firstLetterByProvince: Record<string, string> = {
    ON: "KLMNP",
    QC: "GHJ",
    BC: "VX",
    AB: "T",
    MB: "R",
    SK: "S",
    NS: "B",
    NB: "E",
    NL: "A",
    PE: "C",
    YT: "Y",
    NT: "X",
    NU: "X",
  }

  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  const firstLetter = firstLetterByProvince[province]
    ? firstLetterByProvince[province][Math.floor(Math.random() * firstLetterByProvince[province].length)]
    : letters[Math.floor(Math.random() * letters.length)]

  const number1 = Math.floor(Math.random() * 10)
  const letter2 = letters[Math.floor(Math.random() * letters.length)]
  const number2 = Math.floor(Math.random() * 10)
  const letter3 = letters[Math.floor(Math.random() * letters.length)]
  const number3 = Math.floor(Math.random() * 10)

  return `${firstLetter}${number1}${letter2} ${number2}${letter3}${number3}`
}

// Helper function to generate a random street address
export function generateStreetAddress(province: string): string {
  const streetNames = STREET_NAMES_BY_PROVINCE[province] || STREET_NAMES_BY_PROVINCE["default"]
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)]
  const streetNumber = Math.floor(Math.random() * 9000) + 1000

  const streetTypes = ["St", "Ave", "Rd", "Blvd", "Cres", "Dr", "Way"]
  const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)]

  const units = ["", "", "", "", "Unit " + Math.floor(Math.random() * 100 + 1) + ", "]
  const unit = units[Math.floor(Math.random() * units.length)]

  return `${unit}${streetNumber} ${streetName} ${streetType}`
}

// Generate a list of mock properties for a given city
export function generatePropertiesForCity(city: string, count = 10): any[] {
  const province = CITY_PROVINCES[city] || "ON"
  const baseRent = CITY_BASE_RENTS[city] || 1800

  return Array.from({ length: count }, (_, i) => {
    const bedrooms = Math.floor(Math.random() * 3) + 1 // 1-3 bedrooms
    const bathrooms = Math.min(bedrooms, Math.floor(Math.random() * 2) + 1) // 1-2 bathrooms, not more than bedrooms
    const propertyType = getRandomPropertyType()
    const squareFeet = 500 + bedrooms * 200 + Math.floor(Math.random() * 300)

    // Adjust rent based on property characteristics
    const bedroomFactor = (bedrooms - 1) * 300
    const bathroomFactor = (bathrooms - 1) * 200
    const sizeFactor = ((squareFeet - 700) / 100) * 50
    const propertyTypeFactor =
      propertyType === "Apartment"
        ? 0
        : propertyType === "Condo"
          ? 200
          : propertyType === "Townhouse"
            ? 300
            : propertyType === "House"
              ? 500
              : -100 // Basement Suite

    const rent = Math.round(baseRent + bedroomFactor + bathroomFactor + sizeFactor + propertyTypeFactor)

    // Add some randomness (±10%)
    const randomFactor = 1 + (Math.random() * 0.2 - 0.1)
    const finalRent = Math.round(rent * randomFactor)

    const streetAddress = generateStreetAddress(province)
    const postalCode = generatePostalCode(province)

    return {
      id: `prop-${city.toLowerCase().replace(/\s+/g, "-")}-${i + 1}`,
      title: `${bedrooms} Bedroom ${propertyType} in ${city}`,
      description: generatePropertyDescription(bedrooms, propertyType, city),
      address: streetAddress,
      city: city,
      province: province,
      postalCode: postalCode,
      propertyType: propertyType,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      squareFeet: squareFeet,
      price: finalRent,
      amenities: getRandomAmenities(),
      availableFrom: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // Random date in next 30 days
      images: Array.from(
        { length: Math.floor(Math.random() * 4) + 2 },
        (_, i) => `/placeholder.svg?height=400&width=600&text=${encodeURIComponent(`${city} Property ${i + 1}`)}`,
      ),
      status: "available",
      createdAt: new Date().toISOString(),
    };
  });
}

// Generate market trends data for a city
export function generateMarketTrendsForCity(city: string, propertyType?: string, bedrooms?: number): any {
  const baseRent = CITY_BASE_RENTS[city] || 1800
  const vacancyRate = CITY_VACANCY_RATES[city] || 3.0
  const rentGrowth = CITY_RENT_GROWTH[city] || 3.5

  // Adjust for property type and bedrooms
  let adjustedRent = baseRent
  if (propertyType) {
    adjustedRent +=
      propertyType === "apartment"
        ? 0
        : propertyType === "condo"
          ? 200
          : propertyType === "house"
            ? 500
            : propertyType === "townhouse"
              ? 300
              : -100 // basement
  }

  if (bedrooms) {
    adjustedRent += (bedrooms - 2) * 300 // Adjust from base 2-bedroom
  }

  // Generate monthly trend data for the past 12 months
  const currentDate = new Date()
  const rentTrends = Array.from({ length: 12 }, (_, i) => {
    // Go back i months from current date
    const month = new Date(currentDate)
    month.setMonth(currentDate.getMonth() - 11 + i)

    // Calculate rent for that month based on growth rate
    const monthsAgo = 11 - i
    const monthlyGrowthRate = rentGrowth / 12 / 100
    const historicalRent = Math.round(adjustedRent / Math.pow(1 + monthlyGrowthRate, monthsAgo))

    // Add some randomness for realism (±2%)
    const randomFactor = 1 + (Math.random() * 0.04 - 0.02)
    const finalRent = Math.round(historicalRent * randomFactor)

    return {
      month: month.toISOString().split("T")[0].substring(0, 7), // YYYY-MM format
      averageRent: finalRent,
    }
  })

  // Calculate days on market based on vacancy rate
  // Lower vacancy rates typically mean properties rent faster
  const daysOnMarket = Math.round(10 + vacancyRate * 3)

  return {
    location: city,
    propertyType: propertyType || "apartment",
    bedrooms: bedrooms || 2,
    averageRent: adjustedRent,
    rentGrowth: Number.parseFloat(rentGrowth.toFixed(1)),
    vacancyRate: Number.parseFloat(vacancyRate.toFixed(1)),
    daysOnMarket,
    rentTrends,
    currency: "CAD",
    dataSource: "Based on CMHC and Statistics Canada data",
  }
}

// Generate rent estimate for a property
export function generateRentEstimate(address: string, bedrooms?: number, bathrooms?: number, squareFeet?: number): any {
  // Extract city from address
  const addressParts = address.split(",").map((part) => part.trim())
  let city = "Toronto" // Default

  // Try to find a known city in the address
  for (const part of addressParts) {
    if (CANADIAN_CITIES.includes(part)) {
      city = part
      break
    }
  }

  const baseRent = CITY_BASE_RENTS[city] || 1800
  const bedroomFactor = bedrooms ? (bedrooms - 1) * 300 : 300
  const bathroomFactor = bathrooms ? (bathrooms - 1) * 200 : 0
  const sizeFactor = squareFeet ? ((squareFeet - 700) / 100) * 50 : 0

  const rentEstimate = Math.round(baseRent + bedroomFactor + bathroomFactor + sizeFactor)
  const rentRangeLow = Math.floor(rentEstimate * 0.9)
  const rentRangeHigh = Math.floor(rentEstimate * 1.1)

  // Generate comparable properties
  const comparables = Array.from({ length: 3 }, (_, i) => {
    // Randomize property characteristics slightly
    const bedroomDiff = Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : -1) : 0
    const bathroomDiff = Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : -1) : 0
    const sizeDiff = Math.floor(Math.random() * 200 - 100)

    // Randomize rent based on property differences
    const rentDiff = bedroomDiff * 300 + bathroomDiff * 200 + (sizeDiff / 100) * 50

    // Add some randomness (±7%)
    const randomFactor = 1 + (Math.random() * 0.14 - 0.07)
    const finalRent = Math.round((rentEstimate + rentDiff) * randomFactor)

    const province = CITY_PROVINCES[city] || "ON"
    const streetAddress = generateStreetAddress(province)

    return {
      address: `${streetAddress}, ${city}, ${province}`,
      bedrooms: Math.max(1, (bedrooms || 2) + bedroomDiff),
      bathrooms: Math.max(1, (bathrooms || 1) + bathroomDiff),
      squareFootage: Math.max(400, (squareFeet || 800) + sizeDiff),
      rent: finalRent,
    }
  })

  return {
    address,
    rent: rentEstimate,
    rentRangeLow,
    rentRangeHigh,
    bedrooms: bedrooms || 2,
    bathrooms: bathrooms || 1,
    squareFootage: squareFeet || 800,
    comparables,
  }
}

// Search for properties by location
export function searchPropertiesByLocation(location: string, limit = 10): any {
  // Check if location matches any of our cities
  const matchedCity = CANADIAN_CITIES.find((city) => city.toLowerCase() === location.toLowerCase())

  if (matchedCity) {
    return {
      success: true,
      properties: generatePropertiesForCity(matchedCity, limit),
    }
  }

  // Check if location is a partial match for any city
  const partialMatches = CANADIAN_CITIES.filter((city) => city.toLowerCase().includes(location.toLowerCase()))

  if (partialMatches.length > 0) {
    // Generate properties from the first few matches
    const properties = partialMatches
      .slice(0, 3)
      .flatMap((city) => generatePropertiesForCity(city, Math.ceil(limit / Math.min(partialMatches.length, 3))))

    return {
      success: true,
      properties: properties.slice(0, limit),
    }
  }

  // If no matches, return properties from random cities
  const randomCities = Array.from(
    { length: Math.min(3, Math.ceil(limit / 4)) },
    () => CANADIAN_CITIES[Math.floor(Math.random() * CANADIAN_CITIES.length)],
  )

  const properties = randomCities.flatMap((city) =>
    generatePropertiesForCity(city, Math.ceil(limit / randomCities.length)),
  )

  return {
    success: true,
    properties: properties.slice(0, limit),
  }
}
