"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { useAuth } from "@/components/auth-provider"

interface Property {
  _id: string
  title: string
  status: 'pending' | 'approved' | 'declined' | 'appealed'
  landlord_id: string
  createdAt: string
  reason?: string
}

export function PropertyApproval() {
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const { user } = useAuth()

  useEffect(() => {
    fetchProperties()
  }, [])

  const fetchProperties = async () => {
    try {
      const response = await fetch('/api/properties?status=pending')
      const data = await response.json()
      setProperties(data)
    } catch (error) {
      console.error('Error fetching properties:', error)
      toast({
        title: "Error",
        description: "Failed to fetch properties",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (propertyId: string) => {
    try {
      const response = await fetch(`/api/properties/${propertyId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) throw new Error('Failed to approve property')

      toast({
        title: "Success",
        description: "Property approved successfully",
      })

      fetchProperties()
    } catch (error) {
      console.error('Error approving property:', error)
      toast({
        title: "Error",
        description: "Failed to approve property",
        variant: "destructive",
      })
    }
  }

  const handleDecline = async (propertyId: string, reason: string) => {
    try {
      const response = await fetch(`/api/properties/${propertyId}/decline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      })

      if (!response.ok) throw new Error('Failed to decline property')

      toast({
        title: "Success",
        description: "Property declined successfully",
      })

      fetchProperties()
    } catch (error) {
      console.error('Error declining property:', error)
      toast({
        title: "Error",
        description: "Failed to decline property",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Approvals</CardTitle>
        <CardDescription>Review and manage property listings</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {properties.map((property) => (
              <TableRow key={property._id}>
                <TableCell>{property.title}</TableCell>
                <TableCell>
                  <Badge variant={
                    property.status === 'approved' ? 'success' :
                    property.status === 'declined' ? 'destructive' :
                    property.status === 'appealed' ? 'warning' :
                    'default'
                  }>
                    {property.status}
                  </Badge>
                </TableCell>
                <TableCell>{new Date(property.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleApprove(property._id)}
                    >
                      Approve
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        const reason = prompt('Please enter a reason for declining:')
                        if (reason) handleDecline(property._id, reason)
                      }}
                    >
                      Decline
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}