"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { fetchRentEstimate } from "@/app/actions/rentcast-actions"
import { Home, DollarSign, Ruler, Bath } from "lucide-react"
import { formatCurrency } from "@/lib/utils"

interface RentEstimateProps {
  address: string
  bedrooms?: number
  bathrooms?: number
  squareFeet?: number
}

export function RentEstimate({ address, bedrooms, bathrooms, squareFeet }: RentEstimateProps) {
  const [estimateData, setEstimateData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadEstimateData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const data = await fetchRentEstimate(address, bedrooms, bathrooms, squareFeet)
        if (data.error) {
          setError(data.error)
        } else {
          setEstimateData(data)
        }
      } catch (error: any) {
        console.error("Error loading rent estimate:", error)
        setError(error.message || "Failed to load rent estimate")
      } finally {
        setIsLoading(false)
      }
    }

    if (address) {
      loadEstimateData()
    }
  }, [address, bedrooms, bathrooms, squareFeet])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-1/3" />
          <Skeleton className="h-8 w-2/3" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[200px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Rent Estimate</CardTitle>
          <CardDescription>Error loading estimate</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!estimateData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Rent Estimate</CardTitle>
          <CardDescription>No estimate available for this property</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            We couldn't generate a rent estimate for this property. Please check the address and try again.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rent Estimate</CardTitle>
        <CardDescription>Estimated monthly rent for this property</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <div className="text-4xl font-bold">{formatCurrency(estimateData.rent)}</div>
          <div className="text-sm text-muted-foreground">
            Estimated rent range: {formatCurrency(estimateData.rentRangeLow)} -{" "}
            {formatCurrency(estimateData.rentRangeHigh)}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
          <div className="flex flex-col items-center justify-center space-y-1 rounded-lg border p-3">
            <Home className="h-5 w-5 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Bedrooms</span>
            <span className="font-medium">{estimateData.bedrooms || bedrooms || "N/A"}</span>
          </div>
          <div className="flex flex-col items-center justify-center space-y-1 rounded-lg border p-3">
            <Bath className="h-5 w-5 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Bathrooms</span>
            <span className="font-medium">{estimateData.bathrooms || bathrooms || "N/A"}</span>
          </div>
          <div className="flex flex-col items-center justify-center space-y-1 rounded-lg border p-3">
            <Ruler className="h-5 w-5 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Square Feet</span>
            <span className="font-medium">{estimateData.squareFootage || squareFeet || "N/A"}</span>
          </div>
          <div className="flex flex-col items-center justify-center space-y-1 rounded-lg border p-3">
            <DollarSign className="h-5 w-5 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Per Sq Ft</span>
            <span className="font-medium">
              {estimateData.squareFootage
                ? formatCurrency(estimateData.rent / estimateData.squareFootage).replace("$", "$")
                : "N/A"}
            </span>
          </div>
        </div>

        {estimateData.comparables && estimateData.comparables.length > 0 && (
          <div>
            <h3 className="mb-2 font-medium">Comparable Properties</h3>
            <div className="space-y-2">
              {estimateData.comparables.slice(0, 3).map((comp: any, index: number) => (
                <div key={index} className="rounded-lg border p-3">
                  <div className="flex justify-between">
                    <div className="text-sm font-medium">{comp.address}</div>
                    <div className="font-medium">{formatCurrency(comp.rent)}</div>
                  </div>
                  <div className="mt-1 text-xs text-muted-foreground">
                    {comp.bedrooms} bed • {comp.bathrooms} bath • {comp.squareFootage} sq ft
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground">Data source: Canadian Rental Market Data</div>
      </CardContent>
    </Card>
  )
}
