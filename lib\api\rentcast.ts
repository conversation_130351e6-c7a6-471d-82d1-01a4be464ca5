/**
 * Rentcast API Service
 * This service handles all interactions with the Rentcast API
 */

const RENTCAST_API_BASE_URL = "https://api.rentcast.io/v1"
const RENTCAST_API_KEY = process.env.RENTCAST_API_KEY || "38289d03f1b74fb6b598e14346dd0c48"

/**
 * Get rent estimate for a property based on address
 */
export async function getRentEstimate(address: string, bedrooms?: number, bathrooms?: number, squareFeet?: number) {
  try {
    const params = new URLSearchParams()
    params.append("address", address)
    if (bedrooms) params.append("bedrooms", bedrooms.toString())
    if (bathrooms) params.append("bathrooms", bathrooms.toString())
    if (squareFeet) params.append("squareFeet", squareFeet.toString())

    const response = await fetch(`${RENTCAST_API_BASE_URL}/rental-estimate?${params.toString()}`, {
      headers: {
        "X-Api-Key": RENTCAST_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Rentcast API error: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching rent estimate:", error)
    throw error
  }
}

/**
 * Search for properties by location (city, state, or zip)
 */
export async function searchProperties(location: string, limit = 10) {
  try {
    const params = new URLSearchParams()
    params.append("location", location)
    params.append("limit", limit.toString())

    const response = await fetch(`${RENTCAST_API_BASE_URL}/properties/search?${params.toString()}`, {
      headers: {
        "X-Api-Key": RENTCAST_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Rentcast API error: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error searching properties:", error)
    throw error
  }
}

/**
 * Get comparable properties for a given address
 */
export async function getComparableProperties(address: string, radius = 1, limit = 5) {
  try {
    const params = new URLSearchParams()
    params.append("address", address)
    params.append("radius", radius.toString())
    params.append("limit", limit.toString())

    const response = await fetch(`${RENTCAST_API_BASE_URL}/properties/comparables?${params.toString()}`, {
      headers: {
        "X-Api-Key": RENTCAST_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Rentcast API error: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching comparable properties:", error)
    throw error
  }
}

/**
 * Get market trends for a location (city, state, or zip)
 */
export async function getMarketTrends(location: string, propertyType?: string, bedrooms?: number) {
  try {
    const params = new URLSearchParams()
    params.append("location", location)
    if (propertyType) params.append("propertyType", propertyType)
    if (bedrooms) params.append("bedrooms", bedrooms.toString())

    const response = await fetch(`${RENTCAST_API_BASE_URL}/market/trends?${params.toString()}`, {
      headers: {
        "X-Api-Key": RENTCAST_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Rentcast API error: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching market trends:", error)
    throw error
  }
}
