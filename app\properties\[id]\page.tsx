'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { ArrowLeft, Bed, Bath, DollarSign, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

// Define property type
type PropertyType = {
  id: string;
  title: string;
  description: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  amenities: string[];
  images: string[];
};

export default function PropertyDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [property, setProperty] = useState<PropertyType | null>(null);
  const [loading, setLoading] = useState(true);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [applicationData, setApplicationData] = useState({
    income: '',
    employmentStatus: '',
    references: [{ name: '', relationship: '', contact: '' }],
  });

  // Get the property ID from params
  // In client components, we can't use React.use() directly
  // Instead, we'll destructure the id from params
  const { id: propertyId } = params;

  useEffect(() => {
    const fetchProperty = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/properties/${propertyId}`);

        if (response.ok) {
          const data = await response.json();
          setProperty(data);
        } else {
          console.error('Failed to fetch property');
        }
      } catch (error) {
        console.error('Error fetching property:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProperty();
  }, [propertyId]);

  const handleSubmitApplication = async () => {
    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: propertyId,
          ...applicationData,
        }),
      });

      if (response.ok) {
        alert('Application submitted successfully!');
        setShowApplicationForm(false);
      } else {
        throw new Error('Failed to submit application');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      alert('Failed to submit application. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold">Property not found</h1>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Button
        variant="outline"
        className="mb-6"
        onClick={() => router.back()}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Properties
      </Button>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Property Images */}
        <div className="space-y-4">
          {property.images && property.images.length > 0 ? (
            property.images.map((image, index) => (
              <div key={index} className="relative h-64 w-full">
                <Image
                  src={image}
                  alt={`${property.title} - Image ${index + 1}`}
                  fill
                  className="object-cover rounded-lg"
                  unoptimized={image.includes('cloudinary') || image.includes('unsplash')}
                  onError={(e) => {
                    // Fallback image if the URL fails to load
                    (e.target as HTMLImageElement).src = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80";
                  }}
                />
              </div>
            ))
          ) : (
            // Display placeholder if no images
            (<div className="relative h-64 w-full">
              <Image
                src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Property placeholder"
                fill
                className="object-cover rounded-lg"
                unoptimized
              />
            </div>)
          )}
        </div>

        {/* Property Details */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">{property.title}</CardTitle>
              <CardDescription className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {property.address.street}, {property.address.city}, {property.address.state} {property.address.zipCode}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center">
                  <Bed className="w-4 h-4 mr-1" />
                  <span>{property.bedrooms} beds</span>
                </div>
                <div className="flex items-center">
                  <Bath className="w-4 h-4 mr-1" />
                  <span>{property.bathrooms} baths</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 mr-1" />
                  <span>{property.price}/mo</span>
                </div>
                <div className="flex items-center">
                  <span>{property.squareFeet} sq ft</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">{property.description}</p>

              <div className="mb-4">
                <h3 className="font-semibold mb-2">Amenities</h3>
                <div className="flex flex-wrap gap-2">
                  {property.amenities.map((amenity, index) => (
                    <span key={index} className="bg-gray-100 px-3 py-1 rounded-full text-sm">
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>

              <Button
                className="w-full"
                onClick={() => setShowApplicationForm(true)}
              >
                Apply Now
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Application Form Modal */}
      {showApplicationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Submit Application</CardTitle>
              <CardDescription>Please fill out the application form</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Monthly Income</label>
                <Input
                  type="number"
                  value={applicationData.income}
                  onChange={(e) => setApplicationData({ ...applicationData, income: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Employment Status</label>
                <Input
                  value={applicationData.employmentStatus}
                  onChange={(e) => setApplicationData({ ...applicationData, employmentStatus: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">References</label>
                {applicationData.references.map((ref, index) => (
                  <div key={index} className="space-y-2 mb-4">
                    <Input
                      placeholder="Name"
                      value={ref.name}
                      onChange={(e) => {
                        const newRefs = [...applicationData.references];
                        newRefs[index].name = e.target.value;
                        setApplicationData({ ...applicationData, references: newRefs });
                      }}
                    />
                    <Input
                      placeholder="Relationship"
                      value={ref.relationship}
                      onChange={(e) => {
                        const newRefs = [...applicationData.references];
                        newRefs[index].relationship = e.target.value;
                        setApplicationData({ ...applicationData, references: newRefs });
                      }}
                    />
                    <Input
                      placeholder="Contact"
                      value={ref.contact}
                      onChange={(e) => {
                        const newRefs = [...applicationData.references];
                        newRefs[index].contact = e.target.value;
                        setApplicationData({ ...applicationData, references: newRefs });
                      }}
                    />
                  </div>
                ))}
                <Button
                  variant="outline"
                  onClick={() => {
                    setApplicationData({
                      ...applicationData,
                      references: [...applicationData.references, { name: '', relationship: '', contact: '' }]
                    });
                  }}
                >
                  Add Reference
                </Button>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowApplicationForm(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSubmitApplication}>
                  Submit Application
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
