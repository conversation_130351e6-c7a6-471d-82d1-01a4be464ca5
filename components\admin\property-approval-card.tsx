"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { toast } from "@/components/ui/use-toast"
import { CheckCircle, XCircle, User, Calendar, DollarSign, Bed, Bath, Eye, AlertTriangle } from "lucide-react"

interface PropertyApprovalCardProps {
  property: {
    id: string
    title: string
    address: string
    landlord: string
    landlordId: string
    submittedDate: string
    approvedDate?: string
    rejectedDate?: string
    rejectionReason?: string
    price: number
    bedrooms: number
    bathrooms: number
    images: string[]
    status: "pending" | "approved" | "rejected"
  }
}

export function PropertyApprovalCard({ property }: PropertyApprovalCardProps) {
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [currentStatus, setCurrentStatus] = useState(property.status)

  const handleApprove = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setCurrentStatus("approved")
      setIsApproveDialogOpen(false)

      toast({
        title: "Property approved",
        description: `${property.title} has been approved and is now live.`,
      })
    }, 1000)
  }

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for rejection.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setCurrentStatus("rejected")
      setIsRejectDialogOpen(false)

      toast({
        title: "Property rejected",
        description: `${property.title} has been rejected.`,
      })
    }, 1000)
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid gap-6 md:grid-cols-[300px_1fr]">
          <div>
            <Carousel className="w-full">
              <CarouselContent>
                {property.images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative aspect-[4/3] w-full overflow-hidden rounded-md">
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`Property image ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious />
              <CarouselNext />
            </Carousel>
            <div className="mt-4 flex flex-wrap gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Bed className="h-3 w-3" />
                {property.bedrooms} {property.bedrooms === 1 ? "Bedroom" : "Bedrooms"}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <Bath className="h-3 w-3" />
                {property.bathrooms} {property.bathrooms === 1 ? "Bathroom" : "Bathrooms"}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />${property.price}/month
              </Badge>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold">{property.title}</h3>
                {currentStatus === "pending" ? (
                  <Badge
                    variant="outline"
                    className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100"
                  >
                    Pending Review
                  </Badge>
                ) : currentStatus === "approved" ? (
                  <Badge
                    variant="outline"
                    className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                  >
                    Approved
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                    Rejected
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">{property.address}</p>
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Landlord:{" "}
                  <Link
                    href={`/admin/users/${property.landlordId}`}
                    className="font-medium hover:underline"
                    legacyBehavior>
                    {property.landlord}
                  </Link>
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Submitted: <span className="font-medium">{property.submittedDate}</span>
                </span>
              </div>
            </div>

            {currentStatus === "approved" && property.approvedDate && (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">
                  Approved on <span className="font-medium">{property.approvedDate}</span>
                </span>
              </div>
            )}

            {currentStatus === "rejected" && property.rejectedDate && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm">
                    Rejected on <span className="font-medium">{property.rejectedDate}</span>
                  </span>
                </div>
                {property.rejectionReason && (
                  <div className="rounded-md bg-red-50 p-3 dark:bg-red-950">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="mt-0.5 h-4 w-4 text-red-600" />
                      <div>
                        <p className="text-sm font-medium text-red-800 dark:text-red-200">Reason for rejection:</p>
                        <p className="text-sm text-red-700 dark:text-red-300">{property.rejectionReason}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2 border-t p-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/admin/properties/${property.id}`} legacyBehavior>
            <span className="flex items-center">
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </span>
          </Link>
        </Button>

        {currentStatus === "pending" && (
          <>
            <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Reject Property Listing</DialogTitle>
                  <DialogDescription>
                    Please provide a reason for rejecting this property listing. This will be shared with the landlord.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">{property.title}</h4>
                    <p className="text-sm text-muted-foreground">{property.address}</p>
                  </div>
                  <Textarea
                    placeholder="Reason for rejection..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleReject} disabled={isLoading}>
                    {isLoading ? "Rejecting..." : "Reject Listing"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Approve Property Listing</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to approve this property listing? It will be visible to all users.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">{property.title}</h4>
                    <p className="text-sm text-muted-foreground">{property.address}</p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleApprove} disabled={isLoading}>
                    {isLoading ? "Approving..." : "Approve Listing"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        )}
      </CardFooter>
    </Card>
  );
}
