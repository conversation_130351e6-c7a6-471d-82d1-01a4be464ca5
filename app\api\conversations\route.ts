import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb/client";
import { auth } from "@/lib/firebase-admin";

// GET all conversations for the current user
export async function GET(request: NextRequest) {
  try {
    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const conversationsCollection = db.collection('conversations');

    // Get all conversations for the user
    const conversations = await conversationsCollection
      .find({
        $or: [
          { userId1: userId },
          { userId2: userId }
        ]
      })
      .sort({ updatedAt: -1 })
      .toArray();

    // Get the latest message for each conversation
    const conversationsWithMessages = await Promise.all(
      conversations.map(async (conversation) => {
        const messagesCollection = db.collection('messages');
        const latestMessage = await messagesCollection
          .find({ conversationId: conversation._id.toString() })
          .sort({ createdAt: -1 })
          .limit(1)
          .toArray();

        return {
          ...conversation,
          messages: latestMessage
        };
      })
    );

    return NextResponse.json(conversationsWithMessages);
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return NextResponse.json({ error: "Failed to fetch conversations" }, { status: 500 });
  }
}

// POST create a new conversation
export async function POST(request: NextRequest) {
  try {
    // Get the authorization token from the request headers
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.split("Bearer ")[1];
    
    // Verify the Firebase token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { otherUserId } = body;

    if (!otherUserId) {
      return NextResponse.json({ error: "Other user ID is required" }, { status: 400 });
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const conversationsCollection = db.collection('conversations');

    // Check if a conversation already exists between these users
    const existingConversation = await conversationsCollection.findOne({
      $or: [
        { userId1: userId, userId2: otherUserId },
        { userId1: otherUserId, userId2: userId }
      ]
    });

    if (existingConversation) {
      return NextResponse.json(existingConversation);
    }

    // Create a new conversation
    const newConversation = {
      userId1: userId,
      userId2: otherUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await conversationsCollection.insertOne(newConversation);
    return NextResponse.json({
      _id: result.insertedId,
      ...newConversation
    });
  } catch (error) {
    console.error("Error creating conversation:", error);
    return NextResponse.json({ error: "Failed to create conversation" }, { status: 500 });
  }
} 