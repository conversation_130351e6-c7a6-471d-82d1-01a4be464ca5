import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Base directory for JSON storage
const DATA_DIR = path.join(process.cwd(), 'data');

// Ensure the data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Generic storage class
export class JsonStorage<T extends { id?: string }> {
  private filePath: string;

  constructor(collectionName: string) {
    this.filePath = path.join(DATA_DIR, `${collectionName}.json`);
    
    // Create the file if it doesn't exist
    if (!fs.existsSync(this.filePath)) {
      fs.writeFileSync(this.filePath, JSON.stringify([]), 'utf8');
    }
  }

  // Get all items
  async getAll(): Promise<T[]> {
    try {
      const data = fs.readFileSync(this.filePath, 'utf8');
      return JSON.parse(data) as T[];
    } catch (error) {
      console.error(`Error reading from ${this.filePath}:`, error);
      return [];
    }
  }

  // Get item by ID
  async getById(id: string): Promise<T | null> {
    try {
      const items = await this.getAll();
      return items.find(item => item.id === id) || null;
    } catch (error) {
      console.error(`Error finding item with ID ${id}:`, error);
      return null;
    }
  }

  // Create new item
  async create(item: T): Promise<T> {
    try {
      const items = await this.getAll();
      const newItem = { ...item, id: item.id || uuidv4(), createdAt: new Date().toISOString() };
      items.push(newItem as T);
      fs.writeFileSync(this.filePath, JSON.stringify(items, null, 2), 'utf8');
      return newItem as T;
    } catch (error) {
      console.error('Error creating item:', error);
      throw error;
    }
  }

  // Update existing item
  async update(id: string, item: Partial<T>): Promise<T | null> {
    try {
      const items = await this.getAll();
      const index = items.findIndex(i => i.id === id);
      
      if (index === -1) return null;
      
      const updatedItem = { 
        ...items[index], 
        ...item, 
        updatedAt: new Date().toISOString() 
      };
      
      items[index] = updatedItem as T;
      fs.writeFileSync(this.filePath, JSON.stringify(items, null, 2), 'utf8');
      
      return updatedItem as T;
    } catch (error) {
      console.error(`Error updating item with ID ${id}:`, error);
      throw error;
    }
  }

  // Delete item
  async delete(id: string): Promise<boolean> {
    try {
      const items = await this.getAll();
      const filteredItems = items.filter(item => item.id !== id);
      
      if (filteredItems.length === items.length) {
        return false; // Item not found
      }
      
      fs.writeFileSync(this.filePath, JSON.stringify(filteredItems, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error(`Error deleting item with ID ${id}:`, error);
      throw error;
    }
  }

  // Custom query (filter function)
  async query(filterFn: (item: T) => boolean): Promise<T[]> {
    try {
      const items = await this.getAll();
      return items.filter(filterFn);
    } catch (error) {
      console.error('Error querying items:', error);
      return [];
    }
  }
} 