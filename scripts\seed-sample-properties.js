// This script seeds the database with sample properties
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Sample property data
const sampleProperties = [
  {
    title: "Luxury Downtown Apartment",
    description: "Beautiful luxury apartment in the heart of downtown with amazing views.",
    address: "123 Main Street",
    city: "Toronto",
    province: "Ontario",
    postalCode: "M5V 2K7",
    propertyType: "apartment",
    bedrooms: 2,
    bathrooms: 2,
    squareFeet: 1200,
    price: 2500,
    availableFrom: new Date("2023-12-01"),
    status: "approved",
    images: [
      "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60"
    ]
  },
  {
    title: "Cozy Studio in Midtown",
    description: "Cozy studio apartment perfect for students or young professionals.",
    address: "456 College Street",
    city: "Toronto",
    province: "Ontario",
    postalCode: "M6G 1A1",
    propertyType: "studio",
    bedrooms: 0,
    bathrooms: 1,
    squareFeet: 500,
    price: 1500,
    availableFrom: new Date("2023-11-15"),
    status: "approved",
    images: [
      "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTd8fHN0dWRpbyUyMGFwYXJ0bWVudHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1630699144867-37acec97df5a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fHN0dWRpbyUyMGFwYXJ0bWVudHxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60"
    ]
  },
  {
    title: "Spacious Family Home",
    description: "Spacious 4-bedroom home perfect for families, with a large backyard and garage.",
    address: "789 Oak Avenue",
    city: "Mississauga",
    province: "Ontario",
    postalCode: "L5B 2C9",
    propertyType: "house",
    bedrooms: 4,
    bathrooms: 3,
    squareFeet: 2500,
    price: 3500,
    availableFrom: new Date("2023-12-15"),
    status: "approved",
    images: [
      "https://images.unsplash.com/photo-1568605114967-8130f3a36994?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8aG91c2V8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGhvdXNlfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60"
    ]
  },
  {
    title: "Modern Condo with Lake View",
    description: "Modern 1-bedroom condo with stunning lake views and access to amenities.",
    address: "101 Lakeshore Blvd",
    city: "Toronto",
    province: "Ontario",
    postalCode: "M5J 2N8",
    propertyType: "condo",
    bedrooms: 1,
    bathrooms: 1,
    squareFeet: 700,
    price: 2200,
    availableFrom: new Date("2023-11-01"),
    status: "approved",
    images: [
      "https://images.unsplash.com/photo-1551361415-69c87624334f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y29uZG98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1567496898669-ee935f5f647a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGNvbmRvfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60"
    ]
  },
  {
    title: "Charming Townhouse",
    description: "Charming 3-bedroom townhouse in a quiet neighborhood with a small garden.",
    address: "222 Elm Street",
    city: "Oakville",
    province: "Ontario",
    postalCode: "L6H 3C8",
    propertyType: "townhouse",
    bedrooms: 3,
    bathrooms: 2,
    squareFeet: 1800,
    price: 2800,
    availableFrom: new Date("2023-12-01"),
    status: "approved",
    images: [
      "https://images.unsplash.com/photo-1625602812206-5ec545ca1231?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dG93bmhvdXNlfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1628744448840-55bdb2497bd4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fHRvd25ob3VzZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60"
    ]
  }
];

async function main() {
  try {
    console.log('Connecting to database...');
    
    // Get or create a landlord user
    let landlord = await prisma.user.findFirst({
      where: { isLandlord: true }
    });

    if (!landlord) {
      console.log('Creating a landlord user...');
      landlord = await prisma.user.create({
        data: {
          firstName: 'Sample',
          lastName: 'Landlord',
          email: '<EMAIL>',
          isLandlord: true,
          role: 'landlord'
        }
      });
      console.log(`Created landlord user with ID: ${landlord.id}`);
    } else {
      console.log(`Using existing landlord: ${landlord.firstName} ${landlord.lastName} (${landlord.id})`);
    }

    // Count existing properties
    const existingCount = await prisma.property.count();
    console.log(`Existing properties: ${existingCount}`);

    // Seed properties
    console.log('Seeding properties...');
    for (const propertyData of sampleProperties) {
      const { images, ...property } = propertyData;
      
      // Create property
      const createdProperty = await prisma.property.create({
        data: {
          ...property,
          landlordId: landlord.id
        }
      });
      
      console.log(`Created property: ${createdProperty.title} (${createdProperty.id})`);
      
      // Add images
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          await prisma.propertyImage.create({
            data: {
              propertyId: createdProperty.id,
              imageUrl: images[i],
              isPrimary: i === 0
            }
          });
        }
        console.log(`Added ${images.length} images to property ${createdProperty.id}`);
      }
    }
    
    // Count properties after seeding
    const finalCount = await prisma.property.count();
    console.log(`\nFinal count of properties: ${finalCount}`);
    
    console.log('\nDone!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
