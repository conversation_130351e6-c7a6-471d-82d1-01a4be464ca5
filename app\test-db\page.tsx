'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestDbPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testDatabase = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test-db');
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error('Error testing database:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testDatabase();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Database Connection Test</h1>
      
      <Button 
        onClick={testDatabase} 
        disabled={loading}
        className="mb-6"
      >
        {loading ? 'Testing...' : 'Test Database Connection'}
      </Button>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p className="font-bold">Error</p>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Database Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="font-semibold">Success:</p>
                <p>{result.success ? 'Yes' : 'No'}</p>
              </div>
              
              <div>
                <p className="font-semibold">Total Properties:</p>
                <p>{result.totalCount}</p>
              </div>
              
              <div>
                <p className="font-semibold">Pending Properties:</p>
                <p>{result.pendingCount}</p>
              </div>
              
              <div>
                <p className="font-semibold">Approved Properties:</p>
                <p>{result.approvedCount}</p>
              </div>
              
              {result.sampleProperty && (
                <div>
                  <p className="font-semibold">Sample Property:</p>
                  <pre className="bg-gray-100 p-4 rounded overflow-auto">
                    {JSON.stringify(result.sampleProperty, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
