"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { PropertyForm } from "@/components/property-form"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { propertiesApi } from "@/lib/aws/api"
import { useAuth } from "@/lib/aws/auth"

export default function EditPropertyPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [property, setProperty] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // Destructure id from params to avoid direct access warning
  const { id: propertyId } = params;

  useEffect(() => {
    if (!user) {
      router.push("/auth/login?redirect=" + encodeURIComponent(`/dashboard/properties/${propertyId}/edit`))
      return
    }

    const fetchProperty = async () => {
      try {
        const data = await propertiesApi.getPropertyById(propertyId)

        // Check if the user is the owner of the property
        if (data.landlord_id !== user.id) {
          setError("You do not have permission to edit this property.")
          toast({
            title: "Access denied",
            description: "You do not have permission to edit this property.",
            variant: "destructive",
          })
          router.push("/dashboard/properties")
          return
        }

        setProperty(data)
      } catch (error) {
        console.error("Error fetching property:", error)
        setError("Failed to load property data. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load property data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProperty()
  }, [propertyId, user, router, toast])

  const handleSuccess = (propertyId: string) => {
    router.push("/dashboard/properties")
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="container px-4 py-8 md:px-6 md:py-12">
          <div className="flex items-center mb-8">
            <Skeleton className="h-10 w-64" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-[600px] w-full rounded-lg" />
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="container px-4 py-8 md:px-6 md:py-12">
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h1 className="text-2xl font-bold mb-4">Error</h1>
            <p className="text-muted-foreground mb-6">{error}</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <h1 className="text-3xl font-bold mb-8">Edit Property</h1>
        <PropertyForm propertyId={propertyId} onSuccess={handleSuccess} />
      </div>
    </DashboardLayout>
  )
}
