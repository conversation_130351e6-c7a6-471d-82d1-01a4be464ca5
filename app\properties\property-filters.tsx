"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>lider } from "@/components/ui/slider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function PropertyFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get initial values from URL
  const [city, setCity] = useState(searchParams.get("city") || "")
  const [propertyType, setPropertyType] = useState(searchParams.get("type") || "")
  const [minPrice, setMinPrice] = useState(searchParams.get("minPrice") || "500")
  const [maxPrice, setMaxPrice] = useState(searchParams.get("maxPrice") || "5000")
  const [bedrooms, setBedrooms] = useState(searchParams.get("bedrooms") || "")
  const [priceRange, setPriceRange] = useState<[number, number]>([Number.parseInt(minPrice), Number.parseInt(maxPrice)])

  // Update price range when min/max price changes
  useEffect(() => {
    setPriceRange([Number.parseInt(minPrice), Number.parseInt(maxPrice)])
  }, [minPrice, maxPrice])

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange([values[0], values[1]])
    setMinPrice(values[0].toString())
    setMaxPrice(values[1].toString())
  }

  const applyFilters = () => {
    const params = new URLSearchParams()

    if (city) params.set("city", city)
    if (propertyType) params.set("type", propertyType)
    if (minPrice) params.set("minPrice", minPrice)
    if (maxPrice) params.set("maxPrice", maxPrice)
    if (bedrooms) params.set("bedrooms", bedrooms)

    // Preserve search query if it exists
    const searchQuery = searchParams.get("search")
    if (searchQuery) params.set("search", searchQuery)

    router.push(`/properties?${params.toString()}`)
  }

  const clearFilters = () => {
    setCity("")
    setPropertyType("")
    setMinPrice("500")
    setMaxPrice("5000")
    setBedrooms("")
    setPriceRange([500, 5000])

    // Preserve only search query if it exists
    const searchQuery = searchParams.get("search")
    if (searchQuery) {
      router.push(`/properties?search=${searchQuery}`)
    } else {
      router.push("/properties")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Select value={city} onValueChange={setCity}>
            <SelectTrigger id="city">
              <SelectValue placeholder="Select city" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Cities</SelectItem>
              <SelectItem value="Vancouver">Vancouver</SelectItem>
              <SelectItem value="Toronto">Toronto</SelectItem>
              <SelectItem value="Montreal">Montreal</SelectItem>
              <SelectItem value="Calgary">Calgary</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="property-type">Property Type</Label>
          <Select value={propertyType} onValueChange={setPropertyType}>
            <SelectTrigger id="property-type">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="apartment">Apartment</SelectItem>
              <SelectItem value="house">House</SelectItem>
              <SelectItem value="condo">Condo</SelectItem>
              <SelectItem value="townhouse">Townhouse</SelectItem>
              <SelectItem value="studio">Studio</SelectItem>
              <SelectItem value="loft">Loft</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Price Range ($/month)</Label>
          <div className="pt-4 pb-2">
            <Slider min={500} max={5000} step={100} value={priceRange} onValueChange={handlePriceRangeChange} />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">${priceRange[0]}</span>
            <span className="text-sm">${priceRange[1]}</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="bedrooms">Minimum Bedrooms</Label>
          <Select value={bedrooms} onValueChange={setBedrooms}>
            <SelectTrigger id="bedrooms">
              <SelectValue placeholder="Select bedrooms" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any">Any</SelectItem>
              <SelectItem value="1">1+</SelectItem>
              <SelectItem value="2">2+</SelectItem>
              <SelectItem value="3">3+</SelectItem>
              <SelectItem value="4">4+</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2 pt-2">
          <Button onClick={applyFilters}>Apply Filters</Button>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
