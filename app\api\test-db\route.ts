import { NextResponse } from 'next/server';
import clientPromise from "@/lib/mongodb/client";

export async function GET() {
  try {
    console.log('Testing database connection...');

    // Connect to MongoDB directly
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Count all properties
    const totalCount = await propertiesCollection.countDocuments();
    console.log(`Total properties: ${totalCount}`);

    // Count properties by status
    const pendingCount = await propertiesCollection.countDocuments({ status: 'pending' });
    const approvedCount = await propertiesCollection.countDocuments({ status: 'approved' });

    console.log(`Properties with status 'pending': ${pendingCount}`);
    console.log(`Properties with status 'approved': ${approvedCount}`);

    // Get a sample property
    const sampleProperty = await propertiesCollection.findOne();

    // Get collections in the database
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    return NextResponse.json({
      success: true,
      totalCount,
      pendingCount,
      approvedCount,
      collections: collectionNames,
      sampleProperty: sampleProperty ? {
        id: sampleProperty._id.toString(),
        title: sampleProperty.title,
        city: sampleProperty.city,
        status: sampleProperty.status
      } : null
    });
  } catch (error) {
    console.error('Error testing database:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to test database', message: error.message },
      { status: 500 }
    );
  }
}
