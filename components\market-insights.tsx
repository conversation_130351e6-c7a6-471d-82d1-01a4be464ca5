"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BarChart, Bar, LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { BadgeDollarSign, Building2, ChevronDown, ChevronUp, Home, TrendingUp, TrendingDown } from "lucide-react"

interface RentalMarketData {
  cityName: string
  averageRent: number
  changeYoY: number
  vacancy: number
  supplyDemandRatio: number
  priceHistory: {
    month: string
    studio: number
    oneBed: number
    twoBed: number
    threeBed: number
  }[]
  neighborhoodPrices: {
    name: string
    average: number
    change: number
  }[]
  propertyTypeDistribution: {
    type: string
    percentage: number
    count: number
  }[]
}

// Generate mock data for the rental market
const generateRentalData = (location: string): RentalMarketData => {
  // Create mock data based on the city (simplified for demo purposes)
  const cityMultiplier = {
    "Vancouver": 1.4,
    "Toronto": 1.3,
    "Montreal": 0.8,
    "Calgary": 0.9,
    "Edmonton": 0.85,
    "Ottawa": 0.95,
    "Winnipeg": 0.75,
    "Halifax": 0.85,
    "Victoria": 1.1,
    "Quebec City": 0.7
  }
  
  const multiplier = cityMultiplier[location as keyof typeof cityMultiplier] || 1;
  const baseRent = 1800 * multiplier;
  
  // Generate neighborhood names based on city
  const neighborhoods = [
    `${location} Downtown`,
    `${location} West`,
    `${location} East`,
    `${location} North`,
    `${location} South`,
  ];
  
  // Price history - past 12 months
  const priceHistory = Array.from({ length: 12 }, (_, i) => {
    const date = new Date();
    date.setMonth(date.getMonth() - (11 - i));
    const month = date.toLocaleString('default', { month: 'short' });
    
    // Add some variability to prices
    const trendFactor = 1 + (i * 0.005); // Slight upward trend
    const randomFactor = 0.95 + (Math.random() * 0.1); // ±5% random variation
    
    return {
      month,
      studio: Math.round((baseRent * 0.7 * trendFactor * randomFactor) / 100) * 100,
      oneBed: Math.round((baseRent * trendFactor * randomFactor) / 100) * 100,
      twoBed: Math.round((baseRent * 1.5 * trendFactor * randomFactor) / 100) * 100,
      threeBed: Math.round((baseRent * 2.2 * trendFactor * randomFactor) / 100) * 100,
    };
  });
  
  // Neighborhood prices
  const neighborhoodPrices = neighborhoods.map((name, index) => {
    // Downtown is usually more expensive
    const neighborhoodFactor = index === 0 ? 1.2 : 0.85 + (Math.random() * 0.3);
    
    return {
      name,
      average: Math.round((baseRent * neighborhoodFactor) / 50) * 50,
      change: Math.round((Math.random() * 10 - 2) * 10) / 10 // Between -2% and +8%
    };
  });
  
  // Property type distribution
  const propertyTypeDistribution = [
    {
      type: "Apartment",
      percentage: Math.round(45 + Math.random() * 15),
      count: Math.round((2000 + Math.random() * 3000) * multiplier)
    },
    {
      type: "House",
      percentage: Math.round(20 + Math.random() * 10),
      count: Math.round((800 + Math.random() * 1200) * multiplier)
    },
    {
      type: "Condo",
      percentage: Math.round(15 + Math.random() * 10),
      count: Math.round((600 + Math.random() * 800) * multiplier)
    },
    {
      type: "Townhouse",
      percentage: Math.round(10 + Math.random() * 10),
      count: Math.round((400 + Math.random() * 600) * multiplier)
    },
  ];
  
  // Ensure percentages sum to 100%
  const totalPercentage = propertyTypeDistribution.reduce((sum, item) => sum + item.percentage, 0);
  propertyTypeDistribution.forEach(item => {
    item.percentage = Math.round(item.percentage * 100 / totalPercentage);
  });
  
  return {
    cityName: location,
    averageRent: Math.round(baseRent / 50) * 50,
    changeYoY: Math.round((Math.random() * 8 - 1) * 10) / 10, // Between -1% and +7%
    vacancy: Math.round((Math.random() * 4 + 1) * 10) / 10, // Between 1% and 5%
    supplyDemandRatio: Math.round((Math.random() * 0.4 + 0.8) * 100) / 100, // Between 0.8 and 1.2
    priceHistory,
    neighborhoodPrices,
    propertyTypeDistribution
  };
};

export function MarketInsights({ location = "Vancouver" }: { location?: string }) {
  const [marketData, setMarketData] = useState<RentalMarketData | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    // Simulate API call with a delay
    setLoading(true);
    const timer = setTimeout(() => {
      const data = generateRentalData(location);
      setMarketData(data);
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, [location]);
  
  if (loading) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="animate-pulse text-lg">Loading market data...</div>
      </div>
    );
  }
  
  if (!marketData) {
    return <div className="text-red-500">Error loading market data</div>;
  }
  
  return (
    <div className="space-y-8">
      {/* Market overview section */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-2xl">{marketData.cityName} Rental Market Overview</CardTitle>
          <CardDescription>
            Current rental market statistics and trends for {marketData.cityName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Average Rent Card */}
            <Card className="bg-muted/50">
              <CardHeader className="pb-1">
                <CardDescription>Average Monthly Rent</CardDescription>
                <CardTitle className="text-3xl">${marketData.averageRent}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-sm">
                  {marketData.changeYoY >= 0 ? (
                    <>
                      <div className="bg-emerald-100 p-1 rounded-full">
                        <TrendingUp className="h-3.5 w-3.5 text-emerald-500" />
                      </div>
                      <span className="text-emerald-600">+{marketData.changeYoY}% from last year</span>
                    </>
                  ) : (
                    <>
                      <div className="bg-rose-100 p-1 rounded-full">
                        <TrendingDown className="h-3.5 w-3.5 text-rose-500" />
                      </div>
                      <span className="text-rose-600">{marketData.changeYoY}% from last year</span>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Vacancy Rate Card */}
            <Card className="bg-muted/50">
              <CardHeader className="pb-1">
                <CardDescription>Vacancy Rate</CardDescription>
                <CardTitle className="text-3xl">{marketData.vacancy}%</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-sm">
                  {marketData.vacancy < 3 ? (
                    <span className="text-amber-600">Low vacancy (landlord's market)</span>
                  ) : marketData.vacancy > 5 ? (
                    <span className="text-emerald-600">High vacancy (renter's market)</span>
                  ) : (
                    <span className="text-blue-600">Balanced market</span>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Supply/Demand Card */}
            <Card className="bg-muted/50">
              <CardHeader className="pb-1">
                <CardDescription>Supply/Demand Ratio</CardDescription>
                <CardTitle className="text-3xl">{marketData.supplyDemandRatio}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-sm">
                  {marketData.supplyDemandRatio < 1 ? (
                    <span className="text-amber-600">Demand exceeds supply</span>
                  ) : (
                    <span className="text-emerald-600">Supply meets demand</span>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Property Distribution Card */}
            <Card className="bg-muted/50">
              <CardHeader className="pb-1">
                <CardDescription>Most Common Property</CardDescription>
                <CardTitle className="text-xl truncate">
                  {marketData.propertyTypeDistribution.sort((a, b) => b.percentage - a.percentage)[0].type}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-sm">
                  <div className="bg-blue-100 p-1 rounded-full">
                    <Building2 className="h-3.5 w-3.5 text-blue-500" />
                  </div>
                  <span>
                    {marketData.propertyTypeDistribution.sort((a, b) => b.percentage - a.percentage)[0].percentage}% of listings
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
      
      {/* Detailed analysis tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Market Analysis</CardTitle>
          <CardDescription>
            Explore trends, prices, and market composition in {marketData.cityName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="price-trends">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="price-trends">Price Trends</TabsTrigger>
              <TabsTrigger value="neighborhoods">Neighborhoods</TabsTrigger>
              <TabsTrigger value="property-types">Property Types</TabsTrigger>
            </TabsList>
            
            <TabsContent value="price-trends" className="pt-6 min-h-[400px]">
              <h3 className="text-lg font-semibold mb-4">Rental Price Trends (Last 12 Months)</h3>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={marketData.priceHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [`$${value}`, '']}
                    labelFormatter={(label) => `${label}`}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="studio" 
                    name="Studio" 
                    stroke="#8884d8" 
                    activeDot={{ r: 8 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="oneBed" 
                    name="1 Bedroom" 
                    stroke="#82ca9d"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="twoBed" 
                    name="2 Bedrooms" 
                    stroke="#ffc658"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="threeBed" 
                    name="3+ Bedrooms" 
                    stroke="#ff8042"
                  />
                </LineChart>
              </ResponsiveContainer>
            </TabsContent>
            
            <TabsContent value="neighborhoods" className="pt-6 min-h-[400px]">
              <h3 className="text-lg font-semibold mb-4">Average Rent by Neighborhood</h3>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={marketData.neighborhoodPrices}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [`$${value}`, 'Average Rent']}
                  />
                  <Bar 
                    dataKey="average" 
                    name="Average Rent" 
                    fill="hsl(var(--primary))"
                  />
                </BarChart>
              </ResponsiveContainer>
              
              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
                <h3 className="text-lg font-semibold md:col-span-2">Year-over-Year Change by Neighborhood</h3>
                {marketData.neighborhoodPrices.map((neighborhood) => (
                  <Card key={neighborhood.name} className="overflow-hidden border-0 property-card-shadow">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">{neighborhood.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="pb-4">
                      <div className="flex justify-between items-center">
                        <div className="text-2xl font-bold">${neighborhood.average}</div>
                        <div className={`flex items-center gap-1 ${
                          neighborhood.change >= 0 ? "text-emerald-600" : "text-rose-600"
                        }`}>
                          {neighborhood.change >= 0 ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                          <span className="font-medium">
                            {neighborhood.change >= 0 ? "+" : ""}{neighborhood.change}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="property-types" className="pt-6 min-h-[400px]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Property Type Distribution</h3>
                  <ResponsiveContainer width="100%" height={320}>
                    <AreaChart
                      data={marketData.propertyTypeDistribution}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="type" />
                      <YAxis />
                      <Tooltip 
                        formatter={(value) => [`${value}%`, 'Percentage']}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="percentage" 
                        name="Percentage" 
                        stroke="hsl(var(--primary))" 
                        fill="hsl(var(--primary)/0.2)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Available Units by Type</h3>
                  <div className="grid grid-cols-1 gap-4">
                    {marketData.propertyTypeDistribution.map((type) => (
                      <div key={type.type} className="flex items-center justify-between p-4 rounded-lg border">
                        <div className="flex items-center gap-3">
                          {type.type === "Apartment" ? (
                            <Building2 className="h-5 w-5 text-primary" />
                          ) : type.type === "House" ? (
                            <Home className="h-5 w-5 text-primary" />
                          ) : (
                            <Building2 className="h-5 w-5 text-primary" />
                          )}
                          <span className="font-medium">{type.type}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{type.count.toLocaleString()} units</div>
                          <div className="text-sm text-muted-foreground">{type.percentage}% of market</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
