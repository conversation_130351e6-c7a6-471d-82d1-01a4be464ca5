import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface Message {
  id: string
  content: string
  senderId: string
  receiverId: string
  createdAt: string
  read: boolean
  sender: {
    id: string
    firstName: string
    lastName: string
  }
  receiver: {
    id: string
    firstName: string
    lastName: string
  }
}

export function useMessages(conversationId: string) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true

    async function fetchMessages() {
      try {
        const response = await fetch(`/api/messages?conversationId=${conversationId}`)
        if (!response.ok) throw new Error('Failed to fetch messages')
        const data = await response.json()
        if (mounted) {
          setMessages(data)
          setLoading(false)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to fetch messages')
          setLoading(false)
        }
      }
    }

    // Initial fetch
    fetchMessages()

    // Poll for new messages every 5 seconds
    const interval = setInterval(fetchMessages, 5000)

    return () => {
      mounted = false
      clearInterval(interval)
    }
  }, [conversationId])

  const sendMessage = async (content: string, receiverId: string) => {
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversationId,
          content,
          receiverId,
        }),
      })

      if (!response.ok) throw new Error('Failed to send message')
      const newMessage = await response.json()
      setMessages((prev) => [...prev, newMessage])
      return newMessage
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
      throw err
    }
  }

  return {
    messages,
    loading,
    error,
    sendMessage,
  }
} 