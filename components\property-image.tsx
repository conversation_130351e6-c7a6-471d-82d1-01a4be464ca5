"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface PropertyImageProps extends React.ComponentPropsWithoutRef<typeof Image> {
  fallbackSrc?: string
}

export default function PropertyImage({
  src,
  alt,
  fallbackSrc = "/images/property-placeholder.jpg",
  className,
  ...props
}: PropertyImageProps) {
  const [imgSrc, setImgSrc] = useState(src)
  const [error, setError] = useState(false)

  const handleError = () => {
    if (!error) {
      setImgSrc(fallbackSrc)
      setError(true)
    }
  }

  return (
    <Image
      src={imgSrc || "/placeholder.svg"}
      alt={alt}
      className={cn("object-cover", className)}
      onError={handleError}
      {...props}
    />
  )
}
