// Script to check the database for properties
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function checkDatabase() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }
  
  console.log('Connecting to MongoDB...');
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    
    // Check collections
    const collections = await db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Check properties collection
    const propertiesCollection = db.collection('properties');
    const propertyCount = await propertiesCollection.countDocuments();
    console.log(`\nTotal properties in database: ${propertyCount}`);
    
    if (propertyCount > 0) {
      // Count by property type
      const propertyTypes = await propertiesCollection.aggregate([
        { $group: { _id: "$propertyType", count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]).toArray();
      
      console.log('\nProperties by type:');
      propertyTypes.forEach(type => {
        console.log(`- ${type._id || 'undefined'}: ${type.count}`);
      });
      
      // Get a sample property
      const sampleProperty = await propertiesCollection.findOne({});
      console.log('\nSample property:');
      console.log(JSON.stringify(sampleProperty, null, 2));
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await client.close();
    console.log('\nDatabase connection closed');
  }
}

checkDatabase().catch(console.error);
