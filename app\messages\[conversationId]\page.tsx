'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Phone, Video, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Chat } from '@/components/messaging/chat';
import { Skeleton } from '@/components/ui/skeleton';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface ConversationPageProps {
  params: {
    conversationId: string;
  };
}

export default function ConversationPage({ params }: ConversationPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [conversation, setConversation] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Destructure conversationId from params to avoid direct access warning
  const { conversationId } = params;

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchConversation();
    }
  }, [status, conversationId]);

  const fetchConversation = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/conversations/${conversationId}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch conversation');
      }

      const data = await response.json();
      setConversation(data);
    } catch (error) {
      console.error('Error fetching conversation:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      toast.error('Failed to load conversation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkAsRead = async () => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/read`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Failed to mark conversation as read');

      // Update the conversation state to reflect it's been read
      if (conversation) {
        setConversation({
          ...conversation,
          unreadCount: 0
        });
      }
    } catch (error) {
      console.error('Error marking conversation as read:', error);
    }
  };

  const handleDeleteConversation = async () => {
    if (!confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete conversation');

      toast.success('Conversation deleted');
      router.push('/messages');
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation');
    }
  };

  // Mark conversation as read when it's loaded
  useEffect(() => {
    if (conversation && conversation.unreadCount > 0) {
      handleMarkAsRead();
    }
  }, [conversation]);

  if (status === 'loading' || isLoading) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex items-center mb-6">
          <Skeleton className="h-8 w-8 rounded-full mr-3" />
          <Skeleton className="h-6 w-32" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-start space-x-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={() => router.push('/messages')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Messages
          </Button>
        </div>
        <div className="text-center py-12">
          <h2 className="text-xl font-medium mb-2">Error loading conversation</h2>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={fetchConversation}>Try Again</Button>
        </div>
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={() => router.push('/messages')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Messages
          </Button>
        </div>
        <div className="text-center py-12">
          <h2 className="text-xl font-medium mb-2">Conversation not found</h2>
          <p className="text-gray-500">
            The conversation you're looking for doesn't exist or you don't have access to it.
          </p>
        </div>
      </div>
    );
  }

  // Determine the other user in the conversation
  const otherUser = conversation.userA.id === session.user.id
    ? conversation.userB
    : conversation.userA;

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" onClick={() => router.push('/messages')} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex items-center">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
              {otherUser.image ? (
                <img
                  src={otherUser.image}
                  alt={otherUser.name}
                  className="h-10 w-10 rounded-full object-cover"
                />
              ) : (
                <span className="text-lg font-medium">{otherUser.name.charAt(0)}</span>
              )}
            </div>
            <div>
              <h2 className="font-medium">{otherUser.name}</h2>
              {isTyping ? (
                <p className="text-xs text-muted-foreground">Typing...</p>
              ) : (
                <p className="text-xs text-muted-foreground">Active now</p>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Video className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleMarkAsRead}>
                Mark as read
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/profile/${otherUser.id}`)}>
                View profile
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive" onClick={handleDeleteConversation}>
                Delete conversation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden h-[calc(100vh-12rem)]">
        <Chat
          conversationId={conversationId}
          otherUser={otherUser}
          isTyping={isTyping}
        />
      </div>
    </div>
  );
}