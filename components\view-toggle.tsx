"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Grid, List } from "lucide-react"

export function ViewToggle() {
  const [view, setView] = useState<"grid" | "list">("grid")

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant={view === "grid" ? "default" : "outline"}
        size="icon"
        onClick={() => setView("grid")}
        aria-label="Grid view"
      >
        <Grid className="h-4 w-4" />
      </Button>
      <Button
        variant={view === "list" ? "default" : "outline"}
        size="icon"
        onClick={() => setView("list")}
        aria-label="List view"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  )
}
