import { NextResponse } from 'next/server';
import clientPromise from "@/lib/mongodb/client";
import { ObjectId } from "mongodb";

export async function GET() {
  try {
    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    
    // Create a pending property
    const pendingProperty = {
      title: "Luxury Penthouse - PENDING APPROVAL",
      description: "This is a pending property that needs admin approval. Stunning penthouse with panoramic city views.",
      address: "789 Skyline Drive",
      city: "Toronto",
      province: "Ontario",
      postalCode: "M5V 3L9",
      propertyType: "penthouse",
      bedrooms: 3,
      bathrooms: 3,
      squareFeet: 2200,
      price: 4500,
      availableFrom: new Date("2023-12-15"),
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Find a landlord user to associate with the property
    const landlord = await db.collection('users').findOne({ isLandlord: true });
    
    if (landlord) {
      pendingProperty.landlordId = landlord._id.toString();
      console.log(`Using landlord: ${landlord.firstName} ${landlord.lastName} (${landlord._id})`);
    } else {
      console.log('No landlord found, creating a new one');
      
      // Create a landlord user
      const newLandlord = {
        firstName: 'Test',
        lastName: 'Landlord',
        email: '<EMAIL>',
        isLandlord: true,
        role: 'landlord',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await db.collection('users').insertOne(newLandlord);
      pendingProperty.landlordId = result.insertedId.toString();
      console.log(`Created new landlord with ID: ${result.insertedId}`);
    }
    
    // Insert the pending property
    const propertyResult = await db.collection('properties').insertOne(pendingProperty);
    console.log(`Added pending property with ID: ${propertyResult.insertedId}`);
    
    // Add some images for the property
    const images = [
      {
        propertyId: propertyResult.insertedId,
        imageUrl: "https://images.unsplash.com/photo-1551361415-69c87624334f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y29uZG98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60",
        isPrimary: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        propertyId: propertyResult.insertedId,
        imageUrl: "https://images.unsplash.com/photo-1567496898669-ee935f5f647a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGNvbmRvfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
        isPrimary: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    await db.collection('propertyImages').insertMany(images);
    console.log(`Added ${images.length} images for the property`);
    
    // Count properties by status
    const pendingCount = await db.collection('properties').countDocuments({ status: 'pending' });
    const approvedCount = await db.collection('properties').countDocuments({ status: 'approved' });
    
    return NextResponse.json({
      success: true,
      message: 'Pending property added successfully',
      propertyId: propertyResult.insertedId.toString(),
      pendingCount,
      approvedCount
    });
  } catch (error) {
    console.error('Error adding pending property:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to add pending property' },
      { status: 500 }
    );
  }
}
