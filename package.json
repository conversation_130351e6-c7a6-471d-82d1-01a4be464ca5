{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/ensure-upload-dir.js && node scripts/update-user-roles.js && next dev -H 0.0.0.0 -p 8765", "dev-ui": "next dev -H 0.0.0.0 -p 8765", "dev-ui-only": "next dev -H 0.0.0.0 -p 8765", "dev-alt-port": "next dev -H 0.0.0.0 -p 3000", "build": "next build", "build:windows": "cross-env NODE_OPTIONS=\"--no-warnings\" NEXT_SKIP_TYPECHECKING=true NEXT_SKIP_ESLINT_CHECK=true next build --no-lint", "start": "next start -H 0.0.0.0 -p 8765", "prod": "next build && next start -H 0.0.0.0 -p 8765", "lint": "next lint", "clean": "if exist .next rmdir /s /q .next", "ensure-upload-dir": "node scripts/ensure-upload-dir.js", "update-user-roles": "node scripts/update-user-roles.js"}, "dependencies": {"@auth/mongodb-adapter": "^3.8.0", "@auth/prisma-adapter": "^2.8.0", "@firebase/analytics": "^0.10.12", "@firebase/auth": "^1.10.0", "@googlemaps/js-api-loader": "latest", "@hookform/resolvers": "latest", "@prisma/client": "^5.10.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "autoprefixer": "^10.4.20", "aws-amplify": "^6.14.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "embla-carousel-react": "latest", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "mongodb": "^6.3.0", "mongoose": "^8.0.0", "next": "^15.3.1", "next-auth": "^4.24.11", "next-themes": "latest", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "latest", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "latest", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@types/firebase": "^2.4.32", "@types/node": "^22.14.1", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "postcss": "^8", "prisma": "^5.10.2", "tailwindcss": "^3.4.17", "typescript": "^5", "uuid": "^11.1.0"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"react": "19", "react-dom": "19"}}, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "installCommand": "pnpm install --legacy-peer-deps"}}