"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/components/auth-provider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface ApplyButtonProps {
  propertyId: string
  className?: string
}

export function ApplyButton({ propertyId, className }: ApplyButtonProps) {
  const router = useRouter()
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)

  const handleApply = () => {
    if (!user) {
      setDialogOpen(true)
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Application submitted",
        description: "Your application has been submitted successfully.",
      })
      router.push("/applications")
    }, 1000)
  }

  return (
    <>
      <Button onClick={handleApply} className={className} disabled={isLoading}>
        {isLoading ? "Submitting..." : "Apply Now"}
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sign in required</DialogTitle>
            <DialogDescription>You need to be signed in to apply for this property.</DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row sm:justify-between">
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => router.push("/auth/signup")}>
                Sign Up
              </Button>
              <Button onClick={() => router.push("/auth/login")}>Log In</Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
