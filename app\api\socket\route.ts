import { Server } from 'socket.io';
import { NextRequest } from 'next/server';

// Socket.IO handler for Next.js Route Handlers
export async function GET(req: NextRequest) {
  if (!(globalThis as any).io) {
    const io = new Server({
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });

    io.on('connection', (socket) => {
      console.log('A user connected:', socket.id);

      // Join a chat room (conversation)
      socket.on('join_conversation', (conversationId) => {
        socket.join(conversationId);
        console.log(`User ${socket.id} joined conversation: ${conversationId}`);
      });

      // Leave a chat room
      socket.on('leave_conversation', (conversationId) => {
        socket.leave(conversationId);
        console.log(`User ${socket.id} left conversation: ${conversationId}`);
      });

      // Handle typing indicators
      socket.on('typing_start', (conversationId) => {
        socket.to(conversationId).emit('typing_start');
      });

      socket.on('typing_stop', (conversationId) => {
        socket.to(conversationId).emit('typing_stop');
      });

      // Listen for new messages
      socket.on('send_message', (data) => {
        // Emit to all users in the conversation
        io.to(data.conversationId).emit('receive_message', {
          ...data,
          status: 'sent',
          timestamp: new Date(),
        });
        
        // Emit message received event to sender
        socket.emit('message_received', {
          messageId: data.id,
          status: 'delivered',
          timestamp: new Date(),
        });
      });

      // Handle message read status
      socket.on('mark_as_read', (data) => {
        io.to(data.conversationId).emit('message_read', {
          messageId: data.messageId,
          status: 'read',
          timestamp: new Date(),
        });
      });

      socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
      });
    });

    // Start the Socket.IO server on port 3001
    io.listen(3001);
    (globalThis as any).io = io;
  }

  return new Response('Socket.IO server is running', { status: 200 });
} 