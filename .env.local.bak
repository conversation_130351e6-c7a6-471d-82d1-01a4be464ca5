# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDX1pbObZ_-xw9HU1xi5gPlRyz6c1yRu3M
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=copper-9dd7f.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=copper-9dd7f
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=copper-9dd7f.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=108727998523
NEXT_PUBLIC_FIREBASE_APP_ID=1:108727998523:web:a8e0cd720c58c65460e41e
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-EPNW6GD2JG

# Firebase Admin SDK
FIREBASE_PROJECT_ID=copper-9dd7f
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC9QFbDLh8HhXxN\nYOUR_ACTUAL_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"

# MongoDB
MONGODB_URI="mongodb+srv://backend:<EMAIL>/rentcentral?retryWrites=true&w=majority&appName=AWSMongoDB"
MONGODB_DB=rentcentral

# Google Maps API (for property maps)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# API Base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# Rentcast API
RENTCAST_API_KEY=38289d03f1b74fb6b598e14346dd0c48

# NextAuth.js Configuration
NEXTAUTH_SECRET=8I0gjaTtKi7KonB88saChUc/xve5h2KkraceJNKMsyg=
NEXTAUTH_URL=http://localhost:3000

# Add any other environment variables your application needs
