'use client';

import MessagingSidebar from './MessagingSidebar';

type User = {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
};

type MessagingSidebarWrapperProps = {
  currentUser: User;
};

const MessagingSidebarWrapper = ({ currentUser }: MessagingSidebarWrapperProps) => {
  // Just pass the currentUser to the MessagingSidebar component
  return <MessagingSidebar currentUser={currentUser} />;
};

export default MessagingSidebarWrapper; 