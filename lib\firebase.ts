import { initializeApp, getApps } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDX1pbObZ_-xw9HU1xi5gPlRyz6c1yRu3M",
  authDomain: "copper-9dd7f.firebaseapp.com",
  projectId: "copper-9dd7f",
  storageBucket: "copper-9dd7f.firebasestorage.app",
  messagingSenderId: "108727998523",
  appId: "1:108727998523:web:a8e0cd720c58c65460e41e",
  measurementId: "G-EPNW6GD2JG"
};

// Initialize Firebase
let app;
let auth;
let firestore;
let analytics;

try {
  app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
  auth = getAuth(app);
  firestore = getFirestore(app);
  
  // Only initialize analytics on the client side
  if (typeof window !== 'undefined') {
    analytics = getAnalytics(app);
  }
} catch (error) {
  console.warn('Firebase initialization error:', error);
  console.log('Using mock Firebase for development');
  
  // Create empty mock services if Firebase initialization fails
  app = null;
  auth = {
    currentUser: null,
    onAuthStateChanged: (callback: (user: null) => void) => callback(null),
    signInWithEmailAndPassword: async () => ({ user: null }),
    createUserWithEmailAndPassword: async () => ({ user: null }),
    signOut: async () => {}
  };
  firestore = {
    collection: () => ({
      doc: () => ({
        get: async () => ({ exists: false, data: () => ({}) }),
        set: async () => {}
      })
    })
  };
}

export { auth, firestore };
export default app; 