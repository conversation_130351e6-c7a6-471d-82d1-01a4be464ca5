// Mock AWS Amplify implementation

// Mock Auth class
export const Auth = {
  // Current session
  currentSession: async () => ({
    getIdToken: () => ({
      getJwtToken: () => "mock-jwt-token"
    })
  }),
  
  // Current authenticated user
  currentAuthenticatedUser: async () => ({
    attributes: {
      sub: "mock-user-id",
      email: "<EMAIL>",
      name: "Mock User"
    }
  }),
  
  // Sign in
  signIn: async (username: string, password: string) => ({
    user: {
      username,
      attributes: {
        email: username,
        name: "<PERSON><PERSON> User"
      }
    }
  }),
  
  // Sign up
  signUp: async (username: string, password: string, attributes: any) => ({
    user: {
      username,
      attributes
    }
  }),
  
  // Sign out
  signOut: async () => ({}),
  
  // Other common Auth methods
  forgotPassword: async (username: string) => ({}),
  forgotPasswordSubmit: async (username: string, code: string, newPassword: string) => ({})
};

// Export mock AWS Amplify modules
export default {
  Auth
}; 