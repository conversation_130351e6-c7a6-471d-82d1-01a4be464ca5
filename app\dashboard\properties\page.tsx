"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { propertiesApi } from "@/lib/aws/api"
import { useAuth } from "@/lib/aws/auth"
import { Plus, Edit, Trash, Eye, Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  Dialog<PERSON>it<PERSON>,
} from "@/components/ui/dialog"

export default function PropertiesPage() {
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const [properties, setProperties] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [propertyToDelete, setPropertyToDelete] = useState<any>(null)

  useEffect(() => {
    if (user) {
      fetchProperties()
    }
  }, [user])

  const fetchProperties = async () => {
    setIsLoading(true)
    try {
      const data = await propertiesApi.getLandlordProperties()
      setProperties(data.properties || [])
    } catch (error) {
      console.error("Error fetching properties:", error)
      toast({
        title: "Error",
        description: "Failed to load your properties. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteProperty = async () => {
    if (!propertyToDelete) return

    try {
      await propertiesApi.deleteProperty(propertyToDelete.id)

      // Update local state
      setProperties(properties.filter((p) => p.id !== propertyToDelete.id))

      toast({
        title: "Property deleted",
        description: "Your property has been deleted successfully.",
      })
    } catch (error) {
      console.error("Error deleting property:", error)
      toast({
        title: "Error",
        description: "Failed to delete property. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDeleteDialogOpen(false)
      setPropertyToDelete(null)
    }
  }

  // Filter properties based on active tab
  const filteredProperties = properties.filter((property) => {
    if (activeTab === "all") return true
    if (activeTab === "active") return property.status === "approved"
    if (activeTab === "pending") return property.status === "pending"
    if (activeTab === "rejected") return property.status === "rejected"
    return true
  })

  return (
    <DashboardLayout>
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight md:text-4xl">My Properties</h1>
            <p className="text-muted-foreground">Manage your property listings</p>
          </div>
          <Button onClick={() => router.push("/dashboard/properties/new")}>
            <Plus className="mr-2 h-4 w-4" />
            Add New Property
          </Button>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList>
            <TabsTrigger value="all">All Properties</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-6">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-[300px] w-full rounded-lg" />
                ))}
              </div>
            ) : filteredProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProperties.map((property) => (
                  <PropertyCard
                    key={property.id}
                    property={property}
                    onDelete={(property) => {
                      setPropertyToDelete(property)
                      setDeleteDialogOpen(true)
                    }}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="rounded-full bg-muted p-3 mb-4">
                    {activeTab === "all" ? (
                      <Building className="h-6 w-6 text-muted-foreground" />
                    ) : activeTab === "pending" ? (
                      <Clock className="h-6 w-6 text-muted-foreground" />
                    ) : activeTab === "rejected" ? (
                      <XCircle className="h-6 w-6 text-muted-foreground" />
                    ) : (
                      <CheckCircle className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <h3 className="text-lg font-medium mb-2">No properties found</h3>
                  <p className="text-muted-foreground mb-6">
                    {activeTab === "all"
                      ? "You haven't listed any properties yet."
                      : activeTab === "pending"
                        ? "You don't have any properties pending approval."
                        : activeTab === "rejected"
                          ? "You don't have any rejected properties."
                          : "You don't have any active property listings."}
                  </p>
                  <Button onClick={() => router.push("/dashboard/properties/new")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Property
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Property</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this property? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            {propertyToDelete && (
              <div className="py-4">
                <div className="flex items-center gap-4">
                  <div className="relative h-16 w-16 overflow-hidden rounded-md">
                    <Image
                      src={propertyToDelete.image || "/placeholder.svg"}
                      alt={propertyToDelete.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-medium">{propertyToDelete.title}</h4>
                    <p className="text-sm text-muted-foreground">{propertyToDelete.address}</p>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteProperty}>
                Delete Property
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}

function PropertyCard({ property, onDelete }: { property: any; onDelete: (property: any) => void }) {
  const router = useRouter()

  // Get status badge variant and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "approved":
        return {
          variant: "success" as const,
          icon: CheckCircle,
          label: "Active",
        }
      case "pending":
        return {
          variant: "secondary" as const,
          icon: Clock,
          label: "Pending Approval",
        }
      case "rejected":
        return {
          variant: "destructive" as const,
          icon: XCircle,
          label: "Rejected",
        }
      default:
        return {
          variant: "outline" as const,
          icon: AlertTriangle,
          label: status,
        }
    }
  }

  const { variant, icon: StatusIcon, label } = getStatusInfo(property.status)

  return (
    <Card className="overflow-hidden">
      <div className="relative h-48">
        <Image src={property.image || "/placeholder.svg"} alt={property.title} fill className="object-cover" />
        <Badge variant={variant} className="absolute top-2 right-2 flex items-center gap-1">
          <StatusIcon className="h-3 w-3" />
          {label}
        </Badge>
      </div>
      <CardHeader className="pb-2">
        <CardTitle className="line-clamp-1">{property.title}</CardTitle>
        <CardDescription className="line-clamp-1">{property.address}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex justify-between items-center">
          <div className="font-medium">
            ${property.price}
            <span className="text-sm font-normal text-muted-foreground">/month</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span>{property.bedrooms} bd</span>
            <span>•</span>
            <span>{property.bathrooms} ba</span>
          </div>
        </div>
        {property.status === "rejected" && (
          <div className="mt-2 p-2 bg-destructive/10 rounded-md text-sm">
            <p className="font-medium text-destructive">Rejection reason:</p>
            <p className="text-muted-foreground">{property.rejection_reason || "No reason provided."}</p>
          </div>
        )}
      </CardContent>
      <div className="p-4 pt-0 flex justify-between gap-2">
        <Button variant="outline" size="sm" className="flex-1" asChild>
          <Link href={`/properties/${property.id}`} legacyBehavior>
            <span className="flex items-center">
              <Eye className="mr-2 h-4 w-4" />
              View
            </span>
          </Link>
        </Button>
        <Button variant="outline" size="sm" className="flex-1" asChild>
          <Link href={`/dashboard/properties/${property.id}/edit`} legacyBehavior>
            <span className="flex items-center">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </span>
          </Link>
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="flex-1 text-destructive hover:text-destructive"
          onClick={() => onDelete(property)}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </Button>
      </div>
    </Card>
  );
}

function Building(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="16" height="20" x="4" y="2" rx="2" ry="2" />
      <path d="M9 22v-4h6v4" />
      <path d="M8 6h.01" />
      <path d="M16 6h.01" />
      <path d="M12 6h.01" />
      <path d="M12 10h.01" />
      <path d="M12 14h.01" />
      <path d="M16 10h.01" />
      <path d="M16 14h.01" />
      <path d="M8 10h.01" />
      <path d="M8 14h.01" />
    </svg>
  )
}
