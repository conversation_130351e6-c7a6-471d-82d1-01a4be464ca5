'use client';

import { useEffect, useState } from 'react';
import { Property, Application } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MapPin, Bed, Bath, DollarSign, User, Mail, Phone } from 'lucide-react';

export default function LandlordDashboard() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [newProperty, setNewProperty] = useState({
    title: '',
    description: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    price: '',
    bedrooms: '',
    bathrooms: '',
    squareFeet: '',
    amenities: [] as string[],
    images: [] as string[]
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [propertiesRes, applicationsRes] = await Promise.all([
        fetch('/api/properties'),
        fetch('/api/applications')
      ]);
      
      const propertiesData = await propertiesRes.json();
      const applicationsData = await applicationsRes.json();
      
      setProperties(propertiesData);
      setApplications(applicationsData);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddProperty = async () => {
    try {
      const response = await fetch('/api/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newProperty,
          price: Number(newProperty.price),
          bedrooms: Number(newProperty.bedrooms),
          bathrooms: Number(newProperty.bathrooms),
          squareFeet: Number(newProperty.squareFeet),
          landlordId: 'current-user-id' // This should be replaced with actual user ID
        }),
      });

      if (response.ok) {
        setNewProperty({
          title: '',
          description: '',
          address: {
            street: '',
            city: '',
            state: '',
            zipCode: ''
          },
          price: '',
          bedrooms: '',
          bathrooms: '',
          squareFeet: '',
          amenities: [],
          images: []
        });
        fetchData();
      }
    } catch (error) {
      console.error('Error adding property:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Landlord Dashboard</h1>

      <Tabs defaultValue="properties" className="space-y-6">
        <TabsList>
          <TabsTrigger value="properties">My Properties</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
          <TabsTrigger value="add-property">Add Property</TabsTrigger>
        </TabsList>

        <TabsContent value="properties">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {properties.map((property) => (
              <Card key={property._id.toString()}>
                <CardHeader>
                  <CardTitle>{property.title}</CardTitle>
                  <CardDescription className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {property.address.city}, {property.address.state}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <div className="flex items-center">
                        <Bed className="w-4 h-4 mr-1" />
                        <span>{property.bedrooms} beds</span>
                      </div>
                      <div className="flex items-center">
                        <Bath className="w-4 h-4 mr-1" />
                        <span>{property.bathrooms} baths</span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" />
                        <span>{property.price}/mo</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{property.description}</p>
                    <Button className="w-full">Manage Property</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="applications">
          <div className="space-y-6">
            {applications.map((application) => (
              <Card key={application._id.toString()}>
                <CardHeader>
                  <CardTitle>Application #{application._id.toString().slice(-4)}</CardTitle>
                  <CardDescription>
                    Status: <span className="capitalize">{application.status}</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-2">Applicant Information</h3>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          <span>Income: ${application.income}</span>
                        </div>
                        <div className="flex items-center">
                          <Mail className="w-4 h-4 mr-2" />
                          <span>Employment: {application.employmentStatus}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">References</h3>
                      {application.references.map((ref, index) => (
                        <div key={index} className="space-y-1 mb-2">
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-2" />
                            <span>{ref.name}</span>
                          </div>
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 mr-2" />
                            <span>{ref.contact}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Relationship: {ref.relationship}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" className="flex-1">Reject</Button>
                      <Button className="flex-1">Approve</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="add-property">
          <Card>
            <CardHeader>
              <CardTitle>Add New Property</CardTitle>
              <CardDescription>Fill in the details of your property</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Title</label>
                  <Input
                    value={newProperty.title}
                    onChange={(e) => setNewProperty({ ...newProperty, title: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Price</label>
                  <Input
                    type="number"
                    value={newProperty.price}
                    onChange={(e) => setNewProperty({ ...newProperty, price: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Bedrooms</label>
                  <Input
                    type="number"
                    value={newProperty.bedrooms}
                    onChange={(e) => setNewProperty({ ...newProperty, bedrooms: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Bathrooms</label>
                  <Input
                    type="number"
                    value={newProperty.bathrooms}
                    onChange={(e) => setNewProperty({ ...newProperty, bathrooms: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Square Feet</label>
                  <Input
                    type="number"
                    value={newProperty.squareFeet}
                    onChange={(e) => setNewProperty({ ...newProperty, squareFeet: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Street Address</label>
                  <Input
                    value={newProperty.address.street}
                    onChange={(e) => setNewProperty({
                      ...newProperty,
                      address: { ...newProperty.address, street: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">City</label>
                  <Input
                    value={newProperty.address.city}
                    onChange={(e) => setNewProperty({
                      ...newProperty,
                      address: { ...newProperty.address, city: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">State</label>
                  <Input
                    value={newProperty.address.state}
                    onChange={(e) => setNewProperty({
                      ...newProperty,
                      address: { ...newProperty.address, state: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">ZIP Code</label>
                  <Input
                    value={newProperty.address.zipCode}
                    onChange={(e) => setNewProperty({
                      ...newProperty,
                      address: { ...newProperty.address, zipCode: e.target.value }
                    })}
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={newProperty.description}
                  onChange={(e) => setNewProperty({ ...newProperty, description: e.target.value })}
                />
              </div>
              <Button className="w-full" onClick={handleAddProperty}>
                Add Property
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
