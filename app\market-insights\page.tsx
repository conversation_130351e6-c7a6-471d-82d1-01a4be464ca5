"use client"

import type React from "react"

import { useState } from "react"
import { MarketInsights } from "@/components/market-insights"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"

export default function MarketInsightsPage() {
  const [location, setLocation] = useState("Vancouver")
  const [searchInput, setSearchInput] = useState("Vancouver")

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setLocation(searchInput)
  }

  const popularCities = [
    "Vancouver",
    "Toronto",
    "Montreal",
    "Calgary",
    "Edmonton",
    "Ottawa",
    "Winnipeg",
    "Halifax",
    "Victoria",
    "Quebec City",
  ]

  return (
    <div className="container py-8">
      <div className="mb-8 space-y-4">
        <h1 className="text-3xl font-bold">Canadian Rental Market Insights</h1>
        <p className="text-muted-foreground">
          Explore rental market trends, average prices, and statistics for cities across Canada.
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Search Market Data</CardTitle>
          <CardDescription>Enter a city name to view rental market insights</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-2">
            <Input
              placeholder="Enter city name..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="max-w-md"
            />
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </form>

          <div className="mt-4">
            <div className="text-sm font-medium text-muted-foreground">Popular Cities:</div>
            <div className="mt-2 flex flex-wrap gap-2">
              {popularCities.map((city) => (
                <Button
                  key={city}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchInput(city)
                    setLocation(city)
                  }}
                >
                  {city}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <MarketInsights location={location} />
    </div>
  )
}
