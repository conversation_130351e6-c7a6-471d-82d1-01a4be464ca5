generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URI")
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  authId        String?   @unique
  firstName     String?
  lastName      String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  phone         String?
  postalCode    String?
  city          String?
  bio           String?
  isLandlord    Bo<PERSON>an   @default(false)
  role          String    @default("user")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  profile       Profile?
  sentMessages     Message[]   @relation("SentMessages")
  receivedMessages Message[]   @relation("ReceivedMessages")
  participantA     Conversation[] @relation("UserA")
  participantB     Conversation[] @relation("UserB")
  properties    Property[]
  applications  Application[]
  savedProperties SavedProperty[]
}

model Property {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  title         String
  description   String
  address       String
  city          String
  province      String
  postalCode    String
  propertyType  String
  bedrooms      Int
  bathrooms     Int
  squareFeet    Int
  price         Float
  availableFrom DateTime
  landlordId    String    @db.ObjectId
  landlord      User      @relation(fields: [landlordId], references: [id])
  status        String    @default("pending")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  images        PropertyImage[]
  amenities     PropertyAmenity[]
  applications  Application[]
  approvals     Approval[]
  savedBy       SavedProperty[]
}

model PropertyImage {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  propertyId  String    @db.ObjectId
  property    Property  @relation(fields: [propertyId], references: [id])
  imageUrl    String
  isPrimary   Boolean   @default(false)
  createdAt   DateTime  @default(now())
}

model Amenity {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String    @unique
  icon        String?
  createdAt   DateTime  @default(now())
  properties  PropertyAmenity[]
}

model PropertyAmenity {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  propertyId  String    @db.ObjectId
  amenityId   String    @db.ObjectId
  property    Property  @relation(fields: [propertyId], references: [id])
  amenity     Amenity   @relation(fields: [amenityId], references: [id])

  @@unique([propertyId, amenityId])
}

model Application {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  propertyId  String    @db.ObjectId
  userId      String    @db.ObjectId
  property    Property  @relation(fields: [propertyId], references: [id])
  user        User      @relation(fields: [userId], references: [id])
  status      String    @default("pending")
  message     String?
  appliedDate DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  payments    Payment[]
}

model Approval {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  propertyId      String    @db.ObjectId
  adminId         String?   @db.ObjectId
  status          String    @default("pending")
  rejectionReason String?
  approvedAt      DateTime?
  rejectedAt      DateTime?
  createdAt       DateTime  @default(now())
  property        Property  @relation(fields: [propertyId], references: [id])
}

model Payment {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  applicationId String    @db.ObjectId
  amount        Float
  status        String    @default("pending")
  paymentDate   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  application   Application @relation(fields: [applicationId], references: [id])
}

model SavedProperty {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  userId      String    @db.ObjectId
  propertyId  String    @db.ObjectId
  user        User      @relation(fields: [userId], references: [id])
  property    Property  @relation(fields: [propertyId], references: [id])
  createdAt   DateTime  @default(now())

  @@unique([userId, propertyId])
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Profile {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @unique @db.ObjectId
  bio       String?
  location  String?
  phone     String?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Conversation {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  userAId      String    @db.ObjectId
  userBId      String    @db.ObjectId
  userA        User      @relation("UserA", fields: [userAId], references: [id])
  userB        User      @relation("UserB", fields: [userBId], references: [id])
  messages     Message[]
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  lastMessageAt DateTime @default(now())
}

model Message {
  id             String       @id @default(auto()) @map("_id") @db.ObjectId
  content        String
  conversationId String       @db.ObjectId
  senderId       String       @db.ObjectId
  receiverId     String       @db.ObjectId
  sender         User         @relation("SentMessages", fields: [senderId], references: [id])
  receiver       User         @relation("ReceivedMessages", fields: [receiverId], references: [id])
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  read           Boolean      @default(false)
  createdAt      DateTime     @default(now())
} 