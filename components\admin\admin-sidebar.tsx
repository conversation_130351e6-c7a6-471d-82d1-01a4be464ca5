"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  <PERSON>bar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LayoutDashboard, Home, Building, Users, FileCheck, Settings, LogOut } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

export function AdminSidebar() {
  const pathname = usePathname()
  const router = useRouter()

  const handleLogout = () => {
    // Clear the admin token cookie
    document.cookie = "admin_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"

    toast({
      title: "Logged out",
      description: "You have been logged out of the admin dashboard.",
    })

    router.push("/admin/login")
  }

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <Building className="h-6 w-6 text-primary" />
          <span className="text-lg font-bold">Rent Central Admin</span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/admin/dashboard"}>
              <Link href="/admin/dashboard">
                <div className="flex items-center gap-2">
                  <LayoutDashboard className="h-4 w-4" />
                  <span>Dashboard</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={pathname === "/admin/properties" || pathname.startsWith("/admin/properties/")}
            >
              <Link href="/admin/properties">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  <span>Properties</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/admin/users"}>
              <Link href="/admin/users">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Users</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/admin/approvals"}>
              <Link href="/admin/approvals">
                <div className="flex items-center gap-2">
                  <FileCheck className="h-4 w-4" />
                  <span>Approvals</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/admin/settings"}>
              <Link href="/admin/settings">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <div className="space-y-4 px-4 py-2">
          <div className="flex items-center gap-2">
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link href="/" target="_blank">
                <div className="flex items-center">
                  <Home className="mr-2 h-4 w-4" />
                  View Site
                </div>
              </Link>
            </Button>
          </div>
          <Button variant="outline" size="sm" className="w-full" onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            Log Out
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
