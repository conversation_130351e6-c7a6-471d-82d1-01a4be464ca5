"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { fetchMarketTrends } from "@/app/actions/rentcast-actions"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { formatCurrency } from "@/lib/utils"

interface MarketTrendsProps {
  location: string
}

export function MarketTrends({ location }: MarketTrendsProps) {
  const [marketData, setMarketData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [propertyType, setPropertyType] = useState("apartment")
  const [bedrooms, setBedrooms] = useState(2)

  useEffect(() => {
    const loadMarketData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const data = await fetchMarketTrends(location, propertyType, bedrooms)
        if (data.error) {
          setError(data.error)
        } else {
          setMarketData(data)
        }
      } catch (error: any) {
        console.error("Error loading market data:", error)
        setError(error.message || "Failed to load market data")
      } finally {
        setIsLoading(false)
      }
    }

    if (location) {
      loadMarketData()
    }
  }, [location, propertyType, bedrooms])

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-[300px] w-full" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-[100px] w-full" />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Market Trends</CardTitle>
          <CardDescription>Error loading market data</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!marketData) {
    return (
      <div className="text-center text-muted-foreground">
        No market data available for {location}. Please try another location.
      </div>
    )
  }

  // Prepare data for the chart
  const chartData =
    marketData.rentTrends?.map((item: any) => ({
      month: item.month,
      rent: item.averageRent,
    })) || []

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{location} Market Insights</h2>
          <p className="text-muted-foreground">
            Rental market trends and statistics for {bedrooms}-bedroom {propertyType}s
          </p>
        </div>
        <div className="flex gap-2">
          <Tabs defaultValue={propertyType} onValueChange={setPropertyType}>
            <TabsList>
              <TabsTrigger value="apartment">Apartment</TabsTrigger>
              <TabsTrigger value="house">House</TabsTrigger>
              <TabsTrigger value="condo">Condo</TabsTrigger>
            </TabsList>
          </Tabs>
          <Tabs defaultValue={bedrooms.toString()} onValueChange={(value) => setBedrooms(Number.parseInt(value))}>
            <TabsList>
              <TabsTrigger value="1">1 Bed</TabsTrigger>
              <TabsTrigger value="2">2 Bed</TabsTrigger>
              <TabsTrigger value="3">3 Bed</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Average Rent</CardDescription>
            <CardTitle>{formatCurrency(marketData.averageRent || 0)}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              {marketData.rentGrowth > 0 ? "+" : ""}
              {marketData.rentGrowth || 0}% year over year
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Vacancy Rate</CardDescription>
            <CardTitle>{marketData.vacancyRate || 0}%</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              {marketData.vacancyRate < 3
                ? "Low vacancy"
                : marketData.vacancyRate > 5
                  ? "High vacancy"
                  : "Average vacancy"}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Days on Market</CardDescription>
            <CardTitle>{marketData.daysOnMarket || 0} days</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              {marketData.daysOnMarket < 14
                ? "Fast-moving market"
                : marketData.daysOnMarket > 30
                  ? "Slow-moving market"
                  : "Average-paced market"}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Rent Growth</CardDescription>
            <CardTitle>{marketData.rentGrowth || 0}%</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">Annual increase</div>
          </CardContent>
        </Card>
      </div>

      {chartData.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Rent Trends (12 Months)</CardTitle>
            <CardDescription>
              Average monthly rent for {bedrooms}-bedroom {propertyType}s in {location}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={["dataMin - 100", "dataMax + 100"]} tickFormatter={(value) => `$${value}`} />
                  <Tooltip
                    formatter={(value) => [`$${value}`, "Average Rent"]}
                    labelFormatter={(label) => {
                      const date = new Date(label + "-01")
                      return date.toLocaleDateString("en-CA", { month: "long", year: "numeric" })
                    }}
                  />
                  <Line type="monotone" dataKey="rent" stroke="#8884d8" activeDot={{ r: 8 }} name="Average Rent" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Rent Trends</CardTitle>
            <CardDescription>No trend data available</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Historical rent trend data is not available for this location and property type.
            </p>
          </CardContent>
        </Card>
      )}

      <div className="text-xs text-muted-foreground">Data source: {marketData.dataSource}</div>
    </div>
  )
}
