// This script adds a pending property to the database
require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');

async function main() {
  try {
    // Get the MongoDB URI from environment variables
    const uri = process.env.MONGODB_URI || process.env.DATABASE_URL;
    if (!uri) {
      throw new Error('No MongoDB URI found in environment variables');
    }

    console.log('Connecting to MongoDB...');
    console.log(`URI: ${uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`); // Hide credentials in logs
    
    const client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB successfully!');
    
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    console.log(`Using database: ${db.databaseName}`);
    
    // Create a pending property
    const pendingProperty = {
      title: "Luxury Penthouse - PENDING APPROVAL",
      description: "This is a pending property that needs admin approval. Stunning penthouse with panoramic city views.",
      address: "789 Skyline Drive",
      city: "Toronto",
      province: "Ontario",
      postalCode: "M5V 3L9",
      propertyType: "penthouse",
      bedrooms: 3,
      bathrooms: 3,
      squareFeet: 2200,
      price: 4500,
      availableFrom: new Date("2023-12-15"),
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
      images: [
        "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
        "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60"
      ]
    };
    
    // Find a landlord user to associate with the property
    const landlord = await db.collection('users').findOne({ isLandlord: true });
    
    if (landlord) {
      pendingProperty.landlordId = landlord._id.toString();
      console.log(`Using landlord: ${landlord.firstName} ${landlord.lastName} (${landlord._id})`);
    } else {
      console.log('No landlord found, creating a new one');
      
      // Create a landlord user
      const newLandlord = {
        firstName: 'Test',
        lastName: 'Landlord',
        email: '<EMAIL>',
        isLandlord: true,
        role: 'landlord',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await db.collection('users').insertOne(newLandlord);
      pendingProperty.landlordId = result.insertedId.toString();
      console.log(`Created new landlord with ID: ${result.insertedId}`);
    }
    
    // Insert the pending property
    const propertyResult = await db.collection('properties').insertOne(pendingProperty);
    console.log(`Added pending property with ID: ${propertyResult.insertedId}`);
    
    // Add some images for the property
    const images = [
      {
        propertyId: propertyResult.insertedId,
        imageUrl: "https://images.unsplash.com/photo-1551361415-69c87624334f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y29uZG98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60",
        isPrimary: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        propertyId: propertyResult.insertedId,
        imageUrl: "https://images.unsplash.com/photo-1567496898669-ee935f5f647a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGNvbmRvfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60",
        isPrimary: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    await db.collection('propertyImages').insertMany(images);
    console.log(`Added ${images.length} images for the property`);
    
    // Count properties by status
    const pendingCount = await db.collection('properties').countDocuments({ status: 'pending' });
    const approvedCount = await db.collection('properties').countDocuments({ status: 'approved' });
    
    console.log(`\nProperties with status 'pending': ${pendingCount}`);
    console.log(`Properties with status 'approved': ${approvedCount}`);
    
    await client.close();
    console.log('\nDisconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
