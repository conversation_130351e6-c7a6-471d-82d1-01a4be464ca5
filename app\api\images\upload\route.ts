import { NextRequest, NextResponse } from 'next/server';
import { uploadImage } from '@/lib/mongodb/storage';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bucketName = formData.get('bucketName') as string || 'properties';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const fileName = `property-${Date.now()}-${file.name}`;

    const result = await uploadImage(
      bucketName,
      buffer,
      fileName,
      file.type
    );

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to upload image' },
      { status: 500 }
    );
  }
} 