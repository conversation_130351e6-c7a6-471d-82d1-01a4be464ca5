import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const { reason } = await request.json();

    if (!reason) {
      return NextResponse.json({ error: "Reason is required" }, { status: 400 });
    }

    // Get the session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is an admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email as string },
    });

    if (user?.role !== 'admin') {
      return NextResponse.json({ error: "Unauthorized - Admin access required" }, { status: 403 });
    }

    console.log(`Admin ${user.email} is declining property ${propertyId} with reason: ${reason}`);

    // Update property status using Prisma
    const property = await prisma.property.update({
      where: { id: propertyId },
      data: {
        status: 'declined',
        updatedAt: new Date(),
        declineReason: reason
      }
    });

    return NextResponse.json(property);
  } catch (error) {
    console.error("Error declining property:", error);
    return NextResponse.json({ error: "Failed to decline property" }, { status: 500 });
  }
}