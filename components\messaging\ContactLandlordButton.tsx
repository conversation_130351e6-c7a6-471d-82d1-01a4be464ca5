'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

type ContactLandlordButtonProps = {
  landlordId: string;
  propertyId: string;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
};

const ContactLandlordButton = ({
  landlordId,
  propertyId,
  variant = 'default',
  className = ''
}: ContactLandlordButtonProps) => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (status === 'unauthenticated') {
      // Redirect to login if user is not authenticated
      router.push(`/login?callbackUrl=/properties/${propertyId}`);
      return;
    }

    try {
      setIsLoading(true);
      
      // Create or find existing conversation with the landlord
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiverId: landlordId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      const conversation = await response.json();
      
      // Redirect to the messages page with this conversation
      router.push(`/messages?conversationId=${conversation.id}`);
    } catch (error) {
      console.error('Error starting conversation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleClick}
      variant={variant}
      className={className}
      disabled={isLoading}
    >
      <MessageCircle className="mr-2 h-4 w-4" />
      {isLoading ? 'Loading...' : 'Message Landlord'}
    </Button>
  );
};

export default ContactLandlordButton; 