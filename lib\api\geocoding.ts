"use server"

// Server-side geocoding service
export async function geocodeAddress(address: string) {
  try {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY
    if (!apiKey) {
      throw new Error("Google Maps API key not available")
    }

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`,
    )

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== "OK") {
      throw new Error(`Geocoding failed: ${data.status}`)
    }

    const { lat, lng } = data.results[0].geometry.location
    return { lat, lng, success: true }
  } catch (error) {
    console.error("Geocoding error:", error)
    return { success: false, error: "Failed to geocode address" }
  }
}
