# Rent Central Frontend

A modern property rental platform built with Next.js, MongoDB, and NextAuth.js.

## Local Development

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a `.env.local` file with the required environment variables (see `.env.local.example`)
4. Run the development server:
   ```bash
   npm run dev
   ```

## Deployment to Vercel

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Sign up for a [Vercel account](https://vercel.com/signup)
3. Import your repository in the Vercel dashboard
4. Configure the following environment variables in your Vercel project settings:
   - `MONGODB_URI`: Your MongoDB connection string
   - `NEXTAUTH_SECRET`: A secure random string for NextAuth.js
   - `NEXTAUTH_URL`: Your production URL (e.g., https://your-app.vercel.app)
5. Deploy!

## Environment Variables

- `MONGODB_URI`: MongoDB connection string
- `NEXTAUTH_SECRET`: Secret key for NextAuth.js
- `NEXTAUTH_URL`: URL of your application

## Features

- User authentication with NextAuth.js
- Property listing and management
- Search functionality
- Responsive design 