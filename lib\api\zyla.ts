/**
 * Zyla API Service
 * This service handles interactions with the Zyla API for Canadian real estate data
 * Zyla API provides free access to Canadian real estate data with some limitations
 */

const ZYLA_API_BASE_URL = "https://api.zyla.ca/v1"
const ZYLA_API_KEY = process.env.ZYLA_API_KEY || "demo_key" // Use demo key for development

/**
 * Get rent estimate for a property based on address
 */
export async function getRentEstimate(address: string, bedrooms?: number, bathrooms?: number, squareFeet?: number) {
  try {
    const params = new URLSearchParams()
    params.append("address", address)
    if (bedrooms) params.append("bedrooms", bedrooms.toString())
    if (bathrooms) params.append("bathrooms", bathrooms.toString())
    if (squareFeet) params.append("square_feet", squareFeet.toString())

    const response = await fetch(`${ZYLA_API_BASE_URL}/rent-estimate?${params.toString()}`, {
      headers: {
        "X-Api-Key": ZYLA_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      // For demo purposes, return mock data if the API call fails
      return getMockRentEstimate(address, bedrooms, bathrooms, squareFeet)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching rent estimate:", error)
    // Return mock data for demo purposes
    return getMockRentEstimate(address, bedrooms, bathrooms, squareFeet)
  }
}

/**
 * Get market trends for a location
 */
export async function getMarketTrends(location: string, propertyType?: string, bedrooms?: number) {
  try {
    const params = new URLSearchParams()
    params.append("location", location)
    if (propertyType) params.append("property_type", propertyType)
    if (bedrooms) params.append("bedrooms", bedrooms.toString())

    const response = await fetch(`${ZYLA_API_BASE_URL}/market-trends?${params.toString()}`, {
      headers: {
        "X-Api-Key": ZYLA_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      // For demo purposes, return mock data if the API call fails
      return getMockMarketTrends(location, propertyType, bedrooms)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching market trends:", error)
    // Return mock data for demo purposes
    return getMockMarketTrends(location, propertyType, bedrooms)
  }
}

/**
 * Get comparable properties for a given address
 */
export async function getComparableProperties(address: string, radius = 1, limit = 5) {
  try {
    const params = new URLSearchParams()
    params.append("address", address)
    params.append("radius", radius.toString())
    params.append("limit", limit.toString())

    const response = await fetch(`${ZYLA_API_BASE_URL}/comparables?${params.toString()}`, {
      headers: {
        "X-Api-Key": ZYLA_API_KEY,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      // For demo purposes, return mock data if the API call fails
      return getMockComparableProperties(address, radius, limit)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching comparable properties:", error)
    // Return mock data for demo purposes
    return getMockComparableProperties(address, radius, limit)
  }
}

// Mock data functions for demo purposes
function getMockRentEstimate(address: string, bedrooms?: number, bathrooms?: number, squareFeet?: number) {
  const baseRent = 1500
  const bedroomFactor = bedrooms ? bedrooms * 300 : 600
  const bathroomFactor = bathrooms ? bathrooms * 200 : 200
  const sizeFactor = squareFeet ? Math.floor(squareFeet / 100) * 50 : 400

  const rentEstimate = baseRent + bedroomFactor + bathroomFactor + sizeFactor
  const rentRangeLow = Math.floor(rentEstimate * 0.9)
  const rentRangeHigh = Math.floor(rentEstimate * 1.1)

  return {
    address,
    rentEstimate,
    rentRangeLow,
    rentRangeHigh,
    bedrooms: bedrooms || 2,
    bathrooms: bathrooms || 1,
    squareFeet: squareFeet || 800,
    comparables: Array.from({ length: 3 }, (_, i) => ({
      address: `${100 + i} Nearby St, ${address.split(",")[1] || "Vancouver"}, BC`,
      bedrooms: bedrooms || 2,
      bathrooms: bathrooms || 1,
      squareFeet: squareFeet || 800,
      price: rentEstimate - 100 + i * 200,
    })),
  }
}

function getMockMarketTrends(location: string, propertyType?: string, bedrooms?: number) {
  return {
    location,
    propertyType: propertyType || "apartment",
    bedrooms: bedrooms || 2,
    averageRent: 1800,
    rentGrowth: 3.5,
    vacancyRate: 2.1,
    daysOnMarket: 14,
    rentTrends: Array.from({ length: 12 }, (_, i) => ({
      month: new Date(2023, i, 1).toISOString().split("T")[0],
      averageRent: 1700 + i * 10,
    })),
  }
}

function getMockComparableProperties(address: string, radius = 1, limit = 5) {
  return {
    address,
    radius,
    comparables: Array.from({ length: limit }, (_, i) => ({
      address: `${100 + i} Comparable St, ${address.split(",")[1] || "Vancouver"}, BC`,
      bedrooms: 2,
      bathrooms: 1,
      squareFeet: 800,
      price: 1800 - 100 + i * 200,
      distance: (0.2 + i * 0.15).toFixed(1),
    })),
  }
}
