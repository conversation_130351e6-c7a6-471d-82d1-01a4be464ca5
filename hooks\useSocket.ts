import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export type MessageStatus = 'sent' | 'delivered' | 'read';

export type MessageContentType = 'text' | 'image' | 'property' | 'location';

export type MessageContent = {
  type: MessageContentType;
  text?: string;
  imageUrl?: string;
  propertyId?: string;
  propertyTitle?: string;
  propertyImage?: string;
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
};

export type Message = {
  id: string;
  content: MessageContent;
  conversationId: string;
  senderId: string;
  receiverId: string;
  read: boolean;
  createdAt: Date;
  status?: MessageStatus;
  statusTimestamp?: Date;
  sender: {
    id: string;
    name: string;
    image: string;
  };
  receiver: {
    id: string;
    name: string;
    image: string;
  };
};

const useSocket = (conversationId?: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    // Create socket connection
    const socketInstance = io('http://localhost:3001', {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 2000,
    });

    socketInstance.on('connect', () => {
      console.log('Socket connected:', socketInstance.id);
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('Socket disconnected');
      setIsConnected(false);
    });

    setSocket(socketInstance);

    // Cleanup on unmount
    return () => {
      console.log('Disconnecting socket');
      socketInstance.disconnect();
    };
  }, []);

  // Join conversation room when conversationId changes
  useEffect(() => {
    if (!socket || !conversationId) return;

    console.log('Joining conversation:', conversationId);
    socket.emit('join_conversation', conversationId);

    // Listen for incoming messages
    const handleReceiveMessage = (data: Message) => {
      console.log('Received message:', data);
      setMessages((prev) => [...prev, data]);
    };

    // Listen for message status updates
    const handleMessageReceived = (data: { messageId: string; status: MessageStatus; timestamp: Date }) => {
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === data.messageId 
            ? { ...msg, status: data.status, statusTimestamp: data.timestamp } 
            : msg
        )
      );
    };

    const handleMessageRead = (data: { messageId: string; status: MessageStatus; timestamp: Date }) => {
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === data.messageId 
            ? { ...msg, status: data.status, statusTimestamp: data.timestamp, read: true } 
            : msg
        )
      );
    };

    socket.on('receive_message', handleReceiveMessage);
    socket.on('message_received', handleMessageReceived);
    socket.on('message_read', handleMessageRead);

    // Leave the conversation and clean up when changing conversations
    return () => {
      console.log('Leaving conversation:', conversationId);
      socket.emit('leave_conversation', conversationId);
      socket.off('receive_message', handleReceiveMessage);
      socket.off('message_received', handleMessageReceived);
      socket.off('message_read', handleMessageRead);
    };
  }, [socket, conversationId]);

  // Function to send a text message
  const sendTextMessage = (text: string, receiverId: string) => {
    if (!socket || !conversationId) return;

    const messageData = {
      content: {
        type: 'text' as MessageContentType,
        text
      },
      conversationId,
      receiverId,
      timestamp: new Date(),
    };

    socket.emit('send_message', messageData);
  };

  // Function to send an image message
  const sendImageMessage = (imageUrl: string, receiverId: string) => {
    if (!socket || !conversationId) return;

    const messageData = {
      content: {
        type: 'image' as MessageContentType,
        imageUrl
      },
      conversationId,
      receiverId,
      timestamp: new Date(),
    };

    socket.emit('send_message', messageData);
  };

  // Function to send a property link
  const sendPropertyMessage = (propertyId: string, propertyTitle: string, propertyImage: string, receiverId: string) => {
    if (!socket || !conversationId) return;

    const messageData = {
      content: {
        type: 'property' as MessageContentType,
        propertyId,
        propertyTitle,
        propertyImage
      },
      conversationId,
      receiverId,
      timestamp: new Date(),
    };

    socket.emit('send_message', messageData);
  };

  // Function to send a location
  const sendLocationMessage = (lat: number, lng: number, address: string, receiverId: string) => {
    if (!socket || !conversationId) return;

    const messageData = {
      content: {
        type: 'location' as MessageContentType,
        location: {
          lat,
          lng,
          address
        }
      },
      conversationId,
      receiverId,
      timestamp: new Date(),
    };

    socket.emit('send_message', messageData);
  };

  // Function to mark messages as read
  const markAsRead = (messageId: string) => {
    if (!socket || !conversationId) return;

    socket.emit('mark_as_read', {
      messageId,
      conversationId,
    });
  };

  return {
    socket,
    isConnected,
    messages,
    sendTextMessage,
    sendImageMessage,
    sendPropertyMessage,
    sendLocationMessage,
    markAsRead,
  };
};

export default useSocket; 