"use client"

import { useCurrency } from "@/components/currency-provider"
import { Property } from "@/types/property"

interface PropertyDetailProps {
  property: Property
}

export function PropertyDetail({ property }: PropertyDetailProps) {
  const { formatAmount } = useCurrency()

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold">{property.title}</h2>
        <div className="text-2xl font-bold text-primary">
          {formatAmount(property.price)}
        </div>
      </div>
    </div>
  )
} 