"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle 
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileClock, FileCheck, FileX, Eye } from "lucide-react"
import Link from "next/link"

// Sample application data
const applications = [
  {
    id: "app-1",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    location: "Vancouver, BC",
    status: "pending",
    date: "2023-04-10",
    landlord: "Jane Smith",
  },
  {
    id: "app-2",
    propertyId: "prop-2",
    propertyName: "Cozy Studio Loft",
    location: "Montreal, QC",
    status: "approved",
    date: "2023-03-28",
    landlord: "<PERSON>",
  },
  {
    id: "app-3",
    propertyId: "prop-3",
    propertyName: "Spacious Family Home",
    location: "Toronto, ON",
    status: "rejected",
    date: "2023-03-15",
    landlord: "<PERSON>",
  }
];

export default function ApplicationsPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [applicationData, setApplicationData] = useState(applications)
  
  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
    
    // In a real application, we would fetch the data here
    // Example: fetchApplications().then(data => setApplicationData(data))
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <FileClock className="h-4 w-4 text-amber-500" />
      case "approved":
        return <FileCheck className="h-4 w-4 text-emerald-500" />
      case "rejected":
        return <FileX className="h-4 w-4 text-rose-500" />
      default:
        return null
    }
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">Pending</Badge>
      case "approved":
        return <Badge variant="outline" className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200">Approved</Badge>
      case "rejected":
        return <Badge variant="outline" className="bg-rose-50 text-rose-700 hover:bg-rose-50 border-rose-200">Rejected</Badge>
      default:
        return null
    }
  }

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          Your Applications
        </h1>
        <p className="text-muted-foreground">
          Track the status of your rental applications
        </p>
      </div>
      <Card className="border-0 property-card-shadow">
        <CardHeader>
          <CardTitle>Rental Applications</CardTitle>
          <CardDescription>
            View and manage all your property applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          {applicationData.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Landlord</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {applicationData.map((application) => (
                  <TableRow key={application.id}>
                    <TableCell className="font-medium">{application.propertyName}</TableCell>
                    <TableCell>{application.location}</TableCell>
                    <TableCell>{application.landlord}</TableCell>
                    <TableCell>{new Date(application.date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(application.status)}
                        {getStatusBadge(application.status)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" className="h-8 gap-1" asChild>
                        <Link href={`/dashboard/applications/${application.id}`} legacyBehavior>
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="py-12 text-center">
              <p className="text-muted-foreground mb-4">No applications found</p>
              <Link href="/properties" legacyBehavior>
                <Button>Browse Properties</Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 