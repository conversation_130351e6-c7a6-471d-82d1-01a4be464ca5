'use client';

import { useState } from "react";
import { X, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";

// Sample messages for UI demonstration with static timestamps
// Using fixed dates to avoid hydration mismatches
const initialMessages = [
  {
    id: '1',
    content: 'Hi there! How can I help you with your rental search today?',
    sender: 'agent',
    timestamp: '2023-06-15T14:00:00Z',
  },
  {
    id: '2',
    content: 'I\'m looking for a 2-bedroom apartment in the downtown area.',
    sender: 'user',
    timestamp: '2023-06-15T14:30:00Z',
  },
  {
    id: '3',
    content: 'Great! I can help with that. What\'s your budget range?',
    sender: 'agent',
    timestamp: '2023-06-15T14:35:00Z',
  },
  {
    id: '4',
    content: 'I\'m looking to spend around $1,500-2,000 per month.',
    sender: 'user',
    timestamp: '2023-06-15T14:40:00Z',
  },
  {
    id: '5',
    content: 'Perfect. And when are you looking to move in?',
    sender: 'agent',
    timestamp: '2023-06-15T14:45:00Z',
  },
  {
    id: '6',
    content: 'Within the next 2-3 months.',
    sender: 'user',
    timestamp: '2023-06-15T14:50:00Z',
  },
  {
    id: '7',
    content: 'I\'ve found a few properties that might match your criteria. I\'ll send you the listings shortly. Is there anything specific you\'re looking for in terms of amenities?',
    sender: 'agent',
    timestamp: '2023-06-15T14:55:00Z',
  }
];

interface ChatPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ChatPanel({ isOpen, onClose }: ChatPanelProps) {
  const [messages, setMessages] = useState(initialMessages);
  const [newMessage, setNewMessage] = useState("");

  // Format message time in a consistent way for both server and client
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);

    // Using a more stable approach for time formatting
    const hours = date.getUTCHours();
    const minutes = date.getUTCMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    return `${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    // Generate a unique ID based on the number of existing messages plus a random suffix
    const generateId = () => `msg-${messages.length + 1}-${Math.floor(Math.random() * 1000)}`;

    const userMessage = {
      id: generateId(),
      content: newMessage,
      sender: 'user',
      timestamp: new Date().toISOString(),
    };

    setMessages([...messages, userMessage]);
    setNewMessage("");

    // Simulate agent response after a short delay
    setTimeout(() => {
      const agentMessage = {
        id: generateId(),
        content: "Thank you for your message. A rental agent will get back to you shortly.",
        sender: 'agent',
        timestamp: new Date().toISOString(),
      };
      setMessages(prev => [...prev, agentMessage]);
    }, 1000);
  };

  return (
    <div className={`fixed inset-y-0 right-0 z-50 flex flex-col w-80 md:w-96 bg-card shadow-lg border-l border-border transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}>
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/agents/agent-1.jpg" alt="Support Agent" />
            <AvatarFallback className="bg-primary/10 text-primary">SC</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold">Support Chat</h3>
            <p className="text-xs text-muted-foreground">Online</p>
          </div>
        </div>
        <Button onClick={onClose} size="icon" variant="ghost" className="h-8 w-8">
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Message Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] rounded-lg p-3 ${
                message.sender === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-foreground'
              }`}>
                <p className="text-sm">{message.content}</p>
                <p className="text-xs mt-1 opacity-70">
                  {formatMessageTime(message.timestamp)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <form onSubmit={handleSendMessage} className="border-t border-border p-4">
        <div className="flex gap-2">
          <Input
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={!newMessage.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}