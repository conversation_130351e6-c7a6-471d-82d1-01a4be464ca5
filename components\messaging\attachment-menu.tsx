import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Image, Home, MapPin } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';

interface AttachmentMenuProps {
  onImageSelect: (file: File) => void;
  onPropertySelect: (propertyId: string) => void;
  onLocationSelect: (location: { lat: number; lng: number; address: string }) => void;
}

export function AttachmentMenu({
  onImageSelect,
  onPropertySelect,
  onLocationSelect,
}: AttachmentMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        onImageSelect(file);
      } else {
        toast({
          title: 'Invalid file type',
          description: 'Please select an image file',
          variant: 'destructive',
        });
      }
    }
  };

  const handlePropertySubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const propertyId = formData.get('propertyId') as string;
    if (propertyId) {
      onPropertySelect(propertyId);
    }
  };

  const handleLocationSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const lat = parseFloat(formData.get('lat') as string);
    const lng = parseFloat(formData.get('lng') as string);
    const address = formData.get('address') as string;
    
    if (lat && lng && address) {
      onLocationSelect({ lat, lng, address });
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon">
          <Image className="h-5 w-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Attach</h4>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                className="flex flex-col items-center justify-center h-20"
                onClick={handleImageClick}
              >
                <Image className="h-6 w-6 mb-1" />
                <span className="text-xs">Image</span>
              </Button>
              <Button
                variant="outline"
                className="flex flex-col items-center justify-center h-20"
                onClick={() => setIsOpen(true)}
              >
                <Home className="h-6 w-6 mb-1" />
                <span className="text-xs">Property</span>
              </Button>
              <Button
                variant="outline"
                className="flex flex-col items-center justify-center h-20"
                onClick={() => setIsOpen(true)}
              >
                <MapPin className="h-6 w-6 mb-1" />
                <span className="text-xs">Location</span>
              </Button>
            </div>
          </div>

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
          />

          <form onSubmit={handlePropertySubmit} className="space-y-2">
            <Label htmlFor="propertyId">Property ID</Label>
            <Input
              id="propertyId"
              name="propertyId"
              placeholder="Enter property ID"
              required
            />
            <Button type="submit" className="w-full">
              Attach Property
            </Button>
          </form>

          <form onSubmit={handleLocationSubmit} className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="lat">Latitude</Label>
                <Input
                  id="lat"
                  name="lat"
                  type="number"
                  step="any"
                  placeholder="Latitude"
                  required
                />
              </div>
              <div>
                <Label htmlFor="lng">Longitude</Label>
                <Input
                  id="lng"
                  name="lng"
                  type="number"
                  step="any"
                  placeholder="Longitude"
                  required
                />
              </div>
            </div>
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                placeholder="Enter address"
                required
              />
            </div>
            <Button type="submit" className="w-full">
              Attach Location
            </Button>
          </form>
        </div>
      </PopoverContent>
    </Popover>
  );
} 