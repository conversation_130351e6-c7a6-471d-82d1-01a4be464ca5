"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { cn, formatCurrency } from "@/lib/utils"

interface PropertyFiltersProps {
  className?: string
}

export function PropertyFilters({ className }: PropertyFiltersProps) {
  const [priceRange, setPriceRange] = useState([500, 5000])
  const [duration, setDuration] = useState(12)

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="font-medium">Price Range</h3>
          <div className="space-y-2">
            <Slider value={priceRange} min={0} max={10000} step={100} onValueChange={setPriceRange} />
            <div className="flex items-center gap-2">
              <div className="rounded-md border px-2 py-1 text-sm">{formatCurrency(priceRange[0])}</div>
              <div className="rounded-md border px-2 py-1 text-sm">{formatCurrency(priceRange[1])}</div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Rental Duration</h3>
          <div className="grid grid-cols-3 gap-2">
            {[3, 6, 12].map((months) => (
              <Button
                key={months}
                variant={duration === months ? "default" : "outline"}
                className="w-full"
                onClick={() => setDuration(months)}
              >
                {months} {months === 1 ? "month" : "months"}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Property Type</h3>
          <div className="space-y-2">
            {["Apartment", "House", "Townhouse", "Condo", "Studio"].map((type) => (
              <div key={type} className="flex items-center space-x-2">
                <Checkbox id={`type-${type}`} />
                <Label htmlFor={`type-${type}`}>{type}</Label>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Bedrooms</h3>
          <div className="grid grid-cols-3 gap-2">
            {["Any", "1+", "2+", "3+", "4+", "5+"].map((bedrooms) => (
              <Button key={bedrooms} variant="outline" className="w-full">
                {bedrooms}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Bathrooms</h3>
          <div className="grid grid-cols-3 gap-2">
            {["Any", "1+", "2+", "3+", "4+"].map((bathrooms) => (
              <Button key={bathrooms} variant="outline" className="w-full">
                {bathrooms}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Amenities</h3>
          <div className="grid grid-cols-1 gap-2">
            {[
              "In-unit Laundry",
              "Dishwasher",
              "Air Conditioning",
              "Balcony",
              "Parking",
              "Pet Friendly",
              "Gym",
              "Pool",
            ].map((amenity) => (
              <div key={amenity} className="flex items-center space-x-2">
                <Checkbox id={`amenity-${amenity}`} />
                <Label htmlFor={`amenity-${amenity}`}>{amenity}</Label>
              </div>
            ))}
          </div>
        </div>

        <Button className="w-full">Apply Filters</Button>
      </CardContent>
    </Card>
  )
}
