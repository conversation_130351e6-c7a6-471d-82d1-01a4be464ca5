import type { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda"
import { SecretsManager, S3 } from "aws-sdk"
import { Pool } from "pg"
import { v4 as uuidv4 } from "uuid"
import {
  getProperties,
  getPropertyById,
  createProperty,
  updateProperty,
  deleteProperty,
  getLandlordProperties,
} from "./property-handlers"

// Initialize AWS services
const secretsManager = new SecretsManager()
const s3 = new S3()

// Database connection pool
let pool: Pool | null = null

// Initialize database connection
async function initializeDbConnection() {
  if (pool) return pool

  try {
    // Get database credentials from Secrets Manager
    const secretData = await secretsManager
      .getSecretValue({
        SecretId: process.env.DB_SECRET_ARN!,
      })
      .promise()

    const dbCredentials = JSON.parse(secretData.SecretString!)

    // Create connection pool
    pool = new Pool({
      host: dbCredentials.host,
      port: dbCredentials.port,
      database: dbCredentials.dbname,
      user: dbCredentials.username,
      password: dbCredentials.password,
      ssl: {
        rejectUnauthorized: false,
      },
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })

    return pool
  } catch (error) {
    console.error("Error initializing database connection:", error)
    throw error
  }
}

// Helper function to generate S3 presigned URL
async function generatePresignedUrl(key: string, contentType: string): Promise<string> {
  const params = {
    Bucket: process.env.PROPERTY_IMAGES_BUCKET!,
    Key: key,
    ContentType: contentType,
    Expires: 3600, // URL expires in 1 hour
  }

  return s3.getSignedUrlPromise("putObject", params)
}

// Helper function to extract user ID from event
function getUserIdFromEvent(event: APIGatewayProxyEvent): string | null {
  if (!event.requestContext.authorizer) return null

  const claims = event.requestContext.authorizer.claims
  return claims?.sub || null
}

// Main handler function
export async function handler(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
  // Initialize database connection
  try {
    await initializeDbConnection()
  } catch (error) {
    console.error("Failed to initialize database connection:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Internal server error" }),
    }
  }

  // Parse path and method
  const path = event.path
  const method = event.httpMethod

  try {
    // Route the request to the appropriate handler
    if (path.match(/^\/api\/properties\/?$/)) {
      if (method === "GET") {
        return await getProperties(event, pool!)
      } else if (method === "POST") {
        const userId = getUserIdFromEvent(event)
        if (!userId) {
          return {
            statusCode: 401,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            body: JSON.stringify({ error: "Unauthorized" }),
          }
        }
        return await createProperty(event, pool!, userId)
      }
    } else if (path.match(/^\/api\/properties\/landlord\/?$/)) {
      if (method === "GET") {
        const userId = getUserIdFromEvent(event)
        if (!userId) {
          return {
            statusCode: 401,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            body: JSON.stringify({ error: "Unauthorized" }),
          }
        }
        return await getLandlordProperties(event, pool!, userId)
      }
    } else if (path.match(/^\/api\/properties\/[a-zA-Z0-9-]+\/?$/)) {
      const propertyId = path.split("/").pop()
      if (method === "GET") {
        return await getPropertyById(propertyId!, event, pool!)
      } else if (method === "PUT") {
        const userId = getUserIdFromEvent(event)
        if (!userId) {
          return {
            statusCode: 401,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            body: JSON.stringify({ error: "Unauthorized" }),
          }
        }
        return await updateProperty(propertyId!, event, pool!, userId)
      } else if (method === "DELETE") {
        const userId = getUserIdFromEvent(event)
        if (!userId) {
          return {
            statusCode: 401,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            body: JSON.stringify({ error: "Unauthorized" }),
          }
        }
        return await deleteProperty(propertyId!, event, pool!, userId)
      }
    } else if (path.match(/^\/api\/upload\/?$/)) {
      if (method === "POST") {
        return await getUploadUrl(event)
      }
    }

    // If no route matches
    return {
      statusCode: 404,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Not found" }),
    }
  } catch (error) {
    console.error("Error processing request:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Internal server error" }),
    }
  }
}

// Handler for POST /api/upload
async function getUploadUrl(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  const userId = getUserIdFromEvent(event)

  if (!userId) {
    return {
      statusCode: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Unauthorized" }),
    }
  }

  // Parse request body
  const requestData = JSON.parse(event.body || "{}")

  if (!requestData.contentType || !requestData.fileName) {
    return {
      statusCode: 400,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Missing contentType or fileName" }),
    }
  }

  try {
    // Generate a unique key for the file
    const fileExtension = requestData.fileName.split(".").pop()
    const key = `${userId}/${uuidv4()}.${fileExtension}`

    // Generate presigned URL
    const presignedUrl = await generatePresignedUrl(key, requestData.contentType)

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({
        uploadUrl: presignedUrl,
        key: key,
        url: `https://${process.env.PROPERTY_IMAGES_BUCKET}.s3.amazonaws.com/${key}`,
      }),
    }
  } catch (error) {
    console.error("Error generating upload URL:", error)
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: "Failed to generate upload URL" }),
    }
  }
}
