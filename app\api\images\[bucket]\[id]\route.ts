import { NextRequest, NextResponse } from 'next/server';
import { getImage } from '@/lib/mongodb/storage';
import { Readable } from 'stream';

export async function GET(
  request: NextRequest,
  { params }: { params: { bucket: string; id: string } }
) {
  try {
    const { bucket, id } = params;
    
    // Get the image from GridFS
    const { stream, contentType } = await getImage(bucket, id);
    
    // Convert stream to buffer
    const chunks: Buffer[] = [];
    const readableStream = stream as unknown as Readable;
    
    for await (const chunk of readableStream) {
      chunks.push(chunk instanceof Buffer ? chunk : Buffer.from(chunk));
    }
    
    const buffer = Buffer.concat(chunks);
    
    // Return the image with appropriate content type
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType || 'application/octet-stream',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('Error serving image:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve image' },
      { status: 500 }
    );
  }
} 