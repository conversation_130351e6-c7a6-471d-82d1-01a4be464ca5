"use client"

import React from "react"
import { createContext, useContext, useEffect, useState } from "react"

// Import our mock Auth implementation instead of real AWS Amplify
import { Auth } from "./amplify"

// Mock Hub events for auth
const mockHub = {
  listen: (channel: string, callback: (data: any) => void) => {
    // Return a cleanup function
    return () => {}
  }
}

type AuthContextType = {
  user: any | null
  isLoading: boolean
  signUp: (email: string, password: string, attributes: any) => Promise<any>
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<void>
  confirmSignUp: (email: string, code: string) => Promise<any>
  forgotPassword: (email: string) => Promise<any>
  resetPassword: (email: string, code: string, newPassword: string) => Promise<any>
}

const AuthContext = createContext<AuthContextType | null>(null)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is already signed in
    checkUser()

    // No need for Hub listener with our mock
  }, [])

  async function checkUser() {
    try {
      // Try to get user from localStorage first
      const storedUser = localStorage.getItem('currentUser')
      if (storedUser) {
        setUser(JSON.parse(storedUser))
      } else {
        // Fall back to our mock Auth
        const userData = await Auth.currentAuthenticatedUser()
        setUser(userData)
      }
    } catch (error) {
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  async function signUp(email: string, password: string, attributes: any) {
    try {
      const result = await Auth.signUp(email, password, attributes)
      return result
    } catch (error) {
      throw error
    }
  }

  async function confirmSignUp(email: string, code: string) {
    try {
      // Mock implementation - just return success
      return { success: true }
    } catch (error) {
      throw error
    }
  }

  async function signIn(email: string, password: string) {
    try {
      const result = await Auth.signIn(email, password)
      const userData = {
        username: email,
        attributes: {
          email,
          sub: 'mock-user-id',
          name: email.split('@')[0]
        },
        // Add any other required user properties
      }
      
      setUser(userData)
      
      // Store in localStorage for persistence
      localStorage.setItem('currentUser', JSON.stringify(userData))
      
      return userData
    } catch (error) {
      throw error
    }
  }

  async function signOut() {
    try {
      await Auth.signOut()
      setUser(null)
      localStorage.removeItem('currentUser')
    } catch (error) {
      throw error
    }
  }

  async function forgotPassword(email: string) {
    try {
      return await Auth.forgotPassword(email)
    } catch (error) {
      throw error
    }
  }

  async function resetPassword(email: string, code: string, newPassword: string) {
    try {
      return await Auth.forgotPasswordSubmit(email, code, newPassword)
    } catch (error) {
      throw error
    }
  }

  // Use non-JSX syntax to create the provider context
  return React.createElement(AuthContext.Provider, {
    value: {
      user,
      isLoading,
      signUp,
      signIn,
      signOut,
      confirmSignUp,
      forgotPassword,
      resetPassword,
    }
  }, children)
}
