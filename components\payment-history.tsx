import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export function PaymentHistory() {
  // Mock data for payments
  const payments = [
    {
      id: "pay-1",
      tenant: "<PERSON>",
      property: "Modern Downtown Apartment",
      amount: 1800,
      date: "2023-04-01",
      status: "completed",
    },
    {
      id: "pay-2",
      tenant: "<PERSON>",
      property: "Spacious Family Home",
      amount: 2200,
      date: "2023-04-01",
      status: "completed",
    },
    {
      id: "pay-3",
      tenant: "<PERSON> Brown",
      property: "Cozy Studio Apartment",
      amount: 1200,
      date: "2023-04-01",
      status: "pending",
    },
    {
      id: "pay-4",
      tenant: "<PERSON> Davis",
      property: "Luxury Penthouse",
      amount: 3500,
      date: "2023-03-01",
      status: "completed",
    },
    {
      id: "pay-5",
      tenant: "<PERSON> Wilson",
      property: "Suburban Townhouse",
      amount: 1950,
      date: "2023-03-01",
      status: "completed",
    },
  ]

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Tenant</TableHead>
            <TableHead>Property</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {payments.map((payment) => (
            <TableRow key={payment.id}>
              <TableCell className="font-medium">{payment.tenant}</TableCell>
              <TableCell>{payment.property}</TableCell>
              <TableCell>${payment.amount}</TableCell>
              <TableCell>{payment.date}</TableCell>
              <TableCell>
                <Badge
                  variant={payment.status === "completed" ? "outline" : "secondary"}
                  className={
                    payment.status === "completed"
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                      : ""
                  }
                >
                  {payment.status === "completed" ? "Paid" : "Pending"}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
