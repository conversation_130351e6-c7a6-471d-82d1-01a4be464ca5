import type React from "react"
import { createServerClient } from "@/lib/supabase/server"
import type { PropertyWithImages } from "@/lib/types"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bed, Bath, Home, MapPin, MoreHorizontal, Eye, Edit, Trash } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default async function UserProperties({ userId }: { userId: string }) {
  const supabase = createServerClient()

  // Fetch user's properties with images
  const { data: properties, error } = await supabase

    .from("properties")
    .select(`
      *,
      images:property_images(*)
    `)
    .eq("landlord_id", userId)
    .order("created_at", { ascending: false })

  const userProperties = (properties as unknown as PropertyWithImages[]) || []

  if (userProperties.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-2">No properties listed yet</h2>
        <p className="text-muted-foreground mb-6">Start listing your properties to find tenants.</p>
        <Link href="/dashboard/properties/new" legacyBehavior>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add New Property
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {userProperties.map((property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </div>
  )
}

function PropertyCard({ property }: { property: PropertyWithImages }) {
  const primaryImage = property.images.find((img) => img.is_primary) || property.images[0]

  // Map status to badge variant
  const statusVariant = {
    pending: "secondary",
    approved: "success",
    rented: "default",
    unavailable: "destructive",
  }[property.status] as "secondary" | "success" | "default" | "destructive"

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative h-48">
        <Image
          src={primaryImage?.image_url || "/placeholder.svg?height=400&width=600&text=No+Image"}
          alt={property.title}
          fill
          className="object-cover"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          <Badge variant={statusVariant} className="capitalize">
            {property.status}
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 bg-white/80 hover:bg-white">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/properties/${property.id}`} legacyBehavior>
                  <Eye className="mr-2 h-4 w-4" />
                  View Property
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/properties/${property.id}/edit`} legacyBehavior>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Property
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                <Trash className="mr-2 h-4 w-4" />
                Delete Property
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <CardHeader>
        <CardTitle className="line-clamp-1">{property.title}</CardTitle>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPin className="h-4 w-4 mr-1" />
          {property.city}, {property.province}
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="grid grid-cols-3 gap-2 text-sm">
          <div className="flex items-center">
            <Bed className="h-4 w-4 mr-1" />
            <span>
              {property.bedrooms} {property.bedrooms === 1 ? "Bed" : "Beds"}
            </span>
          </div>
          <div className="flex items-center">
            <Bath className="h-4 w-4 mr-1" />
            <span>
              {property.bathrooms} {property.bathrooms === 1 ? "Bath" : "Baths"}
            </span>
          </div>
          <div className="flex items-center">
            <Home className="h-4 w-4 mr-1" />
            <span>{property.square_feet} ft²</span>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Price</span>
            <span className="font-semibold">${property.price}/month</span>
          </div>
          <div className="flex justify-between items-center mt-1">
            <span className="text-sm text-muted-foreground">Available From</span>
            <span>{new Date(property.available_from).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="w-full flex justify-between items-center">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/properties/${property.id}/applications`}>
              View Applications
            </Link>
          </Button>
          <Button size="sm" asChild>
            <Link href={`/dashboard/properties/${property.id}/edit`}>
              Edit
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

function Plus(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M5 12h14" />
      <path d="M12 5v14" />
    </svg>
  )
}
