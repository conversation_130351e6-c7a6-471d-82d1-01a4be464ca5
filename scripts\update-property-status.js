// Direct MongoDB update script
db = db.getSiblingDB('rentcentral');
print('Updating property status...');
const result = db.properties.updateMany(
  { status: 'pending' },
  { $set: { status: 'approved' } }
);
print(`Updated ${result.modifiedCount} properties to 'approved' status`);

// Count properties by status
const statusCounts = db.properties.aggregate([
  { $group: { _id: "$status", count: { $sum: 1 } } },
  { $sort: { count: -1 } }
]).toArray();

print('\nProperties by status:');
statusCounts.forEach(status => {
  print(`- ${status._id}: ${status.count}`);
});
