'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { MessageCircle, MoreVertical } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MessageContent } from './message-content';
import { MessageStatusIndicator } from './message-status-indicator';
import { MessageReactions } from './message-reactions';

export type MessageContentType = 'text' | 'image' | 'property' | 'location';

export interface MessageContent {
  type: MessageContentType;
  text?: string;
  imageUrl?: string;
  propertyId?: string;
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
}

export interface MessageProps {
  id: string;
  content: MessageContent;
  senderId: string;
  senderName: string;
  senderImage?: string;
  createdAt: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
  isCurrentUser: boolean;
  reactions: {
    id: string;
    emoji: string;
    count: number;
    users: {
      id: string;
      name: string;
    }[];
  }[];
  replyCount?: number;
  onReply: (messageId: string) => void;
  onAddReaction: (messageId: string, emoji: string) => Promise<void>;
  onRemoveReaction: (messageId: string, emoji: string) => Promise<void>;
}

export function Message({
  id,
  content,
  senderId,
  senderName,
  senderImage,
  createdAt,
  status,
  isCurrentUser,
  reactions,
  replyCount = 0,
  onReply,
  onAddReaction,
  onRemoveReaction,
}: MessageProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Use a more stable approach for time formatting
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    return `${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  const formattedTime = formatTime(createdAt);

  return (
    <div
      className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {!isCurrentUser && (
        <Avatar className="h-8 w-8 mr-2 mt-1">
          <AvatarImage src={senderImage} alt={senderName} />
          <AvatarFallback>{senderName.charAt(0)}</AvatarFallback>
        </Avatar>
      )}

      <div className={`max-w-[70%] ${isCurrentUser ? 'mr-2' : 'ml-2'}`}>
        {!isCurrentUser && (
          <div className="text-sm font-medium mb-1">{senderName}</div>
        )}

        <div
          className={`
            rounded-lg p-3
            ${isCurrentUser
              ? 'bg-primary text-primary-foreground'
              : 'bg-muted'
            }
          `}
        >
          <MessageContent content={content} />

          <div className="flex items-center justify-between mt-2 text-xs">
            <span className={isCurrentUser ? 'text-primary-foreground/70' : 'text-muted-foreground'}>
              {formattedTime}
            </span>

            {isCurrentUser && (
              <MessageStatusIndicator status={status} />
            )}
          </div>
        </div>

        <MessageReactions
          messageId={id}
          reactions={reactions}
          onAddReaction={onAddReaction}
          onRemoveReaction={onRemoveReaction}
        />

        {replyCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="mt-1 text-xs text-muted-foreground hover:text-foreground"
            onClick={() => onReply(id)}
          >
            <MessageCircle className="h-3 w-3 mr-1" />
            {replyCount} {replyCount === 1 ? 'reply' : 'replies'}
          </Button>
        )}

        {isHovered && (
          <div className="flex justify-end mt-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onReply(id)}>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Reply
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {isCurrentUser && (
        <Avatar className="h-8 w-8 ml-2 mt-1">
          <AvatarImage src={senderImage} alt={senderName} />
          <AvatarFallback>{senderName.charAt(0)}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}