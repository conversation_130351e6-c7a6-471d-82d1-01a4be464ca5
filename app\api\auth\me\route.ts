import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'firebase-admin';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin if it hasn't been initialized already
if (!getApps().length) {
  try {
    const serviceAccount = JSON.parse(
      process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}'
    );
    
    initializeApp({
      credential: cert(serviceAccount),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
  }
}

export async function GET(req: NextRequest) {
  try {
    // Get the session cookie directly from the request
    const sessionCookie = req.cookies.get('session')?.value;
    
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // Verify the session cookie
    const decodedClaims = await auth().verifySessionCookie(sessionCookie, true);
    
    // Get the user from Firebase
    const user = await auth().getUser(decodedClaims.uid);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Return user data
    return NextResponse.json({
      id: user.uid,
      name: user.displayName,
      email: user.email,
      image: user.photoURL,
    });
  } catch (error) {
    console.error('Error getting user:', error);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }
} 