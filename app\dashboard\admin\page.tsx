'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/auth-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, Check, X, AlertTriangle, Clock } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Define the extended session type with role
interface ExtendedSession {
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  };
}

interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  status: 'pending' | 'approved' | 'declined' | 'appealed';
  images: string[];
  createdAt: string;
  userId: string;
  user: {
    name: string;
    email: string;
  };
}

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    } else if (!loading && user && user.email !== '<EMAIL>') {
      router.push('/dashboard');
    }
  }, [loading, user, router]);

  useEffect(() => {
    const fetchProperties = async () => {
      try {
        // Fetch all properties
        const response = await fetch('/api/properties');
        if (!response.ok) {
          throw new Error('Failed to fetch properties');
        }
        const data = await response.json();
        setProperties(data);
      } catch (error) {
        console.error('Error fetching properties:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (!loading && user && user.email === '<EMAIL>') {
      fetchProperties();
    }
  }, [loading, user]);

  const handleStatusChange = async (propertyId: string, newStatus: 'approved' | 'declined') => {
    try {
      const response = await fetch(`/api/properties/${propertyId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update property status');
      }

      // Update the local state
      setProperties(properties.map(property =>
        property.id === propertyId
          ? { ...property, status: newStatus }
          : property
      ));
    } catch (error) {
      console.error('Error updating property status:', error);
    }
  };

  const filteredProperties = properties.filter(property => {
    if (activeTab === 'all') return true;
    return property.status === activeTab;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
      case 'approved':
        return <Badge variant="default" className="flex items-center gap-1 bg-green-500 hover:bg-green-600"><Check className="h-3 w-3" /> Approved</Badge>;
      case 'declined':
        return <Badge variant="destructive" className="flex items-center gap-1"><X className="h-3 w-3" /> Declined</Badge>;
      case 'appealed':
        return <Badge variant="secondary" className="flex items-center gap-1 bg-amber-500 hover:bg-amber-600 text-white"><AlertTriangle className="h-3 w-3" /> Appealed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading || isLoading) {
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-24 w-full mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!loading && user && user.email !== '<EMAIL>') {
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-3xl font-bold mb-6">Access Denied</h1>
        <p>You do not have permission to access the admin dashboard.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <Button asChild>
          <Link href="/dashboard/properties/new" legacyBehavior>
            <Plus className="mr-2 h-4 w-4" />
            Add New Property
          </Link>
        </Button>
      </div>
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">All Properties</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="declined">Declined</TabsTrigger>
          <TabsTrigger value="appealed">Appealed</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {filteredProperties.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No properties found with this status.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProperties.map((property) => (
                <Card key={property.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="line-clamp-1">{property.title}</CardTitle>
                        <CardDescription className="line-clamp-1">{property.location}</CardDescription>
                      </div>
                      {getStatusBadge(property.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="relative aspect-video mb-4 rounded-md overflow-hidden">
                      {property.images && property.images.length > 0 ? (
                        <Image
                          src={property.images[0]}
                          alt={property.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <span className="text-muted-foreground">No image</span>
                        </div>
                      )}
                    </div>
                    <p className="text-lg font-semibold mb-2">${property.price.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground line-clamp-2">{property.description}</p>
                    <div className="mt-2 text-xs text-muted-foreground">
                      <p>Posted by: {property.user.name}</p>
                      <p>Email: {property.user.email}</p>
                      <p>Date: {new Date(property.createdAt).toLocaleDateString()}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" asChild>
                      <Link href={`/dashboard/properties/${property.id}`}>View Details</Link>
                    </Button>
                    {property.status === 'pending' && (
                      <div className="flex gap-2">
                        <Button
                          className="bg-green-500 hover:bg-green-600 text-white"
                          size="sm"
                          onClick={() => handleStatusChange(property.id, 'approved')}
                        >
                          <Check className="h-4 w-4 mr-1" /> Approve
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleStatusChange(property.id, 'declined')}
                        >
                          <X className="h-4 w-4 mr-1" /> Decline
                        </Button>
                      </div>
                    )}
                    {property.status === 'appealed' && (
                      <div className="flex gap-2">
                        <Button
                          className="bg-green-500 hover:bg-green-600 text-white"
                          size="sm"
                          onClick={() => handleStatusChange(property.id, 'approved')}
                        >
                          <Check className="h-4 w-4 mr-1" /> Approve Appeal
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleStatusChange(property.id, 'declined')}
                        >
                          <X className="h-4 w-4 mr-1" /> Reject Appeal
                        </Button>
                      </div>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}