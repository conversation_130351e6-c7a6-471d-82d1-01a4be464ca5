"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useCurrency } from "@/components/currency-provider"

export function CurrencySwitcher() {
  const { currency, setCurrency } = useCurrency()

  return (
    <div className="flex items-center gap-2">
      <Button
        variant={currency === "CAD" ? "default" : "outline"}
        size="sm"
        onClick={() => setCurrency("CAD")}
      >
        CAD
      </Button>
      <Button
        variant={currency === "USD" ? "default" : "outline"}
        size="sm"
        onClick={() => setCurrency("USD")}
      >
        USD
      </Button>
    </div>
  )
} 