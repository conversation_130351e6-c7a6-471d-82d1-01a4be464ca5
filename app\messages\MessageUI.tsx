'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { Send, ArrowLeft, Check, CheckCheck, Clock, Image as ImageIcon, MapPin, Home, Paperclip, X } from 'lucide-react';
import useSocket, { Message, MessageContentType } from '@/hooks/useSocket';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';

type ConversationUser = {
  id: string;
  name: string;
  image: string;
};

type Conversation = {
  id: string;
  userA: ConversationUser;
  userB: ConversationUser;
};

interface MessageUIProps {
  conversationId: string;
}

const MessageUI = ({ conversationId }: MessageUIProps) => {
  const router = useRouter();
  const { data: session } = useSession();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);

  const {
    isConnected,
    sendTextMessage,
    sendImageMessage,
    sendPropertyMessage,
    sendLocationMessage,
    socket
  } = useSocket(conversationId);

  const currentUserId = session?.user?.id;

  // Get conversation details
  useEffect(() => {
    if (!conversationId || !session?.user) return;

    const fetchConversation = async () => {
      try {
        setLoading(true);

        // Fetch conversation details
        const convResponse = await fetch(`/api/conversations/${conversationId}`);
        if (convResponse.ok) {
          const convData = await convResponse.json();
          setConversation(convData);
        }

        // Fetch messages
        const msgResponse = await fetch(`/api/messages?conversationId=${conversationId}`);
        if (msgResponse.ok) {
          const msgData = await msgResponse.json();
          setMessages(msgData);
        }
      } catch (error) {
        console.error('Error fetching conversation data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversation();
  }, [conversationId, session?.user]);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle typing indicators
  useEffect(() => {
    if (!socket || !conversationId) return;

    const handleTypingStart = () => {
      setOtherUserTyping(true);
    };

    const handleTypingStop = () => {
      setOtherUserTyping(false);
    };

    socket.on('typing_start', handleTypingStart);
    socket.on('typing_stop', handleTypingStop);

    return () => {
      socket.off('typing_start', handleTypingStart);
      socket.off('typing_stop', handleTypingStop);
    };
  }, [socket, conversationId]);

  const handleTyping = () => {
    if (!socket || !conversationId) return;

    if (!isTyping) {
      setIsTyping(true);
      socket.emit('typing_start', conversationId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      socket.emit('typing_stop', conversationId);
    }, 1000);
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if ((!newMessage.trim() && !selectedFile) || !conversationId || !currentUserId || !conversation) return;

    try {
      // Determine the recipient
      const recipientId = conversation.userA.id === currentUserId
        ? conversation.userB.id
        : conversation.userA.id;

      if (selectedFile) {
        // Handle image upload
        const formData = new FormData();
        formData.append('file', selectedFile);

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (uploadResponse.ok) {
          const { url } = await uploadResponse.json();

          // Send image message
          const response = await fetch('/api/messages', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: {
                type: 'image',
                imageUrl: url
              },
              conversationId,
              receiverId: recipientId,
            }),
          });

          if (response.ok) {
            const messageData = await response.json();
            setMessages((prev) => [...prev, messageData]);
            setNewMessage('');
            setSelectedFile(null);
            setPreviewUrl(null);

            // Also send via socket
            sendImageMessage(url, recipientId);
          }
        }
      } else {
        // Send text message
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            content: {
              type: 'text',
              text: newMessage
            },
            conversationId,
            receiverId: recipientId,
          }),
        });

        if (response.ok) {
          const messageData = await response.json();
          setMessages((prev) => [...prev, messageData]);
          setNewMessage('');

          // Also send via socket
          sendTextMessage(newMessage, recipientId);
        }
      }

      // Stop typing indicator
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      setIsTyping(false);
      socket?.emit('typing_stop', conversationId);
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        setSelectedFile(file);
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        setShowAttachmentMenu(false);
      } else {
        toast({
          title: "Invalid file type",
          description: "Please select an image file.",
          variant: "destructive",
        });
      }
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  const handleShareProperty = async (propertyId: string) => {
    if (!conversationId || !currentUserId || !conversation) return;

    try {
      // Fetch property details
      const propertyResponse = await fetch(`/api/properties/${propertyId}`);
      if (!propertyResponse.ok) return;

      const property = await propertyResponse.json();

      // Determine the recipient
      const recipientId = conversation.userA.id === currentUserId
        ? conversation.userB.id
        : conversation.userA.id;

      // Send property message
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'property',
            propertyId: property.id,
            propertyTitle: property.title,
            propertyImage: property.images[0] || ''
          },
          conversationId,
          receiverId: recipientId,
        }),
      });

      if (response.ok) {
        const messageData = await response.json();
        setMessages((prev) => [...prev, messageData]);

        // Also send via socket
        sendPropertyMessage(
          property.id,
          property.title,
          property.images[0] || '',
          recipientId
        );
      }
    } catch (error) {
      console.error('Error sharing property:', error);
    }
  };

  const handleShareLocation = async (lat: number, lng: number, address: string) => {
    if (!conversationId || !currentUserId || !conversation) return;

    try {
      // Determine the recipient
      const recipientId = conversation.userA.id === currentUserId
        ? conversation.userB.id
        : conversation.userA.id;

      // Send location message
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            type: 'location',
            location: {
              lat,
              lng,
              address
            }
          },
          conversationId,
          receiverId: recipientId,
        }),
      });

      if (response.ok) {
        const messageData = await response.json();
        setMessages((prev) => [...prev, messageData]);

        // Also send via socket
        sendLocationMessage(lat, lng, address, recipientId);
      }
    } catch (error) {
      console.error('Error sharing location:', error);
    }
  };

  const getOtherUser = () => {
    if (!conversation || !currentUserId) return null;
    return conversation.userA.id === currentUserId ? conversation.userB : conversation.userA;
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    return `${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  const getMessageStatus = (message: Message) => {
    if (!message.read) {
      return <Clock className="h-3 w-3 text-gray-400" />;
    }
    return <CheckCheck className="h-3 w-3 text-blue-500" />;
  };

  const renderMessageContent = (message: Message) => {
    const { content } = message;

    switch (content.type) {
      case 'text':
        return <p className="text-sm">{content.text}</p>;

      case 'image':
        return (
          <div className="relative w-64 h-48 rounded-lg overflow-hidden">
            <Image
              src={content.imageUrl || ''}
              alt="Shared image"
              fill
              className="object-cover"
            />
          </div>
        );

      case 'property':
        return (
          <Link
            href={`/properties/${content.propertyId}`}
            className="block"
            legacyBehavior>
            <div className="flex items-center space-x-3 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
              <div className="relative w-16 h-16 rounded-md overflow-hidden">
                <Image
                  src={content.propertyImage || '/placeholder.svg'}
                  alt={content.propertyTitle || 'Property'}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <p className="text-sm font-medium">{content.propertyTitle}</p>
                <p className="text-xs text-gray-500">View property details</p>
              </div>
            </div>
          </Link>
        );

      case 'location':
        return (
          <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-red-500" />
              <p className="text-sm">{content.location?.address}</p>
            </div>
            <a
              href={`https://www.google.com/maps?q=${content.location?.lat},${content.location?.lng}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-500 mt-1 block"
            >
              View on Google Maps
            </a>
          </div>
        );

      default:
        return <p className="text-sm">{JSON.stringify(content)}</p>;
    }
  };

  const otherUser = getOtherUser();

  return (
    <div className="flex flex-col h-[calc(100vh-64px-3rem)] bg-white dark:bg-gray-900 rounded-lg shadow">
      {/* Header */}
      {conversation && otherUser && (
        <div className="flex items-center p-4 border-b shadow-sm bg-white dark:bg-gray-900">
          <a href="/messages" className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </a>
          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden mr-3">
            {otherUser.image ? (
              <Image
                src={otherUser.image}
                alt={otherUser.name}
                width={40}
                height={40}
                className="object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                {otherUser.name?.charAt(0) || '?'}
              </div>
            )}
          </div>
          <div>
            <h2 className="font-semibold">{otherUser.name}</h2>
            <p className="text-xs text-gray-500">
              {isConnected ? 'Online' : 'Offline'}
            </p>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-4">
            <p className="text-sm text-gray-500">No messages yet.</p>
            <p className="text-xs text-gray-400 mt-1">
              Send your first message to start the conversation.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {messages.map((message) => {
              const isCurrentUser = message.senderId === currentUserId;

              return (
                <div
                  key={message.id}
                  className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg p-3 ${
                      isCurrentUser
                        ? 'bg-blue-500 text-white'
                        : 'bg-white dark:bg-gray-800 border dark:border-gray-700'
                    }`}
                  >
                    {renderMessageContent(message)}
                    <div className={`flex items-center justify-end mt-1 ${
                      isCurrentUser ? 'text-blue-100' : 'text-gray-400'
                    }`}>
                      <span className="text-xs mr-1">
                        {formatMessageTime(message.createdAt.toString())}
                      </span>
                      {isCurrentUser && getMessageStatus(message)}
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
        )}

        {/* Typing Indicator */}
        {otherUserTyping && (
          <div className="flex items-center mt-2 text-gray-500">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-xs ml-2">{otherUser?.name} is typing...</span>
          </div>
        )}
      </div>

      {/* Message Input */}
      <form
        onSubmit={handleSendMessage}
        className="p-3 border-t dark:border-gray-700 bg-white dark:bg-gray-900"
      >
        {/* Image Preview */}
        {previewUrl && (
          <div className="mb-2 relative">
            <div className="relative w-32 h-24 rounded-lg overflow-hidden">
              <Image
                src={previewUrl}
                alt="Preview"
                fill
                className="object-cover"
              />
            </div>
            <button
              type="button"
              onClick={handleRemoveFile}
              className="absolute top-1 right-1 bg-gray-800 bg-opacity-50 rounded-full p-1 text-white"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}

        <div className="flex items-center">
          <div className="relative">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />

            <button
              type="button"
              onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <Paperclip className="h-5 w-5" />
            </button>

            {showAttachmentMenu && (
              <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 w-48">
                <button
                  type="button"
                  onClick={() => {
                    fileInputRef.current?.click();
                    setShowAttachmentMenu(false);
                  }}
                  className="flex items-center w-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  <span>Image</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    // This would open a property selector in a real app
                    toast({
                      title: "Feature coming soon",
                      description: "Property sharing will be available soon.",
                    });
                    setShowAttachmentMenu(false);
                  }}
                  className="flex items-center w-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                >
                  <Home className="h-4 w-4 mr-2" />
                  <span>Property</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    // This would open a location picker in a real app
                    toast({
                      title: "Feature coming soon",
                      description: "Location sharing will be available soon.",
                    });
                    setShowAttachmentMenu(false);
                  }}
                  className="flex items-center w-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                >
                  <MapPin className="h-4 w-4 mr-2" />
                  <span>Location</span>
                </button>
              </div>
            )}
          </div>

          <input
            type="text"
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              handleTyping();
            }}
            placeholder="Type your message..."
            className="flex-1 px-4 py-2 border rounded-l-full dark:border-gray-700 bg-gray-50 dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={!conversationId || !isConnected}
          />
          <button
            type="submit"
            disabled={(!newMessage.trim() && !selectedFile) || !conversationId || !isConnected}
            className="px-4 py-2 bg-blue-500 text-white rounded-r-full disabled:bg-gray-300 disabled:text-gray-500 dark:disabled:bg-gray-700"
          >
            <Send className="h-5 w-5" />
          </button>
        </div>
      </form>
    </div>
  );
};

export default MessageUI;