"use client"

import { useState, useEffect } from "react"
import { PropertyCard } from "@/components/property-card"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { fetchPropertiesByLocation } from "@/app/actions/rentcast-actions"

interface PropertyListProps {
  postalCode?: string
  duration?: number
}

export function PropertyList({ postalCode, duration = 12 }: PropertyListProps) {
  const [properties, setProperties] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadProperties = async () => {
      if (!postalCode) {
        // If no postal code, use default properties
        setProperties(getDefaultProperties())
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const result = await fetchPropertiesByLocation(postalCode)
        if (result.error) {
          setError(result.error)
          toast({
            title: "Error",
            description: result.error,
            variant: "destructive",
          })
          setProperties(getDefaultProperties())
        } else {
          // Format the properties from our Canadian data service
          const formattedProperties =
            result.properties?.map((prop: any) => ({
              id: prop.id || `prop-${Math.random().toString(36).substring(2, 9)}`,
              title: prop.title || "Property",
              address: `${prop.address}, ${prop.city}, ${prop.province} ${prop.postalCode}`,
              price: prop.price || 1500,
              bedrooms: prop.bedrooms || 2,
              bathrooms: prop.bathrooms || 1,
              image:
                prop.images?.[0] || `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(prop.address)}`,
              status: "available",
            })) || []

          setProperties(formattedProperties.length > 0 ? formattedProperties : getDefaultProperties())
        }
      } catch (error: any) {
        setError("Failed to load properties. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load properties. Please try again.",
          variant: "destructive",
        })
        setProperties(getDefaultProperties())
      } finally {
        setIsLoading(false)
      }
    }

    loadProperties()
  }, [postalCode])

  // Fallback to default properties if API fails or no postal code
  const getDefaultProperties = () => {
    return Array.from({ length: 9 }, (_, i) => ({
      id: `prop-${i + 1}`,
      title: `Property ${i + 1}`,
      address: "123 Main Street, Vancouver, BC",
      price: 1500 + i * 100,
      bedrooms: (i % 3) + 1,
      bathrooms: (i % 2) + 1,
      image: `/placeholder.svg?height=200&width=300&text=Property ${i + 1}`,
      status: i % 5 === 0 ? "featured" : "available",
    }))
  }

  if (isLoading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-[300px] w-full rounded-md" />
        ))}
      </div>
    )
  }

  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {properties.map((property) => (
        <PropertyCard
          key={property.id}
          property={{
            id: property.id,
            title: property.title || property.address,
            address: property.address,
            price: property.price || 1500,
            bedrooms: property.bedrooms || 2,
            bathrooms: property.bathrooms || 1,
            image:
              property.image || `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(property.address)}`,
            status: property.status || "available",
          }}
        />
      ))}
    </div>
  )
}
