"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"

interface FileUploadProps {
  onUploadComplete: (url: string) => void
  accept?: string
  maxSize?: number // in bytes
  className?: string
}

export function FileUpload({
  onUploadComplete,
  accept = "image/*",
  maxSize = 5 * 1024 * 1024, // 5MB default
  className = "",
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Reset states
    setError(null)
    setProgress(0)

    // Validate file size
    if (file.size > maxSize) {
      setError(`File size must be less than ${maxSize / 1024 / 1024}MB`)
      return
    }

    // Validate file type
    if (accept !== "*" && !file.type.match(accept.replace("*", ".*"))) {
      setError("Invalid file type")
      return
    }

    try {
      setIsUploading(true)
      const formData = new FormData()
      formData.append("file", file)

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Upload failed")
      }

      const data = await response.json()
      onUploadComplete(data.url)
      toast({
        title: "Upload successful",
        description: "Your file has been uploaded successfully.",
      })
    } catch (err) {
      setError("Failed to upload file. Please try again.")
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setProgress(0)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className={`relative ${className}`}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={accept}
        className="hidden"
      />
      <Button
        onClick={handleClick}
        disabled={isUploading}
        className="w-full"
        variant="outline"
      >
        {isUploading ? (
          <div className="flex items-center gap-2">
            <Upload className="h-4 w-4 animate-bounce" />
            <span>Uploading...</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            <span>Choose File</span>
          </div>
        )}
      </Button>
      {isUploading && (
        <Progress value={progress} className="mt-2" />
      )}
      {error && (
        <div className="mt-2 text-sm text-red-500 flex items-center gap-2">
          <X className="h-4 w-4" />
          {error}
        </div>
      )}
    </div>
  )
} 