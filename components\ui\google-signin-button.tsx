'use client';

import { FcGoogle } from 'react-icons/fc';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { signInWithPopup, GoogleAuthProvider } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { useRouter } from 'next/navigation';

interface GoogleSignInButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export function GoogleSignInButton({
  className,
  variant = 'outline',
  size = 'default',
}: GoogleSignInButtonProps) {
  const router = useRouter();
  const provider = new GoogleAuthProvider();

  const handleGoogleSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, provider);
      // You can access the user's information here
      console.log('User signed in:', result.user);
      router.push('/');
    } catch (error) {
      console.error('Error signing in with Google:', error);
    }
  };

  return (
    <Button
      className={cn('w-full gap-2', className)}
      variant={variant}
      size={size}
      onClick={handleGoogleSignIn}
    >
      <FcGoogle className="h-5 w-5" />
      <span>Continue with Google</span>
    </Button>
  );
} 