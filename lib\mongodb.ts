import { prisma } from './prisma'

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your MongoDB URI to .env')
}

export async function connectToDatabase() {
  try {
    // Prisma client is already connected when imported
    return prisma
  } catch (error) {
    console.error('Error connecting to database:', error)
    throw new Error('Failed to connect to database')
  }
}

export async function disconnectFromDatabase() {
  try {
    await prisma.$disconnect()
  } catch (error) {
    console.error('Error disconnecting from database:', error)
    throw new Error('Failed to disconnect from database')
  }
}

// Helper function to handle MongoDB query building
export function buildQuery(filters: Record<string, any>) {
  const query: Record<string, any> = {}

  for (const [key, value] of Object.entries(filters)) {
    if (value !== undefined && value !== null && value !== '') {
      query[key] = value
    }
  }

  return query
}

// Helper function to handle MongoDB ObjectId conversion
export function toObjectId(id: string) {
  return id
}

// Helper function to handle MongoDB date conversion
export function toDate(date: Date | string) {
  return new Date(date)
}

// Example usage:
/*
import { connectToDatabase } from '@/lib/mongodb';

export async function someApiRoute() {
  try {
    const { db } = await connectToDatabase();
    const collection = db.collection('your_collection');
    const result = await collection.find({}).toArray();
    return result;
  } catch (error) {
    console.error('Database error:', error);
    throw error;
  }
}
*/ 