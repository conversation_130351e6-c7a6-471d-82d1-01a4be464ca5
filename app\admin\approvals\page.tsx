import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { PropertyApprovalCard } from "@/components/admin/property-approval-card"

export default function ApprovalsPage() {
  // Mock data for pending approvals
  const pendingProperties = [
    {
      id: "prop-1",
      title: "Modern Downtown Apartment",
      address: "123 Main Street, Vancouver, BC",
      landlord: "John Smith",
      landlordId: "user-1",
      submittedDate: "2023-05-15",
      price: 1800,
      bedrooms: 2,
      bathrooms: 1,
      images: Array(3).fill("/placeholder.svg?height=200&width=300"),
      status: "pending",
    },
    {
      id: "prop-2",
      title: "Spacious Family Home",
      address: "456 Oak Avenue, Toronto, ON",
      landlord: "<PERSON> Johnson",
      landlordId: "user-2",
      submittedDate: "2023-05-14",
      price: 2200,
      bedrooms: 3,
      bathrooms: 2,
      images: Array(4).fill("/placeholder.svg?height=200&width=300"),
      status: "pending",
    },
    {
      id: "prop-3",
      title: "Cozy Studio Apartment",
      address: "789 Pine Street, Montreal, QC",
      landlord: "<PERSON>",
      landlordId: "user-3",
      submittedDate: "2023-05-13",
      price: 1200,
      bedrooms: 1,
      bathrooms: 1,
      images: Array(2).fill("/placeholder.svg?height=200&width=300"),
      status: "pending",
    },
  ]

  // Mock data for recently approved properties
  const approvedProperties = [
    {
      id: "prop-4",
      title: "Luxury Penthouse",
      address: "101 High Street, Vancouver, BC",
      landlord: "Emily Davis",
      landlordId: "user-4",
      submittedDate: "2023-05-10",
      approvedDate: "2023-05-11",
      price: 3500,
      bedrooms: 3,
      bathrooms: 2,
      images: Array(5).fill("/placeholder.svg?height=200&width=300"),
      status: "approved",
    },
    {
      id: "prop-5",
      title: "Suburban Townhouse",
      address: "202 Maple Road, Toronto, ON",
      landlord: "Robert Wilson",
      landlordId: "user-5",
      submittedDate: "2023-05-09",
      approvedDate: "2023-05-10",
      price: 1950,
      bedrooms: 3,
      bathrooms: 2.5,
      images: Array(3).fill("/placeholder.svg?height=200&width=300"),
      status: "approved",
    },
  ]

  // Mock data for rejected properties
  const rejectedProperties = [
    {
      id: "prop-6",
      title: "Basement Suite",
      address: "303 Cedar Lane, Calgary, AB",
      landlord: "Jennifer Lee",
      landlordId: "user-6",
      submittedDate: "2023-05-08",
      rejectedDate: "2023-05-09",
      rejectionReason: "Insufficient information provided. Please add more details about the property.",
      price: 1100,
      bedrooms: 1,
      bathrooms: 1,
      images: Array(1).fill("/placeholder.svg?height=200&width=300"),
      status: "rejected",
    },
  ]

  return (
    <div className="container px-4 py-8 md:px-6 md:py-12">
      <div className="mb-8 space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight md:text-4xl">Property Approvals</h1>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {pendingProperties.length} Pending
            </Badge>
            <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
              {approvedProperties.length} Approved
            </Badge>
            <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
              {rejectedProperties.length} Rejected
            </Badge>
          </div>
        </div>
        <p className="text-muted-foreground">Review and manage property listings submitted by landlords</p>
      </div>

      <Tabs defaultValue="pending" className="space-y-8">
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-6">
          {pendingProperties.length > 0 ? (
            pendingProperties.map((property) => <PropertyApprovalCard key={property.id} property={property} />)
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <p className="mb-2 text-lg font-medium">No pending approvals</p>
                <p className="text-muted-foreground">All property listings have been reviewed.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="approved" className="space-y-6">
          {approvedProperties.length > 0 ? (
            approvedProperties.map((property) => <PropertyApprovalCard key={property.id} property={property} />)
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <p className="mb-2 text-lg font-medium">No approved properties</p>
                <p className="text-muted-foreground">Approved properties will appear here.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rejected" className="space-y-6">
          {rejectedProperties.length > 0 ? (
            rejectedProperties.map((property) => <PropertyApprovalCard key={property.id} property={property} />)
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10 text-center">
                <p className="mb-2 text-lg font-medium">No rejected properties</p>
                <p className="text-muted-foreground">Rejected properties will appear here.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
