"use client"

import Link from "next/link"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Bed, Bath, Square, MapPin, Heart, Star } from "lucide-react"
import { useState } from "react"
import { But<PERSON> } from "./ui/button"
import { useCurrency } from "@/components/currency-provider"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface PropertyCardProps {
  id: string
  title: string
  address: string
  city: string
  province: string
  price: number
  bedrooms: number
  bathrooms: number
  squareFeet: number
  imageUrl?: string
  isNew?: boolean
  isFeatured?: boolean
}

export function PropertyCard({
  id,
  title,
  address,
  city,
  province,
  price,
  bedrooms,
  bathrooms,
  squareFeet,
  imageUrl,
  isNew = false,
  isFeatured = false,
}: PropertyCardProps) {
  const { formatAmount } = useCurrency()
  const [favorite, setFavorite] = useState(false)

  const formattedPrice = formatAmount(price)

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setFavorite(!favorite)
  }

  return (
    <Card className="overflow-hidden group h-full flex flex-col transition-all duration-300 hover:shadow-card glass-card border-border/30">
      <div className="h-full flex flex-col">
        <div className="aspect-[4/3] relative overflow-hidden">
          <Link href={`/properties/${id}`} className="block relative">
            <Image
              src={imageUrl || `/placeholder.svg?height=300&width=400&text=${encodeURIComponent(title)}`}
              alt={title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />

            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-40 group-hover:opacity-60 transition-opacity duration-500" />

            <div className="absolute top-3 left-3 flex gap-2">
              {isNew && (
                <Badge className="bg-primary text-white py-1 px-3 rounded-full">New</Badge>
              )}
              {isFeatured && (
                <Badge className="bg-accent text-accent-foreground py-1 px-3 rounded-full">Featured</Badge>
              )}
            </div>
          </Link>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-3 right-3 bg-white/10 hover:bg-white/20 text-white hover:text-rose-500 backdrop-blur-md h-9 w-9 rounded-full flex items-center justify-center shadow-sm transition-all duration-300 hover:scale-110"
                  onClick={handleFavoriteClick}
                >
                  <Heart className={`h-5 w-5 transition-all duration-300 ${favorite ? "fill-rose-500 text-rose-500 scale-110" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{favorite ? "Remove from favorites" : "Add to favorites"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <Link
          href={`/properties/${id}`}
          className="flex flex-col flex-grow"
          legacyBehavior>
          <CardContent className="p-5 flex-grow">
            <div className="flex flex-col h-full justify-between">
              <div>
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0 text-primary" />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="truncate hover:text-primary transition-colors cursor-pointer">
                          {city}, {province}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className="w-80">
                        <div className="space-y-1">
                          <h4 className="text-sm font-semibold">{title}</h4>
                          <p className="text-sm">{address}</p>
                          <p className="text-xs text-muted-foreground">{city}, {province}</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <h3 className="font-bold text-lg line-clamp-1 mb-1 font-montserrat group-hover:text-primary/90 transition-colors">{title}</h3>

                <p className="text-sm text-muted-foreground line-clamp-1 mb-4 group-hover:text-foreground transition-colors">
                  {address}
                </p>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent group-hover:from-primary/90 group-hover:to-primary/60 transition-all duration-300">
                    {formattedPrice}
                    <span className="text-sm font-normal text-muted-foreground ml-1">/month</span>
                  </p>
                </div>

                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Star className="h-3.5 w-3.5 fill-amber-400 text-amber-400" />
                  <span>4.9</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Link>

        <CardFooter className="px-5 py-3 border-t border-border/20 bg-muted/10 flex items-center justify-between text-xs backdrop-blur-sm">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1.5 text-muted-foreground hover:text-primary transition-colors cursor-pointer">
                  <Bed className="h-3.5 w-3.5 text-primary" />
                  <span>{bedrooms}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{bedrooms} {bedrooms === 1 ? 'Bedroom' : 'Bedrooms'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1.5 text-muted-foreground hover:text-primary transition-colors cursor-pointer">
                  <Bath className="h-3.5 w-3.5 text-primary" />
                  <span>{bathrooms}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{bathrooms} {bathrooms === 1 ? 'Bathroom' : 'Bathrooms'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1.5 text-muted-foreground hover:text-primary transition-colors cursor-pointer">
                  <Square className="h-3.5 w-3.5 text-primary" />
                  <span>{squareFeet} ft²</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{squareFeet} square feet</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardFooter>
      </div>
    </Card>
  );
}
