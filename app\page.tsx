"use client"

import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Building2,
  Search,
  Home,
  TrendingUp,
  Shield,
  Users,
  ArrowRight,
  MapPin,
  CheckCircle,
  Star,
  Calendar,
  ExternalLink
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PropertyCard } from "@/components/property-card"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

// Sample property data for the featured properties section
const featuredProperties = [
  {
    id: "1",
    title: "Modern Downtown Apartment",
    address: "123 Main Street",
    city: "Vancouver",
    province: "BC",
    price: 2200,
    bedrooms: 2,
    bathrooms: 1,
    squareFeet: 850,
    imageUrl: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
    isNew: true,
  },
  {
    id: "2",
    title: "Spacious Family Home",
    address: "456 Oak Avenue",
    city: "Toronto",
    province: "ON",
    price: 3500,
    bedrooms: 4,
    bathrooms: 2.5,
    squareFeet: 1800,
    imageUrl: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGhvdXNlfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
    isFeatured: true,
  },
  {
    id: "3",
    title: "Cozy Studio Loft",
    address: "789 Pine Street",
    city: "Montreal",
    province: "QC",
    price: 1400,
    bedrooms: 1,
    bathrooms: 1,
    squareFeet: 550,
    imageUrl: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  }
];

// Sample city data for display in the popular locations section
const popularCities = [
  { name: "Vancouver", count: 482, image: "https://images.unsplash.com/photo-1560814304-4f05b62af116?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dmFuY291dmVyfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60" },
  { name: "Toronto", count: 643, image: "https://images.unsplash.com/photo-1517090504586-fde19ea6066f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8dG9yb250b3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=1000&q=60" },
  { name: "Montreal", count: 327, image: "https://images.unsplash.com/photo-1519178614-68673b201f36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bW9udHJlYWx8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=1000&q=60" },
  { name: "Calgary", count: 215, image: "https://images.unsplash.com/photo-1609861742187-a03e47266552?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y2FsZ2FyeXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=1000&q=60" }
];

// Testimonials
const testimonials = [
  {
    name: "Sarah Johnson",
    role: "Renter",
    content: "RentCentral made finding my dream apartment so easy! The market insights feature helped me make an informed decision.",
    avatar: "https://i.pravatar.cc/150?img=1"
  },
  {
    name: "Michael Chen",
    role: "Property Owner",
    content: "As a landlord, I've been able to find quality tenants quickly. The platform is intuitive and the support team is exceptional.",
    avatar: "https://i.pravatar.cc/150?img=3"
  },
  {
    name: "Emily Rodriguez",
    role: "Renter",
    content: "The virtual tours saved me so much time. I was able to find and secure a great apartment without multiple in-person visits.",
    avatar: "https://i.pravatar.cc/150?img=5"
  }
];

export default function HomePage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [propertyType, setPropertyType] = useState("all")

  useEffect(() => {
    if (!loading && user) {
      router.push("/dashboard")
    }
  }, [user, loading, router])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const params = new URLSearchParams()

    if (searchQuery) {
      params.append("search", searchQuery)
    }

    if (propertyType && propertyType !== "all") {
      params.append("propertyType", propertyType)
    }

    router.push(`/properties?${params.toString()}`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative min-h-[90vh] flex items-center bg-mesh">
        <div className="absolute inset-0 z-0 overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1501183638710-841dd1904471?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8aG9tZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=2000&q=80"
            alt="Modern apartment living room"
            fill
            className="object-cover brightness-[0.4] transform scale-105 animate-float"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/90 to-transparent" />
        </div>

        <div className="container relative z-10 py-20">
          <div className="max-w-3xl animate-fadeIn">
            <Badge className="bg-primary/10 text-primary mb-4 py-1 px-4 rounded-full">
              Find Your Next Home
            </Badge>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-montserrat font-bold text-white mb-6 leading-tight">
              Discover Your <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">Perfect</span> Home
            </h1>
            <p className="text-lg text-white/80 mb-8 max-w-xl leading-relaxed">
              Search thousands of verified rental properties across Canada.
              Transparent pricing, detailed information, and a seamless renting experience.
            </p>

            <div className="glass rounded-2xl p-6 max-w-2xl shadow-elevate animate-fadeIn animate-delay-200">
              <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
                <div className="flex-grow relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="City, address, or postal code"
                    className="pl-9 py-6 rounded-xl bg-background/60 backdrop-blur-sm"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <Select value={propertyType} onValueChange={setPropertyType}>
                  <SelectTrigger className="w-full md:w-[180px] py-6 rounded-xl bg-background/60 backdrop-blur-sm">
                    <SelectValue placeholder="Property Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Properties</SelectItem>
                    <SelectItem value="apartment">Apartments</SelectItem>
                    <SelectItem value="house">Houses</SelectItem>
                    <SelectItem value="condo">Condos</SelectItem>
                    <SelectItem value="townhouse">Townhouses</SelectItem>
                    <SelectItem value="studio">Studios</SelectItem>
                    <SelectItem value="loft">Lofts</SelectItem>
                  </SelectContent>
                </Select>

                <Button type="submit" className="py-6 px-8 rounded-xl bg-primary hover:bg-primary/90 transition-colors">
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </Button>
              </form>

              <div className="flex flex-wrap gap-2 mt-4 justify-center md:justify-start">
                <span className="text-xs text-white/70">Popular:</span>
                {['Vancouver', 'Toronto', 'Montreal', 'Calgary'].map((city) => (
                  <Link
                    key={city}
                    href={`/properties?search=${city}`}
                    className="text-xs text-white/90 hover:text-primary transition-colors">
                    {city}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Stats bar */}
        <div className="absolute bottom-0 left-0 right-0 glass border-t border-border/20">
          <div className="container py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-2">
                <p className="text-xl md:text-3xl font-bold text-white">18,500+</p>
                <p className="text-xs md:text-sm text-white/70">Properties Listed</p>
              </div>
              <div className="text-center p-2">
                <p className="text-xl md:text-3xl font-bold text-white">12,000+</p>
                <p className="text-xs md:text-sm text-white/70">Happy Renters</p>
              </div>
              <div className="text-center p-2">
                <p className="text-xl md:text-3xl font-bold text-white">5,800+</p>
                <p className="text-xs md:text-sm text-white/70">Property Owners</p>
              </div>
              <div className="text-center p-2">
                <p className="text-xl md:text-3xl font-bold text-white">120+</p>
                <p className="text-xs md:text-sm text-white/70">Canadian Cities</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Featured Properties */}
      <section className="container py-20">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-10">
          <div>
            <Badge variant="outline" className="mb-2">Featured Properties</Badge>
            <h2 className="text-3xl font-montserrat font-bold mb-2">Handpicked Properties</h2>
            <p className="text-muted-foreground">Discover our curated selection of premium rental listings</p>
          </div>
          <Button variant="outline" className="gap-2 rounded-full px-6" asChild>
            <Link href="/properties">
              <span className="flex items-center">
                View All <ArrowRight className="h-4 w-4 ml-2" />
              </span>
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredProperties.map((property, index) => (
            <div key={property.id} className={`animate-fadeIn animate-delay-${(index + 1) * 100}`}>
              <PropertyCard {...property} />
            </div>
          ))}
        </div>
      </section>
      {/* How It Works */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-2">Simple Process</Badge>
            <h2 className="text-3xl font-montserrat font-bold mb-4">How RentCentral Works</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We've streamlined the rental process to make finding your next home stress-free
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            <Card className="glass-card border-none relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-12 h-12 bg-primary/10 flex items-center justify-center rounded-br-xl z-10">
                <span className="text-primary font-bold">1</span>
              </div>
              <CardHeader className="pt-12">
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5 text-primary" />
                  Search Properties
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Browse thousands of verified listings with detailed information, high-quality photos, and virtual tours.
                </p>
              </CardContent>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/50 to-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            </Card>

            <Card className="glass-card border-none relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-12 h-12 bg-primary/10 flex items-center justify-center rounded-br-xl z-10">
                <span className="text-primary font-bold">2</span>
              </div>
              <CardHeader className="pt-12">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  Schedule Viewings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Book in-person or virtual property tours directly through our platform at times that work for you.
                </p>
              </CardContent>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/50 to-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            </Card>

            <Card className="glass-card border-none relative overflow-hidden group">
              <div className="absolute top-0 left-0 w-12 h-12 bg-primary/10 flex items-center justify-center rounded-br-xl z-10">
                <span className="text-primary font-bold">3</span>
              </div>
              <CardHeader className="pt-12">
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-primary" />
                  Apply & Move In
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Submit rental applications online, sign documents digitally, and manage your entire rental journey.
                </p>
              </CardContent>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/50 to-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            </Card>
          </div>
        </div>
      </section>
      {/* Popular Cities */}
      <section className="container py-20">
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-2">Top Locations</Badge>
          <h2 className="text-3xl font-montserrat font-bold mb-4">Explore Popular Cities</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover rental opportunities in these top locations across Canada
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {popularCities.map((city, index) => (
            <Link
              key={city.name}
              href={`/properties?search=${city.name}`}
              className={`group relative h-80 overflow-hidden rounded-2xl shadow-card animate-fadeIn animate-delay-${index * 100}`}>
              <Image
                src={city.image}
                alt={city.name}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent"></div>
              <div className="absolute inset-0 flex flex-col justify-end p-6">
                <h3 className="text-white text-2xl font-montserrat font-bold mb-1">{city.name}</h3>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary" />
                  <p className="text-white/90 text-sm">{city.count} properties</p>
                </div>
                <div className="mt-4 mb-2">
                  <span className="inline-flex items-center gap-1 py-1 px-3 bg-white/10 backdrop-blur-sm rounded-full text-xs text-white">
                    <ExternalLink className="h-3 w-3" /> Explore
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>
      {/* Testimonials */}
      <section className="bg-mesh py-20">
        <div className="container">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-2">Testimonials</Badge>
            <h2 className="text-3xl font-montserrat font-bold mb-4">What Our Users Say</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Read about experiences from our community of renters and property owners
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className={`glass-card border-none animate-fadeIn animate-delay-${index * 100}`}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <div className="relative h-12 w-12 rounded-full overflow-hidden">
                        <Image
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <CardTitle className="text-base">{testimonial.name}</CardTitle>
                        <CardDescription>{testimonial.role}</CardDescription>
                      </div>
                    </div>
                    <div className="text-primary">
                      <Star className="h-5 w-5 fill-primary" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/90">"{testimonial.content}"</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section className="container py-20">
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-2">Platform Features</Badge>
          <h2 className="text-3xl font-montserrat font-bold mb-4">Why Choose RentCentral</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We've built powerful tools to make your rental journey seamless from start to finish
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          <div className="flex flex-col items-center text-center p-6 animate-fadeIn animate-delay-100">
            <div className="bg-primary/10 p-4 rounded-full mb-6">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-montserrat font-bold mb-2">Verified Listings</h3>
            <p className="text-muted-foreground">
              All our listings are verified to ensure you're seeing legitimate rentals with accurate information.
            </p>
          </div>

          <div className="flex flex-col items-center text-center p-6 animate-fadeIn animate-delay-200">
            <div className="bg-primary/10 p-4 rounded-full mb-6">
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-montserrat font-bold mb-2">Market Insights</h3>
            <p className="text-muted-foreground">
              Access detailed market data to make informed decisions about pricing and neighborhood trends.
            </p>
          </div>

          <div className="flex flex-col items-center text-center p-6 animate-fadeIn animate-delay-300">
            <div className="bg-primary/10 p-4 rounded-full mb-6">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-montserrat font-bold mb-2">Dedicated Support</h3>
            <p className="text-muted-foreground">
              Our team of rental experts is available to guide you through the entire process.
            </p>
          </div>
        </div>
      </section>
      {/* CTA Section */}
      <section className="bg-primary/5 py-20">
        <div className="container">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-10 p-10 rounded-3xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <div className="max-w-xl">
              <h2 className="text-3xl font-montserrat font-bold mb-4">Ready to Find Your Next Home?</h2>
              <p className="text-muted-foreground mb-6">
                Join thousands of happy renters who've found their perfect home with RentCentral.
                Start your search today or list your property to find qualified tenants quickly.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button asChild size="lg" className="rounded-full px-8">
                  <Link href="/properties">Browse Properties</Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="rounded-full px-8">
                  <Link href="/register">Sign Up Free</Link>
                </Button>
              </div>
            </div>
            <div className="lg:w-1/3 relative h-[300px] w-full rounded-2xl overflow-hidden shadow-glow">
              <Image
                src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fGhvbWV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60"
                alt="Find your home"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/40 to-transparent opacity-70"></div>
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <Badge className="bg-white/20 text-white backdrop-blur-sm">Find Your Perfect Home</Badge>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
