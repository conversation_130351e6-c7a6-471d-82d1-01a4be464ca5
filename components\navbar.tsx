"use client"

import React, { useState, useEffect, Fragment } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import {
  Search,
  Menu,
  X,
  Building2,
  LogIn,
  LayoutDashboard,
  Home,
  MapPin,
  Shield,
  Bell,
  MessageSquare,
  User,
  ChevronDown
} from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ChatPanel } from "./chat-panel"

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

interface ExtendedUser extends User {
  role?: string;
}

export default function Navbar() {
  const pathname = usePathname()
  const router = useRouter()
  const { user } = useAuth() as { user: ExtendedUser | null }
  const [searchQuery, setSearchQuery] = useState("")
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/properties?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  const navLinks = [
    { title: "Home", href: "/" },
    { title: "Properties", href: "/properties" },
    { title: "Market Insights", href: "/market-insights" },
    { title: "About", href: "/about" },
  ]

  const propertyTypes = [
    { title: "Apartments", href: "/properties?type=apartment", icon: Building2 },
    { title: "Houses", href: "/properties?type=house", icon: Home },
    { title: "Condos", href: "/properties?type=condo", icon: Building2 },
    { title: "Townhouses", href: "/properties?type=townhouse", icon: Home },
    { title: "Studios", href: "/properties?type=studio", icon: Building2 },
    { title: "Lofts", href: "/properties?type=loft", icon: Building2 },
  ]

  const adminNavigationItems = [
    {
      name: 'Admin Dashboard',
      href: '/dashboard/admin',
      icon: Shield,
    },
  ]

  const userInitials = user?.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : 'U'

  return (
    <Fragment>
      <header className={cn(
        "sticky top-0 z-50 w-full transition-all duration-200",
        isScrolled ? "bg-background/80 backdrop-blur-md border-b" : "bg-transparent"
      )}>
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-6 md:gap-10">
            <Link href="/" className="flex items-center gap-2">
              <div className="relative h-9 w-9 overflow-hidden text-primary bg-primary/10 rounded-xl flex items-center justify-center">
                <Building2 className="h-5 w-5" />
              </div>
              <span className="font-montserrat text-xl font-bold bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent">RentCentral</span>
            </Link>

            <NavigationMenu className="hidden md:flex">
              <NavigationMenuList>
                {navLinks.map((link) => (
                  link.href === "/properties" ? (
                    <NavigationMenuItem key={link.href}>
                      <NavigationMenuTrigger className={cn(
                        "text-base",
                        pathname === link.href ? "text-primary font-medium" : "text-foreground/80"
                      )}>
                        Properties
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                          {propertyTypes.map((item) => {
                            const Icon = item.icon
                            return (
                              <ListItem key={item.title} title={item.title} href={item.href}>
                                <div className="flex items-center gap-2">
                                  <Icon className="h-4 w-4" />
                                  <span>{item.title}</span>
                                </div>
                              </ListItem>
                            )
                          })}
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  ) : (
                    <NavigationMenuItem key={link.href}>
                      <NavigationMenuLink
                        asChild
                        className={cn(
                          "text-base",
                          pathname === link.href
                            ? "text-primary font-medium"
                            : "text-foreground/80 hover:text-foreground"
                        )}
                      >
                        <Link href={link.href} className={navigationMenuTriggerStyle()}>
                          {link.title}
                        </Link>
                      </NavigationMenuLink>
                    </NavigationMenuItem>
                  )
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          <div className="flex items-center gap-4">
            <form onSubmit={handleSearch} className="hidden md:flex relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search properties..."
                  className="w-[200px] lg:w-[300px] pl-9 pr-4 rounded-full bg-background/80 backdrop-blur-sm border-muted"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button type="submit" size="icon" variant="ghost" className="absolute right-0 top-0 h-full">
                <span className="sr-only">Search</span>
              </Button>
            </form>

            {user ? (
              <div className="flex items-center gap-3">
                {/* Notifications */}
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Bell className="h-5 w-5 text-muted-foreground" />
                </Button>

                {/* Messages */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full"
                  onClick={() => setIsChatOpen(true)}
                >
                  <MessageSquare className="h-5 w-5 text-muted-foreground" />
                </Button>

                {/* User dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="rounded-full p-0 h-9 w-9">
                      <Avatar className="h-9 w-9 border-2 border-primary/20">
                        <AvatarImage src={user?.image || ""} alt={user?.name || "User"} />
                        <AvatarFallback className="bg-primary/10 text-primary">
                          {userInitials}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56 mt-1">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{user?.name}</p>
                        <p className="text-xs text-muted-foreground">{user?.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard" className="cursor-pointer">
                        <div className="flex items-center">
                          <LayoutDashboard className="mr-2 h-4 w-4" />
                          <span>Dashboard</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="cursor-pointer">
                        <div className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    {user?.role === 'admin' && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href="/admin" className="cursor-pointer">
                            <div className="flex items-center">
                              <Shield className="mr-2 h-4 w-4" />
                              <span>Admin Dashboard</span>
                            </div>
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => router.push('/auth/logout')} className="cursor-pointer text-destructive focus:text-destructive">
                      <LogIn className="mr-2 h-4 w-4 rotate-180" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="ghost" className="text-foreground/80 hover:text-foreground hidden md:block" asChild>
                  <Link href="/auth/login">
                    Sign In
                  </Link>
                </Button>
                <Button variant="default" className="rounded-full bg-primary hover:bg-primary/90 hidden md:block" asChild>
                  <Link href="/auth/register">
                    Register
                  </Link>
                </Button>
              </div>
            )}

            {/* Mobile menu button */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent className="flex flex-col h-full">
                <div className="px-1 py-4 flex justify-between items-center border-b">
                  <Link
                    href="/"
                    className="flex items-center gap-2"
                    onClick={() => setIsMobileMenuOpen(false)}>
                      <div className="relative h-8 w-8 overflow-hidden text-primary bg-primary/10 rounded-xl flex items-center justify-center">
                        <Building2 className="h-5 w-5" />
                      </div>
                      <span className="font-montserrat text-lg font-bold bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent">RentCentral</span>
                  </Link>
                </div>

                <div className="mt-6 px-1">
                  <form onSubmit={handleSearch} className="relative mb-6">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search properties..."
                      className="w-full pl-9 rounded-full border-muted"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Button type="submit" size="icon" variant="ghost" className="absolute right-0 top-0 h-full">
                      <span className="sr-only">Search</span>
                    </Button>
                  </form>

                  <nav className="flex flex-col space-y-4">
                    {navLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className={cn(
                          "flex items-center py-2 px-2 rounded-md transition-colors",
                          pathname === link.href ? "text-primary font-medium bg-primary/5" : "text-foreground/80"
                        )}
                        onClick={() => setIsMobileMenuOpen(false)}>
                        {link.title}
                      </Link>
                    ))}

                    {/* Property types for mobile */}
                    <div className="py-2 px-2">
                      <p className="text-sm font-medium mb-2">Property Types</p>
                      <div className="space-y-2 ml-2">
                        {propertyTypes.map((item) => {
                          const Icon = item.icon
                          return (
                            <Link
                              key={item.href}
                              href={item.href}
                              className="flex items-center gap-2 text-foreground/80 py-1"
                              onClick={() => setIsMobileMenuOpen(false)}>
                              <Icon className="h-4 w-4" />
                              <span>{item.title}</span>
                            </Link>
                          );
                        })}
                      </div>
                    </div>
                  </nav>
                </div>

                <div className="mt-auto border-t px-1 py-4">
                  {user ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-3 p-2">
                        <Avatar className="h-10 w-10 border-2 border-primary/20">
                          <AvatarImage src={user?.image || ""} alt={user?.name || "User"} />
                          <AvatarFallback className="bg-primary/10 text-primary">
                            {userInitials}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <p className="text-sm font-medium">{user?.name}</p>
                          <p className="text-xs text-muted-foreground">{user?.email}</p>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <Link
                          href="/dashboard"
                          className="flex items-center gap-2 p-2 rounded-md text-foreground/80 hover:bg-muted transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}>
                          <LayoutDashboard className="h-4 w-4" />
                          <span>Dashboard</span>
                        </Link>
                        <Link
                          href="/profile"
                          className="flex items-center gap-2 p-2 rounded-md text-foreground/80 hover:bg-muted transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}>
                          <User className="h-4 w-4" />
                          <span>Profile</span>
                        </Link>
                        {user?.role === 'admin' && (
                          <Link
                            href="/admin"
                            className="flex items-center gap-2 p-2 rounded-md text-foreground/80 hover:bg-muted transition-colors"
                            onClick={() => setIsMobileMenuOpen(false)}>
                            <Shield className="h-4 w-4" />
                            <span>Admin Dashboard</span>
                          </Link>
                        )}
                        <button
                          onClick={() => {
                            setIsMobileMenuOpen(false)
                            router.push('/auth/logout')
                          }}
                          className="flex items-center gap-2 p-2 rounded-md text-destructive hover:bg-destructive/5 transition-colors w-full text-left"
                        >
                          <LogIn className="h-4 w-4 rotate-180" />
                          <span>Log out</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col gap-2">
                      <Button variant="outline" className="w-full" asChild>
                        <Link
                          href="/auth/login"
                          onClick={() => setIsMobileMenuOpen(false)}>
                          Sign In
                        </Link>
                      </Button>
                      <Button variant="default" className="w-full" asChild>
                        <Link
                          href="/auth/register"
                          onClick={() => setIsMobileMenuOpen(false)}>
                          Register
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>
      {/* Chat Panel */}
      <ChatPanel isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
    </Fragment>
  );
}

function ListItem({ className, title, href, children }: React.ComponentPropsWithoutRef<"a"> & { title: string, href: string }) {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          href={href}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none hover:bg-accent hover:text-accent-foreground transition-colors",
            className
          )}>
          <div>
            <div className="text-sm font-medium leading-none">{title}</div>
            <div className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</div>
          </div>
        </Link>
      </NavigationMenuLink>
    </li>
  );
}
