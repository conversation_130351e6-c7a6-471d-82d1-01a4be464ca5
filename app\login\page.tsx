"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useFirebaseAuth } from "@/hooks/useFirebaseAuth"
import { FaGoogle } from "react-icons/fa"

export default function Login() {
  const { signInWithGoogle, loading } = useFirebaseAuth()

  return (
    <div className="flex min-h-[calc(100vh-64px)] items-center justify-center">
      <div className="mx-auto w-full max-w-sm space-y-6 rounded-lg border p-6 shadow-md">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Sign In</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Sign in to your account to continue
          </p>
        </div>
        <div className="space-y-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={signInWithGoogle}
            disabled={loading}
          >
            {loading ? (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-600 border-t-transparent" />
            ) : (
              <FaGoogle className="mr-2 h-4 w-4" />
            )}
            Sign in with Google
          </Button>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500 dark:bg-gray-900 dark:text-gray-400">
                Or
              </span>
            </div>
          </div>
          
          <p className="text-center text-sm text-gray-500 dark:text-gray-400">
            By continuing, you agree to our{" "}
            <a href="#" className="underline underline-offset-2">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="underline underline-offset-2">
              Privacy Policy
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  )
} 