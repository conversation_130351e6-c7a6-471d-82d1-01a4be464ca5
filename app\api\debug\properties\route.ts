import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';

export async function GET() {
  try {
    // Connect directly to MongoDB
    const uri = process.env.MONGODB_URI;
    if (!uri) {
      throw new Error('MONGODB_URI is not defined');
    }

    const client = new MongoClient(uri);
    await client.connect();

    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Count properties
    const count = await propertiesCollection.countDocuments();

    // Get sample properties
    const properties = await propertiesCollection.find().limit(5).toArray();

    // Count by city
    const cityCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$city", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();

    await client.close();

    return NextResponse.json({
      success: true,
      count,
      cityCounts: cityCounts.map(city => ({
        city: city._id,
        count: city.count
      })),
      sampleProperties: properties.map(property => ({
        id: property._id,
        title: property.title,
        city: property.city,
        province: property.province,
        price: property.price,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        status: property.status
      }))
    });
  } catch (error) {
    console.error('Error fetching properties:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch properties',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
