"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"

interface DurationPricingProps {
  basePrice: number
}

export function DurationPricing({ basePrice }: DurationPricingProps) {
  const [selectedDuration, setSelectedDuration] = useState(12)

  // Calculate price adjustments based on duration
  const getPriceForDuration = (months: number) => {
    switch (months) {
      case 3:
        return Math.round(basePrice * 1.2) // 20% premium for short-term
      case 6:
        return Math.round(basePrice * 1.1) // 10% premium for mid-term
      case 12:
      default:
        return basePrice // Base price for standard 12-month lease
    }
  }

  return (
    <div className="space-y-4">
      <h3 className="font-medium">Lease Duration</h3>
      <div className="grid grid-cols-3 gap-2">
        {[3, 6, 12].map((months) => (
          <Button
            key={months}
            variant={selectedDuration === months ? "default" : "outline"}
            onClick={() => setSelectedDuration(months)}
            className="w-full"
          >
            {months} {months === 1 ? "month" : "months"}
          </Button>
        ))}
      </div>
      <div className="mt-2 text-center">
        <div className="text-2xl font-bold">
          ${getPriceForDuration(selectedDuration)}
          <span className="text-base font-normal text-muted-foreground">/month</span>
        </div>
        {selectedDuration !== 12 && (
          <p className="text-xs text-muted-foreground">
            {selectedDuration === 3 ? "Short-term leases have a 20% premium" : "6-month leases have a 10% premium"}
          </p>
        )}
      </div>
    </div>
  )
}
