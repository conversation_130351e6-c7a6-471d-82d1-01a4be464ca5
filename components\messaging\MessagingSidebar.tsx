import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { MessageCircle, ChevronRight, ChevronLeft, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

// Types for conversation data
type ConversationUser = {
  id: string;
  name: string;
  image: string;
};

export type ConversationWithUsers = {
  id: string;
  userA: ConversationUser;
  userB: ConversationUser;
  messages: {
    content: string;
    createdAt: string;
    read: boolean;
    senderId: string;
  }[];
  lastMessageAt: string;
};

type MessagingSidebarProps = {
  currentUser: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
};

const MessagingSidebar = ({ currentUser }: MessagingSidebarProps) => {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const [conversations, setConversations] = useState<ConversationWithUsers[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/conversations');

        if (response.ok) {
          const data = await response.json();
          setConversations(data);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();
  }, []);

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  const getOtherUser = (conversation: ConversationWithUsers) => {
    return conversation.userA.id === currentUser.id
      ? conversation.userB
      : conversation.userA;
  };

  const handleConversationClick = (conversationId: string) => {
    router.push(`/messages?conversationId=${conversationId}`);
  };

  // Function to format the time of the last message
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    // Calculate difference in days using UTC methods to avoid timezone issues
    const diffInDays = Math.floor(
      (now.setHours(0, 0, 0, 0) - new Date(date).setHours(0, 0, 0, 0)) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays === 0) {
      // Use a more stable approach for time formatting
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

      return `${formattedHours}:${formattedMinutes} ${ampm}`;
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      // Use a more stable approach for weekday formatting
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      return days[date.getDay()];
    } else {
      // Use a more stable approach for date formatting
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${months[date.getMonth()]} ${date.getDate()}`;
    }
  };

  return (
    <div
      className={cn(
        "fixed right-0 top-1/4 h-3/5 bg-white dark:bg-gray-900 shadow-lg rounded-l-xl transition-all duration-300 z-50 flex flex-col",
        isExpanded ? "w-80" : "w-14"
      )}
    >
      <div className="flex items-center justify-between p-3 border-b dark:border-gray-700">
        {isExpanded && (
          <h3 className="text-lg font-semibold">Messages</h3>
        )}

        <button
          onClick={toggleSidebar}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          {isExpanded ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
        </button>
      </div>

      {isExpanded ? (
        <div className="flex-1 overflow-y-auto p-2">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-gray-100"></div>
            </div>
          ) : conversations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4">
              <MessageCircle className="h-10 w-10 text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">No conversations yet.</p>
              <p className="text-xs text-gray-400 mt-1">
                Start messaging property owners or landlords via property listings.
              </p>
            </div>
          ) : (
            <ul className="space-y-1">
              {conversations.map((conversation) => {
                const otherUser = getOtherUser(conversation);
                const lastMessage = conversation.messages[0];
                const hasUnread = lastMessage && !lastMessage.read && lastMessage.senderId !== currentUser.id;

                return (
                  <li
                    key={conversation.id}
                    onClick={() => handleConversationClick(conversation.id)}
                    className={cn(
                      "flex items-center p-2 rounded-lg cursor-pointer",
                      hasUnread
                        ? "bg-blue-50 dark:bg-blue-900/20"
                        : "hover:bg-gray-100 dark:hover:bg-gray-800"
                    )}
                  >
                    <div className="relative">
                      <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                        {otherUser.image ? (
                          <Image
                            src={otherUser.image}
                            alt={otherUser.name || 'User'}
                            width={40}
                            height={40}
                            className="object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-500">
                            {otherUser.name?.charAt(0) || '?'}
                          </div>
                        )}
                      </div>
                      {hasUnread && (
                        <span className="absolute top-0 right-0 h-3 w-3 rounded-full bg-blue-500"></span>
                      )}
                    </div>

                    <div className="ml-3 flex-1 truncate">
                      <div className="flex justify-between items-center">
                        <p className={cn(
                          "text-sm",
                          hasUnread ? "font-semibold" : "font-normal"
                        )}>
                          {otherUser.name || 'User'}
                        </p>
                        {lastMessage && (
                          <span className="text-xs text-gray-500">
                            {formatTime(conversation.lastMessageAt)}
                          </span>
                        )}
                      </div>

                      {lastMessage && (
                        <p className="text-xs text-gray-500 truncate">
                          {lastMessage.content}
                        </p>
                      )}
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center pt-4 flex-1">
          <MessageCircle className="h-6 w-6 mb-4" />
          {conversations.filter(c =>
            c.messages.some(m => !m.read && m.senderId !== currentUser.id)
          ).length > 0 && (
            <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
              {conversations.filter(c =>
                c.messages.some(m => !m.read && m.senderId !== currentUser.id)
              ).length}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MessagingSidebar;