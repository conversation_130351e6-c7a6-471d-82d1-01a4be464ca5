"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/components/auth-provider"

interface PropertyAppealProps {
  propertyId: string
  declineReason: string
  onAppealSubmitted?: () => void
}

export function PropertyAppeal({ propertyId, declineReason, onAppealSubmitted }: PropertyAppealProps) {
  const [appealReason, setAppealReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const { user } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!appealReason.trim()) {
      toast({
        title: "Error",
        description: "Please provide a reason for your appeal",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/properties/${propertyId}/appeal`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${await user?.getIdToken()}`,
        },
        body: JSON.stringify({ appealReason }),
      })

      if (!response.ok) {
        throw new Error("Failed to submit appeal")
      }

      toast({
        title: "Success",
        description: "Your appeal has been submitted successfully",
      })

      onAppealSubmitted?.()
    } catch (error) {
      console.error("Error submitting appeal:", error)
      toast({
        title: "Error",
        description: "Failed to submit appeal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Appeal Property Listing</CardTitle>
        <CardDescription>
          Your property listing was declined. Please provide a reason for your appeal.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <h4 className="font-medium mb-2">Decline Reason:</h4>
          <p className="text-sm text-muted-foreground">{declineReason}</p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="appealReason" className="block text-sm font-medium mb-2">
                Appeal Reason
              </label>
              <Textarea
                id="appealReason"
                value={appealReason}
                onChange={(e) => setAppealReason(e.target.value)}
                placeholder="Please explain why you believe your property listing should be approved..."
                className="min-h-[100px]"
              />
            </div>
          </div>
          <CardFooter className="px-0 pt-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit Appeal"}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  )
} 