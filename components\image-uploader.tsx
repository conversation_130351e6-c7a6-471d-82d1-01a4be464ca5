"use client"

import type React from "react"
import { useState, useRef } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Upload, X, ImageIcon } from "lucide-react"

interface ImageUploaderProps {
  onImagesUploaded: (imageUrls: string[]) => void
  maxImages?: number
  initialImages?: string[]
}

export function ImageUploader({ onImagesUploaded, maxImages = 5, initialImages = [] }: ImageUploaderProps) {
  const [images, setImages] = useState<string[]>(initialImages)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    // Check if adding these files would exceed the maximum
    if (images.length + files.length > maxImages) {
      toast({
        title: "Too many images",
        description: `You can only upload a maximum of ${maxImages} images.`,
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)

    try {
      const newImageUrls: string[] = []

      // Upload each file using the API route
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const formData = new FormData()
        formData.append('file', file)
        formData.append('bucketName', 'properties')

        const response = await fetch('/api/images/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error('Failed to upload image')
        }

        const result = await response.json()
        newImageUrls.push(result.url)
      }

      const updatedImages = [...images, ...newImageUrls]
      setImages(updatedImages)
      onImagesUploaded(updatedImages)

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload images. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = async (index: number) => {
    try {
      const imageToRemove = images[index]
      
      // Extract file ID from the URL
      const fileId = imageToRemove.split('/').pop() || ''
      const bucketName = imageToRemove.split('/').slice(-2, -1)[0] || 'properties'

      // Delete using the API route
      const response = await fetch(`/api/images/delete?fileId=${fileId}&bucketName=${bucketName}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete image')
      }

      const updatedImages = images.filter((_, i) => i !== index)
      setImages(updatedImages)
      onImagesUploaded(updatedImages)
    } catch (error: any) {
      toast({
        title: "Error removing image",
        description: error.message || "Failed to remove image. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image, index) => (
          <Card key={index} className="relative aspect-square">
            <Image
              src={image}
              alt={`Uploaded image ${index + 1}`}
              fill
              className="object-cover rounded-lg"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2"
              onClick={() => handleRemoveImage(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </Card>
        ))}
        {images.length < maxImages && (
          <Card
            className="aspect-square flex items-center justify-center cursor-pointer hover:bg-accent/50 transition-colors"
            onClick={handleButtonClick}
          >
            <div className="text-center">
              <Upload className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Upload Image</p>
            </div>
          </Card>
        )}
      </div>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        multiple
        onChange={handleFileChange}
      />
    </div>
  )
}
