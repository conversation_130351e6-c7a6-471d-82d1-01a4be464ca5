'use client';

import { useState, useEffect } from 'react';
import { 
  getAuth, 
  onAuthStateChanged, 
  signInWithPopup, 
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  User
} from 'firebase/auth';
import { useRouter } from 'next/navigation';
import firebase from '@/lib/firebase';

// Initialize Firebase auth
const auth = getAuth(firebase);
const googleProvider = new GoogleAuthProvider();

// Define types
interface AuthUser {
  uid: string;
  email: string | null;
  name: string | null;
  photoURL: string | null;
  token: string;
}

interface UseFirebaseAuthReturn {
  user: AuthUser | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
}

export function useFirebaseAuth(): UseFirebaseAuthReturn {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const router = useRouter();

  // Handle auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      
      if (firebaseUser) {
        // User is signed in
        const token = await firebaseUser.getIdToken();
        const authUser: AuthUser = {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          name: firebaseUser.displayName,
          photoURL: firebaseUser.photoURL,
          token
        };
        setUser(authUser);
      } else {
        // User is signed out
        setUser(null);
      }
      
      setLoading(false);
    });

    // Cleanup subscription
    return () => unsubscribe();
  }, []);

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      await signInWithPopup(auth, googleProvider);
      router.push('/dashboard');
    } catch (error) {
      console.error('Error signing in with Google:', error);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return {
    user,
    loading,
    signInWithGoogle,
    signOut
  };
}

// Helper to format user for compatibility with previous next-auth patterns
export function formatUser(user: User | null) {
  if (!user) return null;
  
  return {
    id: user.uid,
    name: user.displayName,
    email: user.email,
    image: user.photoURL
  };
}

export default useFirebaseAuth; 