"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import Link from "next/link"
import Image from "next/image"
import {
  Building2,
  Plus,
  ArrowLeft,
  Edit,
  Trash2,
  MoreHorizontal,
  Users,
  Calendar,
  MapPin,
  DollarSign,
  ArrowUpDown,
  Search
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Sample data for landlord properties
const properties = [
  {
    id: "prop-1",
    title: "Modern Downtown Apartment",
    address: "123 Main Street",
    city: "Vancouver",
    province: "BC",
    price: 2200,
    bedrooms: 2,
    bathrooms: 1,
    squareFeet: 850,
    status: "occupied",
    tenantCount: 2,
    leaseEnd: "2023-12-15",
    imageUrl: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  },
  {
    id: "prop-2",
    title: "Spacious Family Home",
    address: "456 Oak Avenue",
    city: "Toronto",
    province: "ON",
    price: 3500,
    bedrooms: 4,
    bathrooms: 2.5,
    squareFeet: 1800,
    status: "occupied",
    tenantCount: 4,
    leaseEnd: "2024-02-01",
    imageUrl: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGhvdXNlfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  },
  {
    id: "prop-3",
    title: "Cozy Studio Loft",
    address: "789 Pine Street",
    city: "Montreal",
    province: "QC",
    price: 1400,
    bedrooms: 1,
    bathrooms: 1,
    squareFeet: 550,
    status: "vacant",
    tenantCount: 0,
    leaseEnd: null,
    imageUrl: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXBhcnRtZW50fGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  },
  {
    id: "prop-4",
    title: "Luxury Waterfront Condo",
    address: "101 Harbor Drive",
    city: "Vancouver",
    province: "BC",
    price: 3800,
    bedrooms: 3,
    bathrooms: 2,
    squareFeet: 1200,
    status: "occupied",
    tenantCount: 2,
    leaseEnd: "2024-04-30",
    imageUrl: "https://images.unsplash.com/photo-1493809842364-78817add7ffb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGNvbmRvfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  },
  {
    id: "prop-5",
    title: "Contemporary Townhouse",
    address: "222 Birch Road",
    city: "Calgary",
    province: "AB",
    price: 2800,
    bedrooms: 3,
    bathrooms: 2.5,
    squareFeet: 1550,
    status: "maintenance",
    tenantCount: 0,
    leaseEnd: null,
    imageUrl: "https://images.unsplash.com/photo-1549517045-bc93de075e53?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8dG93bmhvdXNlfGVufDB8fDB8fHww&auto=format&fit=crop&w=1000&q=60",
  }
];

export default function LandlordPropertiesPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest")
  const [filteredProperties, setFilteredProperties] = useState(properties)

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }

    // In a real application, we would fetch landlord properties here
    // Example: fetchLandlordProperties(user.id).then(data => setProperties(data))
  }, [user, loading, router])

  // Apply filters and sorting
  useEffect(() => {
    let results = [...properties]

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      results = results.filter(property =>
        property.title.toLowerCase().includes(search) ||
        property.address.toLowerCase().includes(search) ||
        property.city.toLowerCase().includes(search) ||
        property.province.toLowerCase().includes(search)
      )
    }

    // Apply status filter
    if (statusFilter !== "all") {
      results = results.filter(property => property.status === statusFilter)
    }

    // Apply sorting
    switch (sortBy) {
      case "price-asc":
        results.sort((a, b) => a.price - b.price)
        break
      case "price-desc":
        results.sort((a, b) => b.price - a.price)
        break
      case "newest":
        // In a real app, would sort by creation date
        // For demo, we'll keep the existing order
        break
      case "alphabetical":
        results.sort((a, b) => a.title.localeCompare(b.title))
        break
    }

    setFilteredProperties(results)
  }, [searchTerm, statusFilter, sortBy])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "occupied":
        return <Badge className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200">Occupied</Badge>
      case "vacant":
        return <Badge className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">Vacant</Badge>
      case "maintenance":
        return <Badge className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">Maintenance</Badge>
      default:
        return null
    }
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" className="gap-2 mb-4 hover:bg-primary/5" asChild>
          <Link href="/dashboard/landlord">
            <span className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Landlord Dashboard
            </span>
          </Link>
        </Button>
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          Your Properties
        </h1>
        <p className="text-muted-foreground">
          Manage all your rental properties in one place
        </p>
      </div>
      {/* Filters & Actions */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-primary/10 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-80">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search properties..."
              className="pl-8 border-primary/20 focus-visible:ring-primary/30"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40 border-primary/20">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="occupied">Occupied</SelectItem>
              <SelectItem value="vacant">Vacant</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full sm:w-40 border-primary/20">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="price-asc">Price: Low to High</SelectItem>
              <SelectItem value="price-desc">Price: High to Low</SelectItem>
              <SelectItem value="alphabetical">Alphabetical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button asChild className="gap-2">
          <Link href="/dashboard/properties/new">
            <span className="flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </span>
          </Link>
        </Button>
      </div>
      {/* Property Cards */}
      {filteredProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProperties.map((property) => (
            <Card key={property.id} className="border-0 property-card-shadow overflow-hidden bg-white/90 backdrop-blur-sm border border-primary/5 hover:shadow-lg transition-shadow">
              <div className="relative h-48">
                <Image
                  src={property.imageUrl}
                  alt={property.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-2 right-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 bg-white/80 hover:bg-white shadow-sm">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="border-primary/10">
                      <DropdownMenuLabel>Property Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/landlord/properties/${property.id}`}>
                          <span className="flex items-center">
                            <Building2 className="mr-2 h-4 w-4 text-primary/70" />
                            View Details
                          </span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/landlord/properties/${property.id}/edit`}>
                          <span className="flex items-center">
                            <Edit className="mr-2 h-4 w-4 text-primary/70" />
                            Edit Property
                          </span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/landlord/properties/${property.id}/tenants`}>
                          <span className="flex items-center">
                            <Users className="mr-2 h-4 w-4 text-primary/70" />
                            Manage Tenants
                          </span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Property
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="absolute bottom-2 left-2">
                  {getStatusBadge(property.status)}
                </div>
              </div>

              <CardHeader className="border-b border-primary/5">
                <CardTitle className="line-clamp-1">{property.title}</CardTitle>
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-1 text-primary/60" />
                  {property.city}, {property.province}
                </div>
              </CardHeader>

              <CardContent className="pt-4">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex flex-col">
                    <span className="text-sm text-muted-foreground">Rent</span>
                    <span className="font-semibold flex items-center">
                      <DollarSign className="h-4 w-4 text-primary/60" />
                      {property.price}/mo
                    </span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm text-muted-foreground">Size</span>
                    <span className="font-semibold">{property.squareFeet} sq ft</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm text-muted-foreground">Tenants</span>
                    <span className="font-semibold flex items-center">
                      <Users className="h-4 w-4 mr-1 text-primary/60" />
                      {property.tenantCount > 0 ? property.tenantCount : "None"}
                    </span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm text-muted-foreground">Lease Ends</span>
                    <span className="font-semibold flex items-center">
                      <Calendar className="h-4 w-4 mr-1 text-primary/60" />
                      {property.leaseEnd ? new Date(property.leaseEnd).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      }) : "N/A"}
                    </span>
                  </div>
                </div>

                <Button variant="outline" className="w-full border-primary/20 bg-white/80 hover:bg-primary/5" asChild>
                  <Link href={`/dashboard/landlord/properties/${property.id}`}>
                    View Details
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="border-0 property-card-shadow bg-white/90 backdrop-blur-sm border border-primary/5">
          <CardContent className="py-12 text-center">
            <Building2 className="h-12 w-12 text-primary/40 mx-auto mb-4" />
            {searchTerm || statusFilter !== "all" ? (
              <>
                <h3 className="text-xl font-semibold mb-2">No properties match your filters</h3>
                <p className="text-muted-foreground mb-6">
                  Try adjusting your search criteria or filters
                </p>
                <Button onClick={() => {
                  setSearchTerm("")
                  setStatusFilter("all")
                }}>
                  Clear Filters
                </Button>
              </>
            ) : (
              <>
                <h3 className="text-xl font-semibold mb-2">No properties yet</h3>
                <p className="text-muted-foreground mb-6">
                  Add your first property to start managing your rentals
                </p>
                <Button asChild>
                  <Link href="/dashboard/properties/new">
                    <span className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Property
                    </span>
                  </Link>
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}