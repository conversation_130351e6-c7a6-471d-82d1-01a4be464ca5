'use client';

import { useEffect, useState } from 'react';
import { Property } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, MapPin, Bed, Bath, DollarSign } from 'lucide-react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function PropertiesPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [properties, setProperties] = useState<Property[]>([]);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [activeTab, setActiveTab] = useState(searchParams.get('propertyType') || 'all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProperties();
  }, [searchParams]);

  const fetchProperties = async () => {
    try {
      // Get search and type parameters from URL
      const searchQuery = searchParams.get('search');
      const typeQuery = searchParams.get('propertyType');
      const cityQuery = searchParams.get('city');

      // Build query string
      const queryParams = new URLSearchParams();
      if (typeQuery && typeQuery !== 'all') {
        queryParams.append('propertyType', typeQuery);
      }
      if (cityQuery) {
        queryParams.append('city', cityQuery);
      }

      // Fetch properties with filters
      const url = `/api/properties${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('Fetching properties from:', url);

      console.log('Fetching properties from URL:', url);
      const response = await fetch(url, {
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} - ${await response.text()}`);
      }

      const data = await response.json();
      console.log(`Fetched ${Array.isArray(data) ? data.length : 0} properties:`, data);

      if (Array.isArray(data) && data.length > 0) {
        console.log('First property:', {
          id: data[0].id,
          title: data[0].title,
          city: data[0].city,
          status: data[0].status
        });
      } else {
        console.log('No properties found in response');
      }

      setProperties(Array.isArray(data) ? data : []);

      // Set active tab based on URL parameter
      if (typeQuery) {
        setActiveTab(typeQuery);
      }

      // Set search term based on URL parameter
      if (searchQuery) {
        setSearchTerm(searchQuery);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter properties based on search term
  const filteredProperties = Array.isArray(properties)
    ? properties.filter(property => {
        if (!searchTerm) return true;

        const searchLower = searchTerm.toLowerCase();
        return (
          (property.title && property.title.toLowerCase().includes(searchLower)) ||
          (property.city && property.city.toLowerCase().includes(searchLower)) ||
          (property.province && property.province.toLowerCase().includes(searchLower)) ||
          (property.address && property.address.toLowerCase().includes(searchLower)) ||
          (property.description && property.description.toLowerCase().includes(searchLower))
        );
      })
    : [];

  // Function to create a URL with the selected property type
  const getPropertyTypeUrl = (type: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (type === 'all') {
      params.delete('propertyType');
    } else {
      params.set('propertyType', type);
    }
    return `/properties?${params.toString()}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 className="text-3xl font-bold mb-4 md:mb-0">Available Properties</h1>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const params = new URLSearchParams(searchParams.toString());
            if (searchTerm) {
              params.set('search', searchTerm);
            } else {
              params.delete('search');
            }
            router.push(`/properties?${params.toString()}`);
          }}
          className="relative w-full md:w-64 flex"
        >
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search properties..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" className="ml-2">
            Search
          </Button>
        </form>
      </div>

      {/* Property Type Tabs */}
      <div className="mb-8">
        <Tabs value={activeTab} className="w-full">
          <TabsList className="w-full justify-start mb-4 overflow-x-auto flex-nowrap">
            <TabsTrigger value="all" asChild>
              <a href={getPropertyTypeUrl('all')}>All Properties</a>
            </TabsTrigger>
            <TabsTrigger value="apartment" asChild>
              <a href={getPropertyTypeUrl('apartment')}>Apartments</a>
            </TabsTrigger>
            <TabsTrigger value="house" asChild>
              <a href={getPropertyTypeUrl('house')}>Houses</a>
            </TabsTrigger>
            <TabsTrigger value="condo" asChild>
              <a href={getPropertyTypeUrl('condo')}>Condos</a>
            </TabsTrigger>
            <TabsTrigger value="townhouse" asChild>
              <a href={getPropertyTypeUrl('townhouse')}>Townhouses</a>
            </TabsTrigger>
            <TabsTrigger value="studio" asChild>
              <a href={getPropertyTypeUrl('studio')}>Studios</a>
            </TabsTrigger>
            <TabsTrigger value="loft" asChild>
              <a href={getPropertyTypeUrl('loft')}>Lofts</a>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      ) : filteredProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProperties.map((property) => (
            <Card key={property.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle>{property.title}</CardTitle>
                <CardDescription className="flex items-center">
                  <MapPin className="w-4 h-4 mr-1" />
                  {property.city}, {property.province}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <div className="flex items-center">
                      <Bed className="w-4 h-4 mr-1" />
                      <span>{property.bedrooms || 0} beds</span>
                    </div>
                    <div className="flex items-center">
                      <Bath className="w-4 h-4 mr-1" />
                      <span>{property.bathrooms || 0} baths</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>${property.price || 0}/mo</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 line-clamp-3">{property.description}</p>
                  <Button className="w-full" asChild>
                    <a href={`/properties/${property.id}`}>View Details</a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">No properties found</h2>
            <p className="text-muted-foreground mb-6">
              {activeTab === 'all'
                ? "We couldn't find any properties matching your search criteria."
                : `We couldn't find any ${activeTab}s matching your search criteria.`}
            </p>
            <Button asChild>
              <a href="/properties">View All Properties</a>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
