"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import Link from "next/link"
import {
  ArrowLeft,
  DollarSign,
  CreditCard,
  BarChart2,
  Plus,
  ArrowDown,
  ArrowUp,
  Calendar,
  DownloadCloud,
  Filter,
  Search,
  Building2
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"

// Sample data
const transactions = [
  {
    id: "tx-1",
    date: "2023-05-01",
    type: "income",
    category: "rent",
    description: "May Rent - Unit 101",
    amount: 2200,
    status: "completed",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    tenant: "<PERSON>"
  },
  {
    id: "tx-2",
    date: "2023-05-01",
    type: "income",
    category: "rent",
    description: "May Rent - Unit 201",
    amount: 3500,
    status: "completed",
    propertyId: "prop-2",
    propertyName: "Spacious Family Home",
    tenant: "Michael Brown"
  },
  {
    id: "tx-3",
    date: "2023-05-02",
    type: "expense",
    category: "maintenance",
    description: "Plumbing Repair - Unit 101",
    amount: 350,
    status: "completed",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    tenant: null
  },
  {
    id: "tx-4",
    date: "2023-05-03",
    type: "income",
    category: "rent",
    description: "May Rent - Unit 301",
    amount: 1400,
    status: "pending",
    propertyId: "prop-3",
    propertyName: "Cozy Studio Loft",
    tenant: "Lisa Taylor"
  },
  {
    id: "tx-5",
    date: "2023-05-05",
    type: "expense",
    category: "utilities",
    description: "Water Bill - Common Areas",
    amount: 125,
    status: "completed",
    propertyId: null,
    propertyName: "All Properties",
    tenant: null
  },
  {
    id: "tx-6",
    date: "2023-05-10",
    type: "expense",
    category: "insurance",
    description: "Property Insurance - May",
    amount: 450,
    status: "completed",
    propertyId: null,
    propertyName: "All Properties",
    tenant: null
  },
  {
    id: "tx-7",
    date: "2023-05-15",
    type: "expense",
    category: "property_tax",
    description: "Property Tax - Q2",
    amount: 1200,
    status: "scheduled",
    propertyId: null,
    propertyName: "All Properties",
    tenant: null
  },
  {
    id: "tx-8",
    date: "2023-06-01",
    type: "income",
    category: "rent",
    description: "June Rent - Unit 101",
    amount: 2200,
    status: "scheduled",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    tenant: "John Smith"
  }
];

// Calculate monthly data
const incomeByMonth = [12500, 13200, 12800, 13500, 14100, 14300, 14500, 14800, 14700, 15100, 15400, 15700];
const expensesByMonth = [4800, 5100, 4500, 5200, 4900, 5300, 5100, 5400, 5000, 5200, 5300, 5500];
const netIncomeByMonth = incomeByMonth.map((income, index) => income - expensesByMonth[index]);

// Financial summary calculations
const currentMonth = new Date().getMonth();
const currentMonthIncome = incomeByMonth[currentMonth];
const currentMonthExpenses = expensesByMonth[currentMonth];
const currentMonthNet = currentMonthIncome - currentMonthExpenses;
const previousMonthIncome = incomeByMonth[currentMonth - 1 >= 0 ? currentMonth - 1 : 11];
const previousMonthExpenses = expensesByMonth[currentMonth - 1 >= 0 ? currentMonth - 1 : 11];
const previousMonthNet = previousMonthIncome - previousMonthExpenses;

const incomeChange = ((currentMonthIncome - previousMonthIncome) / previousMonthIncome * 100).toFixed(1);
const expensesChange = ((currentMonthExpenses - previousMonthExpenses) / previousMonthExpenses * 100).toFixed(1);
const netChange = ((currentMonthNet - previousMonthNet) / previousMonthNet * 100).toFixed(1);

export default function LandlordFinancesPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [propertyFilter, setPropertyFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [filteredTransactions, setFilteredTransactions] = useState(transactions)

  // Apply filters
  useEffect(() => {
    let results = [...transactions]

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      results = results.filter(tx =>
        tx.description.toLowerCase().includes(search) ||
        (tx.propertyName && tx.propertyName.toLowerCase().includes(search)) ||
        (tx.tenant && tx.tenant.toLowerCase().includes(search))
      )
    }

    // Apply type filter
    if (typeFilter !== "all") {
      results = results.filter(tx => tx.type === typeFilter)
    }

    // Apply property filter
    if (propertyFilter !== "all") {
      results = results.filter(tx => tx.propertyId === propertyFilter ||
        (propertyFilter === "common" && tx.propertyId === null))
    }

    // Apply status filter
    if (statusFilter !== "all") {
      results = results.filter(tx => tx.status === statusFilter)
    }

    // Sort by date (newest first)
    results.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    setFilteredTransactions(results)
  }, [searchTerm, typeFilter, propertyFilter, statusFilter])

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // Get unique properties for the filter
  const uniqueProperties = Array.from(
    new Set(transactions.filter(tx => tx.propertyId).map(tx => tx.propertyId))
  ).map(propertyId => {
    const tx = transactions.find(t => t.propertyId === propertyId)
    return {
      id: propertyId,
      name: tx ? tx.propertyName : 'Unknown Property'
    }
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200">Completed</Badge>
      case "pending":
        return <Badge className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">Pending</Badge>
      case "scheduled":
        return <Badge className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">Scheduled</Badge>
      default:
        return null
    }
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" className="gap-2 mb-4" asChild>
          <Link href="/dashboard/landlord" legacyBehavior>
            <ArrowLeft className="h-4 w-4" />
            Back to Landlord Dashboard
          </Link>
        </Button>
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          <span className="flex items-center gap-2">
            <DollarSign className="h-6 w-6 text-primary" />
            Financial Dashboard
          </span>
        </h1>
        <p className="text-muted-foreground">
          Track your rental income, expenses, and overall financial performance
        </p>
      </div>
      {/* Financial Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="border-0 property-card-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Monthly Income
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentMonthIncome)}</div>
            <div className="flex items-center mt-1">
              <span className={`text-xs ${Number(incomeChange) >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {Number(incomeChange) >= 0 ? (
                  <ArrowUp className="h-3 w-3 inline mr-1" />
                ) : (
                  <ArrowDown className="h-3 w-3 inline mr-1" />
                )}
                {Math.abs(Number(incomeChange))}%
              </span>
              <span className="text-xs text-muted-foreground ml-1">
                vs last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 property-card-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Monthly Expenses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentMonthExpenses)}</div>
            <div className="flex items-center mt-1">
              <span className={`text-xs ${Number(expensesChange) <= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {Number(expensesChange) <= 0 ? (
                  <ArrowDown className="h-3 w-3 inline mr-1" />
                ) : (
                  <ArrowUp className="h-3 w-3 inline mr-1" />
                )}
                {Math.abs(Number(expensesChange))}%
              </span>
              <span className="text-xs text-muted-foreground ml-1">
                vs last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 property-card-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Net Income
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentMonthNet)}</div>
            <div className="flex items-center mt-1">
              <span className={`text-xs ${Number(netChange) >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
                {Number(netChange) >= 0 ? (
                  <ArrowUp className="h-3 w-3 inline mr-1" />
                ) : (
                  <ArrowDown className="h-3 w-3 inline mr-1" />
                )}
                {Math.abs(Number(netChange))}%
              </span>
              <span className="text-xs text-muted-foreground ml-1">
                vs last month
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
      {/* Monthly Performance Chart */}
      <Card className="border-0 property-card-shadow mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Yearly Financial Performance</CardTitle>
              <CardDescription>Monthly income, expenses, and net profit</CardDescription>
            </div>
            <BarChart2 className="h-5 w-5 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full">
            {/* This would be a chart in a real application */}
            <div className="h-full w-full flex items-end gap-2 relative">
              <div className="absolute inset-0 grid grid-rows-4 border-b">
                <div className="border-t border-dashed border-muted-foreground/20 flex items-center">
                  <span className="text-xs text-muted-foreground pl-2">$20k</span>
                </div>
                <div className="border-t border-dashed border-muted-foreground/20 flex items-center">
                  <span className="text-xs text-muted-foreground pl-2">$15k</span>
                </div>
                <div className="border-t border-dashed border-muted-foreground/20 flex items-center">
                  <span className="text-xs text-muted-foreground pl-2">$10k</span>
                </div>
                <div className="border-t border-dashed border-muted-foreground/20 flex items-center">
                  <span className="text-xs text-muted-foreground pl-2">$5k</span>
                </div>
              </div>

              {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, idx) => (
                <div key={month} className="flex-1 flex flex-col items-center gap-1">
                  {/* Bars */}
                  <div className="w-full flex items-end justify-center gap-0.5 h-[250px]">
                    <div
                      className="w-2 bg-primary/20 rounded-t"
                      style={{ height: `${(incomeByMonth[idx] / 20000) * 100}%` }}
                    ></div>
                    <div
                      className="w-2 bg-rose-500/20 rounded-t"
                      style={{ height: `${(expensesByMonth[idx] / 20000) * 100}%` }}
                    ></div>
                    <div
                      className="w-4 bg-emerald-500 rounded-t"
                      style={{ height: `${(netIncomeByMonth[idx] / 20000) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-muted-foreground">{month}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-center gap-6 mt-6">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary/20 rounded"></div>
              <span className="text-sm text-muted-foreground">Income</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-rose-500/20 rounded"></div>
              <span className="text-sm text-muted-foreground">Expenses</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-emerald-500 rounded"></div>
              <span className="text-sm text-muted-foreground">Net Profit</span>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Transactions */}
      <Card className="border-0 property-card-shadow">
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <CardTitle>Transactions</CardTitle>
              <CardDescription>Recent financial activity across your properties</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="gap-2" asChild>
                <Link href="/dashboard/landlord/finances/reports" legacyBehavior>
                  <span className="flex items-center">
                    <DownloadCloud className="h-4 w-4 mr-2" />
                    Export Report
                  </span>
                </Link>
              </Button>
              <Button className="gap-2" asChild>
                <Link href="/dashboard/landlord/finances/new-transaction" legacyBehavior>
                  <span className="flex items-center">
                    <Plus className="h-4 w-4 mr-2" />
                    New Transaction
                  </span>
                </Link>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="mb-8">
            <TabsList>
              <TabsTrigger value="all" onClick={() => setTypeFilter("all")}>All</TabsTrigger>
              <TabsTrigger value="income" onClick={() => setTypeFilter("income")}>Income</TabsTrigger>
              <TabsTrigger value="expense" onClick={() => setTypeFilter("expense")}>Expenses</TabsTrigger>
            </TabsList>

            <div className="mt-4 space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search transactions..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <div className="flex gap-2">
                  <Select value={propertyFilter} onValueChange={setPropertyFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Property" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Properties</SelectItem>
                      <SelectItem value="common">Common Expenses</SelectItem>
                      {uniqueProperties.map(property => (
                        <SelectItem key={property.id} value={property.id as string}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <TabsContent value="all" className="mt-6">
              <TransactionsTable
                transactions={filteredTransactions}
                formatCurrency={formatCurrency}
                getStatusBadge={getStatusBadge}
              />
            </TabsContent>

            <TabsContent value="income" className="mt-6">
              <TransactionsTable
                transactions={filteredTransactions.filter(tx => tx.type === "income")}
                formatCurrency={formatCurrency}
                getStatusBadge={getStatusBadge}
              />
            </TabsContent>

            <TabsContent value="expense" className="mt-6">
              <TransactionsTable
                transactions={filteredTransactions.filter(tx => tx.type === "expense")}
                formatCurrency={formatCurrency}
                getStatusBadge={getStatusBadge}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

interface TransactionsTableProps {
  transactions: typeof transactions
  formatCurrency: (amount: number) => string
  getStatusBadge: (status: string) => React.ReactNode
}

function TransactionsTable({ transactions, formatCurrency, getStatusBadge }: TransactionsTableProps) {
  if (transactions.length === 0) {
    return (
      <div className="text-center py-10">
        <CreditCard className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2">No transactions found</h3>
        <p className="text-muted-foreground">
          Try adjusting your filters to see more results
        </p>
      </div>
    )
  }

  return (
    <div className="overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Property</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((tx) => (
            <TableRow key={tx.id}>
              <TableCell>
                {new Date(tx.date).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <div className="font-medium">{tx.description}</div>
                <div className="text-sm text-muted-foreground">
                  {tx.category.charAt(0).toUpperCase() + tx.category.slice(1).replace('_', ' ')}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  {tx.propertyId ? (
                    <Building2 className="h-4 w-4 mr-1 text-muted-foreground" />
                  ) : (
                    <Building2 className="h-4 w-4 mr-1 text-muted-foreground opacity-50" />
                  )}
                  <span>{tx.propertyName}</span>
                </div>
              </TableCell>
              <TableCell>
                {getStatusBadge(tx.status)}
              </TableCell>
              <TableCell className={`text-right font-medium ${tx.type === 'income' ? 'text-emerald-600' : 'text-rose-600'}`}>
                {tx.type === 'income' ? '+' : '-'}{formatCurrency(tx.amount)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}