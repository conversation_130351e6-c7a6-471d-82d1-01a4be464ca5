'use client';

import React from 'react';
import { Button } from './button';
import { Calendar } from 'lucide-react';
import { toast } from './use-toast';

interface GoogleCalendarButtonProps {
  appointmentId?: string;
  title: string;
  description: string;
  startTime: string; // ISO string
  endTime: string; // ISO string
  location?: string;
  className?: string;
}

export function GoogleCalendarButton({
  appointmentId,
  title,
  description,
  startTime,
  endTime,
  location,
  className = '',
}: GoogleCalendarButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const addToGoogleCalendar = () => {
    try {
      setIsLoading(true);
      
      // Create Google Calendar event URL directly
      // This approach doesn't require server-side API calls and works with the user's Google account
      const startDate = new Date(startTime);
      const endDate = new Date(endTime);
      
      // Format dates for Google Calendar URL
      const formatDate = (date: Date) => {
        return date.toISOString().replace(/-|:|\.\d+/g, '');
      };
      
      const startDateFormatted = formatDate(startDate);
      const endDateFormatted = formatDate(endDate);
      
      // Build the Google Calendar URL
      const baseUrl = 'https://calendar.google.com/calendar/render';
      const query = new URLSearchParams({
        action: 'TEMPLATE',
        text: title,
        details: description,
        dates: `${startDateFormatted}/${endDateFormatted}`,
        ...(location && { location }),
      }).toString();
      
      const googleCalendarUrl = `${baseUrl}?${query}`;
      
      // Open the Google Calendar link in a new tab
      window.open(googleCalendarUrl, '_blank');
      
      toast({
        title: 'Google Calendar Opened',
        description: 'Add this event to your Google Calendar',
      });
    } catch (error) {
      console.error('Error opening Google Calendar:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to open Google Calendar',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className={`flex items-center gap-2 ${className}`}
      onClick={addToGoogleCalendar}
      disabled={isLoading}
    >
      <Calendar className="h-4 w-4" />
      {isLoading ? 'Opening...' : 'Add to Calendar'}
    </Button>
  );
} 