'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { format } from 'date-fns';
import { MessageCircle } from 'lucide-react';
import { ConversationWithUsers } from '@/components/messaging/MessagingSidebar';

export default function MessagesIndexPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [conversations, setConversations] = useState<ConversationWithUsers[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/conversations');

        if (response.ok) {
          const data = await response.json();
          setConversations(data);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchConversations();
    }
  }, [session?.user]);

  const getOtherUser = (conversation: ConversationWithUsers) => {
    if (!session?.user?.id) return null;
    return conversation.userA.id === session.user.id
      ? conversation.userB
      : conversation.userA;
  };

  const handleConversationClick = (conversationId: string) => {
    router.push(`/messages?conversationId=${conversationId}`);
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    // Calculate difference in days using UTC methods to avoid timezone issues
    const diffInDays = Math.floor(
      (now.setHours(0, 0, 0, 0) - new Date(date).setHours(0, 0, 0, 0)) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays === 0) {
      // Use a more stable approach for time formatting
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

      return `${formattedHours}:${formattedMinutes} ${ampm}`;
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      // Use a more stable approach for weekday formatting
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      return days[date.getDay()];
    } else {
      // Use a more stable approach for date formatting
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${months[date.getMonth()]} ${date.getDate()}`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100"></div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <MessageCircle className="h-16 w-16 text-gray-400 mb-4" />
        <h2 className="text-xl font-semibold mb-2">No conversations yet</h2>
        <p className="text-gray-500 max-w-md">
          Start messaging property owners or landlords by visiting property listings and clicking the "Message Landlord" button.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Messages</h1>

      <div className="grid md:grid-cols-2 gap-4">
        {conversations.map((conversation) => {
          const otherUser = getOtherUser(conversation);
          if (!otherUser) return null;

          const lastMessage = conversation.messages[0];
          const hasUnread = lastMessage && !lastMessage.read && lastMessage.senderId !== session?.user?.id;

          return (
            <div
              key={conversation.id}
              onClick={() => handleConversationClick(conversation.id)}
              className={`
                p-4 rounded-lg border cursor-pointer transition-colors
                ${hasUnread
                  ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'}
              `}
            >
              <div className="flex items-start">
                <div className="relative">
                  <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                    {otherUser.image ? (
                      <Image
                        src={otherUser.image}
                        alt={otherUser.name || 'User'}
                        width={48}
                        height={48}
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-500 text-lg">
                        {otherUser.name?.charAt(0) || '?'}
                      </div>
                    )}
                  </div>
                  {hasUnread && (
                    <span className="absolute top-0 right-0 h-3 w-3 rounded-full bg-blue-500"></span>
                  )}
                </div>

                <div className="ml-4 flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className={`font-medium ${hasUnread ? 'font-semibold' : ''}`}>
                      {otherUser.name || 'User'}
                    </h3>
                    {lastMessage && (
                      <span className="text-xs text-gray-500">
                        {formatTime(conversation.lastMessageAt)}
                      </span>
                    )}
                  </div>

                  {lastMessage ? (
                    <p className={`text-sm truncate ${
                      hasUnread ? 'text-gray-800 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {lastMessage.content}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-400 italic">No messages yet</p>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}