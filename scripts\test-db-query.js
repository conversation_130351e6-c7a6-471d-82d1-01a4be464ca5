// This script tests a direct query to the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Connecting to database...');
    
    // Query all properties
    console.log('Querying all properties...');
    const allProperties = await prisma.property.findMany({
      include: {
        images: true,
        landlord: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
    
    console.log(`Found ${allProperties.length} properties`);
    
    if (allProperties.length > 0) {
      console.log('First property:', {
        id: allProperties[0].id,
        title: allProperties[0].title,
        city: allProperties[0].city,
        status: allProperties[0].status,
        images: allProperties[0].images?.length || 0
      });
    }
    
    // Count properties by status
    const pendingCount = await prisma.property.count({
      where: { status: 'pending' }
    });
    
    const approvedCount = await prisma.property.count({
      where: { status: 'approved' }
    });
    
    console.log(`Properties with status 'pending': ${pendingCount}`);
    console.log(`Properties with status 'approved': ${approvedCount}`);
    
    console.log('\nDone!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
