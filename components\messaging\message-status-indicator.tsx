'use client';

import { Check, CheckChe<PERSON>, AlertCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface MessageStatusIndicatorProps {
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
}

export function MessageStatusIndicator({ status }: MessageStatusIndicatorProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'sending':
        return <div className="animate-pulse h-3 w-3 rounded-full bg-primary-foreground/50" />;
      case 'sent':
        return <Check className="h-3 w-3 text-primary-foreground/70" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-primary-foreground/70" />;
      case 'read':
        return <CheckCheck className="h-3 w-3 text-primary-foreground" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-destructive" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'sending':
        return 'Sending...';
      case 'sent':
        return 'Sent';
      case 'delivered':
        return 'Delivered';
      case 'read':
        return 'Read';
      case 'error':
        return 'Failed to send';
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center">
            {getStatusIcon()}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{getStatusText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 