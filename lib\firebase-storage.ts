import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import app from "./firebase";
import { v4 as uuidv4 } from "uuid";

const storage = getStorage(app);

/**
 * Uploads an image to Firebase Storage
 * @param file The image file to upload
 * @param path Optional path within storage (defaults to 'properties')
 * @returns Promise with the download URL
 */
export async function uploadImage(file: File, path: string = 'properties'): Promise<string> {
  try {
    // Generate a unique filename
    const fileName = `${path}/${uuidv4()}-${file.name.replace(/\s+/g, '_')}`;
    const storageRef = ref(storage, fileName);
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return downloadURL;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw new Error("Failed to upload image");
  }
}

/**
 * Upload multiple images to Firebase Storage
 * @param files Array of image files to upload
 * @param path Optional path within storage (defaults to 'properties')
 * @returns Promise with an array of download URLs
 */
export async function uploadMultipleImages(files: File[], path: string = 'properties'): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadImage(file, path));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error("Error uploading multiple images:", error);
    throw new Error("Failed to upload one or more images");
  }
}

/**
 * Get the file name from a Firebase Storage URL
 * @param url Firebase Storage download URL
 * @returns The file name
 */
export function getFileNameFromUrl(url: string): string {
  try {
    // Extract the file name from the URL
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/');
    const fileName = pathSegments[pathSegments.length - 1];
    
    // Remove any query parameters
    const nameWithoutParams = fileName.split('?')[0];
    
    return decodeURIComponent(nameWithoutParams);
  } catch (error) {
    console.error("Error extracting file name from URL:", error);
    return "unknown-file";
  }
}

export default {
  uploadImage,
  uploadMultipleImages,
  getFileNameFromUrl
}; 