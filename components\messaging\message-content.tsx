import Image from 'next/image';
import Link from 'next/link';
import { MapPin, Home } from 'lucide-react';
import type { MessageContent } from '@/hooks/useSocket';

interface MessageContentProps {
  content: MessageContent;
}

export function MessageContent({ content }: MessageContentProps) {
  switch (content.type) {
    case 'text':
      return <p className="text-sm">{content.text}</p>;

    case 'image':
      return (
        <div className="relative w-64 h-48 rounded-lg overflow-hidden">
          <Image
            src={content.imageUrl || ''}
            alt="Shared image"
            fill
            className="object-cover"
          />
        </div>
      );

    case 'property':
      return (
        <Link
          href={`/properties/${content.propertyId}`}
          className="block">
          <div className="flex items-center space-x-3 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <div className="relative w-16 h-16 rounded-md overflow-hidden">
              <Image
                src={content.propertyImage || '/placeholder.svg'}
                alt={content.propertyTitle || 'Property'}
                fill
                className="object-cover"
              />
            </div>
            <div>
              <p className="text-sm font-medium">{content.propertyTitle}</p>
              <p className="text-xs text-gray-500">View property details</p>
            </div>
          </div>
        </Link>
      );

    case 'location':
      return (
        <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-red-500" />
            <p className="text-sm">{content.location?.address}</p>
          </div>
          <a
            href={`https://www.google.com/maps?q=${content.location?.lat},${content.location?.lng}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-blue-500 mt-1 block"
          >
            View on Google Maps
          </a>
        </div>
      );

    default:
      return <p className="text-sm">{JSON.stringify(content)}</p>;
  }
}