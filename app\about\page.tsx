"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Mail, Phone, MapPin, Globe, Users, Shield } from "lucide-react"

export default function AboutPage() {
  return (
    <div className="container max-w-6xl px-4 py-12 md:py-16">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">About RentEasy</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Transforming the rental experience with modern technology and exceptional service.
        </p>
      </div>
      {/* Company Info */}
      <div className="grid gap-8 md:grid-cols-2 mb-16">
        <div>
          <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
          <p className="text-muted-foreground mb-6">
            At RentEasy, we're committed to making the rental process seamless and transparent for both tenants and landlords. Our platform combines cutting-edge technology with user-friendly design to create the most efficient rental experience possible.
          </p>
          <div className="flex gap-4">
            <Button variant="default" asChild>
              <Link href="/properties">Browse Properties</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="https://www.trizan.com" target="_blank">Visit Trizan.com</Link>
            </Button>
          </div>
        </div>
        <div className="relative h-[300px] rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3"
            alt="Modern office space"
            fill
            className="object-cover"
          />
        </div>
      </div>
      {/* Contact Information */}
      <Card className="mb-16">
        <CardContent className="p-8">
          <h2 className="text-2xl font-bold mb-6">Contact Information</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Address</h3>
                <p className="text-muted-foreground">Visit us at Trizan's office</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Phone className="h-5 w-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Phone</h3>
                <p className="text-muted-foreground">Contact Trizan support</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Mail className="h-5 w-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Email</h3>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <EMAIL>
                </Link>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Globe className="h-5 w-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Website</h3>
                <Link
                  href="https://www.trizan.com"
                  target="_blank"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  trizan.com
                </Link>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Values Section */}
      <div className="text-center mb-12">
        <h2 className="text-2xl font-bold mb-8">Our Values</h2>
        <div className="grid gap-8 md:grid-cols-3">
          <div className="flex flex-col items-center">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <Building2 className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Innovation</h3>
            <p className="text-muted-foreground">
              Leveraging technology to create better rental experiences
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <Users className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Community</h3>
            <p className="text-muted-foreground">
              Building connections between tenants and landlords
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Trust</h3>
            <p className="text-muted-foreground">
              Ensuring security and transparency in every transaction
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}