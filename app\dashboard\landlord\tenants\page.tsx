"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import Link from "next/link"
import {
  ArrowLeft,
  Users,
  Search,
  Plus,
  Mail,
  Phone,
  Building2,
  Calendar,
  Clock,
  FileText,
  MoreHorizontal,
  MessageSquare,
  Bell,
  AlertCircle,
  ArrowUpDown
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Sample data
const tenants = [
  {
    id: "tenant-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    leaseStart: "2023-01-15",
    leaseEnd: "2024-01-15",
    status: "active",
    paymentStatus: "paid",
    lastPaymentDate: "2023-05-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-2",
    name: "Emily Johnson",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-1",
    propertyName: "Modern Downtown Apartment",
    leaseStart: "2023-01-15",
    leaseEnd: "2024-01-15",
    status: "active",
    paymentStatus: "paid",
    lastPaymentDate: "2023-05-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-3",
    name: "Michael Brown",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-2",
    propertyName: "Spacious Family Home",
    leaseStart: "2023-02-01",
    leaseEnd: "2024-02-01",
    status: "active",
    paymentStatus: "late",
    lastPaymentDate: "2023-04-05",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-4",
    name: "Sarah Davis",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-2",
    propertyName: "Spacious Family Home",
    leaseStart: "2023-02-01",
    leaseEnd: "2024-02-01",
    status: "active",
    paymentStatus: "paid",
    lastPaymentDate: "2023-05-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-5",
    name: "David Wilson",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-2",
    propertyName: "Spacious Family Home",
    leaseStart: "2023-02-01",
    leaseEnd: "2024-02-01",
    status: "active",
    paymentStatus: "paid",
    lastPaymentDate: "2023-05-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-6",
    name: "Jennifer Lee",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-4",
    propertyName: "Luxury Waterfront Condo",
    leaseStart: "2023-04-01",
    leaseEnd: "2024-04-01",
    status: "active",
    paymentStatus: "pending",
    lastPaymentDate: "2023-04-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-7",
    name: "Robert Chen",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-4",
    propertyName: "Luxury Waterfront Condo",
    leaseStart: "2023-04-01",
    leaseEnd: "2024-04-01",
    status: "active",
    paymentStatus: "paid",
    lastPaymentDate: "2023-05-01",
    avatarUrl: "/placeholder-user.jpg"
  },
  {
    id: "tenant-8",
    name: "Lisa Taylor",
    email: "<EMAIL>",
    phone: "************",
    propertyId: "prop-5",
    propertyName: "Contemporary Townhouse",
    leaseStart: "2022-10-15",
    leaseEnd: "2023-04-15",
    status: "ended",
    paymentStatus: "paid",
    lastPaymentDate: "2023-04-01",
    avatarUrl: "/placeholder-user.jpg"
  }
];

export default function LandlordTenantsPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [propertyFilter, setPropertyFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")
  const [filteredTenants, setFilteredTenants] = useState(tenants)

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }

    // In a real application, we would fetch landlord tenants here
    // Example: fetchLandlordTenants(user.id).then(data => setTenants(data))
  }, [user, loading, router])

  // Get unique properties for the filter
  const uniqueProperties = Array.from(
    new Set(tenants.map(tenant => tenant.propertyId))
  ).map(propertyId => {
    const tenant = tenants.find(t => t.propertyId === propertyId)
    return {
      id: propertyId,
      name: tenant ? tenant.propertyName : 'Unknown Property'
    }
  });

  // Apply filters and sorting
  useEffect(() => {
    let results = [...tenants]

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      results = results.filter(tenant =>
        tenant.name.toLowerCase().includes(search) ||
        tenant.email.toLowerCase().includes(search) ||
        tenant.phone.includes(search) ||
        tenant.propertyName.toLowerCase().includes(search)
      )
    }

    // Apply status filter
    if (statusFilter !== "all") {
      results = results.filter(tenant => tenant.status === statusFilter)
    }

    // Apply property filter
    if (propertyFilter !== "all") {
      results = results.filter(tenant => tenant.propertyId === propertyFilter)
    }

    // Apply sorting
    switch (sortBy) {
      case "name":
        results.sort((a, b) => a.name.localeCompare(b.name))
        break
      case "property":
        results.sort((a, b) => a.propertyName.localeCompare(b.propertyName))
        break
      case "leaseEnd":
        results.sort((a, b) => new Date(a.leaseEnd).getTime() - new Date(b.leaseEnd).getTime())
        break
      case "status":
        results.sort((a, b) => a.paymentStatus.localeCompare(b.paymentStatus))
        break
    }

    setFilteredTenants(results)
  }, [searchTerm, statusFilter, propertyFilter, sortBy])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200">Paid</Badge>
      case "pending":
        return <Badge className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">Pending</Badge>
      case "late":
        return <Badge className="bg-rose-50 text-rose-700 hover:bg-rose-50 border-rose-200">Late</Badge>
      default:
        return null
    }
  }

  const getTenantStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200">Active</Badge>
      case "ending":
        return <Badge className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">Ending Soon</Badge>
      case "ended":
        return <Badge className="bg-gray-100 text-gray-700 hover:bg-gray-100 border-gray-200">Ended</Badge>
      default:
        return null
    }
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" className="gap-2 mb-4" asChild>
          <Link href="/dashboard/landlord" legacyBehavior>
            <ArrowLeft className="h-4 w-4" />
            Back to Landlord Dashboard
          </Link>
        </Button>
        <h1 className="text-2xl md:text-3xl font-montserrat font-bold mb-2">
          <span className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            Tenant Management
          </span>
        </h1>
        <p className="text-muted-foreground">
          View and manage all your tenants across properties
        </p>
      </div>
      {/* Filters & Actions */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-80">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search tenants..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="ending">Ending Soon</SelectItem>
              <SelectItem value="ended">Ended</SelectItem>
            </SelectContent>
          </Select>

          <Select value={propertyFilter} onValueChange={setPropertyFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Property" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              {uniqueProperties.map(property => (
                <SelectItem key={property.id} value={property.id}>
                  {property.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button asChild className="gap-2">
          <Link href="/dashboard/landlord/tenants/add" legacyBehavior>
            <span className="flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              Add Tenant
            </span>
          </Link>
        </Button>
      </div>
      {/* Tenants Table */}
      <Card className="border-0 property-card-shadow overflow-hidden">
        <CardHeader>
          <CardTitle>Tenants</CardTitle>
          <CardDescription>
            Managing {filteredTenants.length} {filteredTenants.length === 1 ? 'tenant' : 'tenants'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredTenants.length > 0 ? (
            <div className="overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[250px]">
                      <div className="flex items-center gap-2 cursor-pointer" onClick={() => setSortBy("name")}>
                        Tenant
                        {sortBy === "name" && <ArrowUpDown className="h-3 w-3" />}
                      </div>
                    </TableHead>
                    <TableHead className="hidden md:table-cell">Contact</TableHead>
                    <TableHead>
                      <div className="flex items-center gap-2 cursor-pointer" onClick={() => setSortBy("property")}>
                        Property
                        {sortBy === "property" && <ArrowUpDown className="h-3 w-3" />}
                      </div>
                    </TableHead>
                    <TableHead className="hidden md:table-cell">
                      <div className="flex items-center gap-2 cursor-pointer" onClick={() => setSortBy("leaseEnd")}>
                        Lease End
                        {sortBy === "leaseEnd" && <ArrowUpDown className="h-3 w-3" />}
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center gap-2 cursor-pointer" onClick={() => setSortBy("status")}>
                        Payment Status
                        {sortBy === "status" && <ArrowUpDown className="h-3 w-3" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTenants.map((tenant) => (
                    <TableRow key={tenant.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={tenant.avatarUrl} alt={tenant.name} />
                            <AvatarFallback>{tenant.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{tenant.name}</div>
                            <div className="text-sm text-muted-foreground md:hidden">
                              {tenant.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Mail className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                            {tenant.email}
                          </div>
                          <div className="flex items-center text-sm">
                            <Phone className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                            {tenant.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm">
                          <Building2 className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                          <span className="line-clamp-1">{tenant.propertyName}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="flex items-center text-sm">
                          <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                          {new Date(tenant.leaseEnd).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getPaymentStatusBadge(tenant.paymentStatus)}
                          {tenant.paymentStatus === "late" && (
                            <AlertCircle className="h-4 w-4 text-rose-500" />
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Tenant Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/landlord/tenants/${tenant.id}`} legacyBehavior>
                                <FileText className="mr-2 h-4 w-4" />
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/landlord/tenants/${tenant.id}/message`} legacyBehavior>
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Send Message
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/landlord/leases/${tenant.id}/renew`}>
                                <span className="flex items-center">
                                  <Calendar className="mr-2 h-4 w-4" />
                                  Renew Lease
                                </span>
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/landlord/payments/request/${tenant.id}`}>
                                <span className="flex items-center">
                                  <Bell className="mr-2 h-4 w-4" />
                                  Payment Reminder
                                </span>
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="py-12 text-center">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              {searchTerm || statusFilter !== "all" || propertyFilter !== "all" ? (
                <>
                  <h3 className="text-xl font-semibold mb-2">No tenants match your filters</h3>
                  <p className="text-muted-foreground mb-6">
                    Try adjusting your search criteria or filters
                  </p>
                  <Button onClick={() => {
                    setSearchTerm("")
                    setStatusFilter("all")
                    setPropertyFilter("all")
                  }}>
                    Clear Filters
                  </Button>
                </>
              ) : (
                <>
                  <h3 className="text-xl font-semibold mb-2">No tenants yet</h3>
                  <p className="text-muted-foreground mb-6">
                    Add your first tenant to start managing your rental relationships
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/landlord/tenants/add" legacyBehavior>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Tenant
                    </Link>
                  </Button>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}