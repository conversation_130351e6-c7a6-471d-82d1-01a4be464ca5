import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ApplicationStatus } from "@/components/application-status"
import { Clock, FileText } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function ApplicationsPage() {
  // Mock data for applications
  const applications = [
    {
      id: "app-1",
      propertyId: "prop-1",
      propertyName: "Modern Downtown Apartment",
      propertyImage: "/placeholder.svg?height=100&width=150",
      address: "123 Main Street, Vancouver, BC",
      appliedDate: "2023-04-15",
      status: "pending",
      nextSteps: "Waiting for landlord review",
    },
    {
      id: "app-2",
      propertyId: "prop-2",
      propertyName: "Spacious Family Home",
      propertyImage: "/placeholder.svg?height=100&width=150",
      address: "456 Oak Avenue, Toronto, ON",
      appliedDate: "2023-04-10",
      status: "approved",
      nextSteps: "Sign lease agreement",
    },
    {
      id: "app-3",
      propertyId: "prop-3",
      propertyName: "Cozy Studio Apartment",
      propertyImage: "/placeholder.svg?height=100&width=150",
      address: "789 Pine Street, Montreal, QC",
      appliedDate: "2023-04-05",
      status: "rejected",
      nextSteps: "Application was not approved",
    },
  ]

  return (
    <div className="container px-4 py-8 md:px-6 md:py-12">
      <div className="mb-8 space-y-4">
        <h1 className="text-3xl font-bold tracking-tight md:text-4xl">My Applications</h1>
        <p className="text-muted-foreground">Track the status of your rental applications</p>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="mb-6">
          <TabsTrigger value="all">All Applications</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {applications.length > 0 ? (
            applications.map((application) => <ApplicationCard key={application.id} application={application} />)
          ) : (
            <EmptyApplications />
          )}
        </TabsContent>

        <TabsContent value="pending" className="space-y-6">
          {applications
            .filter((a) => a.status === "pending")
            .map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
        </TabsContent>

        <TabsContent value="approved" className="space-y-6">
          {applications
            .filter((a) => a.status === "approved")
            .map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
        </TabsContent>

        <TabsContent value="rejected" className="space-y-6">
          {applications
            .filter((a) => a.status === "rejected")
            .map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
        </TabsContent>
      </Tabs>
    </div>
  )
}

function ApplicationCard({ application }: { application: any }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-start gap-4 space-y-0">
        <div className="hidden sm:block">
          <Image
            src={application.propertyImage || "/placeholder.svg"}
            alt={application.propertyName}
            width={150}
            height={100}
            className="rounded-md object-cover"
          />
        </div>
        <div className="flex-1 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle>{application.propertyName}</CardTitle>
            <ApplicationStatus status={application.status} />
          </div>
          <CardDescription>{application.address}</CardDescription>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Applied on {application.appliedDate}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <h3 className="font-medium">Next Steps</h3>
          <p className="text-sm text-muted-foreground">{application.nextSteps}</p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
          <Link href={`/properties/${application.propertyId}`}>View Property</Link>
        </Button>
        <Button asChild>
          <Link href={`/applications/${application.id}`}>Application Details</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

function EmptyApplications() {
  return (
    <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <div className="mb-4 rounded-full bg-muted p-3">
        <FileText className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="mb-2 text-lg font-medium">No Applications Yet</h3>
      <p className="mb-4 max-w-md text-sm text-muted-foreground">
        You haven't submitted any rental applications yet. Browse properties and apply to get started.
      </p>
      <Button asChild>
        <Link href="/properties">Browse Properties</Link>
      </Button>
    </div>
  )
}
