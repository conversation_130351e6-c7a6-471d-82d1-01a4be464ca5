// Script to approve all properties in the database
require('dotenv').config();
const { MongoClient } = require('mongodb');

async function approveProperties() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI is not defined in .env');
    process.exit(1);
  }
  
  console.log('Connecting to MongoDB...');
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');
    
    // Count pending properties
    const pendingCount = await propertiesCollection.countDocuments({ status: 'pending' });
    console.log(`Found ${pendingCount} pending properties`);
    
    if (pendingCount > 0) {
      // Update all pending properties to approved
      const result = await propertiesCollection.updateMany(
        { status: 'pending' },
        { $set: { status: 'approved' } }
      );
      
      console.log(`Updated ${result.modifiedCount} properties to 'approved' status`);
    }
    
    // Count properties by status
    const statusCounts = await propertiesCollection.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();
    
    console.log('\nProperties by status:');
    statusCounts.forEach(status => {
      console.log(`- ${status._id}: ${status.count}`);
    });
    
  } catch (error) {
    console.error('Error approving properties:', error);
  } finally {
    await client.close();
    console.log('\nDatabase connection closed');
  }
}

approveProperties().catch(console.error);
