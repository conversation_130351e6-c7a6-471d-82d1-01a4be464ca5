export interface User {
  id?: string;
  email: string;
  name: string;
  role: 'tenant' | 'landlord' | 'admin';
  createdAt: string;
  updatedAt: string;
}

export interface Property {
  id?: string;
  title: string;
  description?: string;
  address: string;
  city: string;
  province: string;
  postalCode?: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  images: string[];
  amenities: string[];
  isNew?: boolean;
  isFeatured?: boolean;
  createdAt?: string;
  updatedAt?: string;
  landlordId?: string;
  status?: 'available' | 'rented' | 'pending' | 'approved';
}

export interface Application {
  id?: string;
  propertyId: string;
  tenantId: string;
  status: 'pending' | 'approved' | 'rejected';
  income: number;
  employmentStatus: string;
  references: {
    name: string;
    relationship: string;
    contact: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface MarketInsight {
  id?: string;
  location: string;
  averageRent: number;
  vacancyRate: number;
  yearOverYearChange: number;
  dataPoints: {
    date: string;
    value: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface Profile {
  id?: string;
  userId: string;
  phoneNumber?: string;
  bio?: string;
  profilePicture?: string;
  preferences: {
    minPrice?: number;
    maxPrice?: number;
    minBedrooms?: number;
    maxBedrooms?: number;
    locations?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export type PropertyWithImages = Property & {
  images: PropertyImage[]
  amenities: Amenity[]
}

export type PropertyImage = {
  id: string
  property_id: string
  image_url: string
  is_primary: boolean
  created_at: string
}

export type Amenity = {
  id: string
  name: string
  category: string
  created_at: string
}

export type PropertyAmenity = {
  property_id: string
  amenity_id: string
}

export type Approval = {
  id: string
  application_id: string
  approved_by: string
  notes?: string
  created_at: string
}

export type Payment = {
  id: string
  property_id: string
  user_id: string
  amount: number
  payment_date: string
  payment_type: "rent" | "deposit" | "fee"
  status: "pending" | "completed" | "failed"
  created_at: string
}

export interface SavedProperty {
  id?: string;
  userId: string;
  propertyId: string;
  createdAt?: string;
}

export interface Appointment {
  id?: string;
  propertyId: string;
  userId: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  googleCalendarEventId?: string;
  googleCalendarLink?: string;
  createdAt?: string;
  updatedAt?: string;
}
