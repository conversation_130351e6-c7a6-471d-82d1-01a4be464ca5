import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Building2, Facebook, Instagram, Twitter, Send, MapPin, Mail, Phone, Linkedin, Github, Youtube } from "lucide-react"
import { Separator } from "./ui/separator"
import { Badge } from "./ui/badge"

export default function Footer() {
  return (
    <footer className="bg-muted/30 border-t">
      <div className="container py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center gap-2">
              <div className="relative h-9 w-9 overflow-hidden text-primary bg-primary/10 rounded-xl flex items-center justify-center">
                <Building2 className="h-5 w-5" />
              </div>
              <span className="font-montserrat text-xl font-bold bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent">RentCentral</span>
            </Link>

            <p className="mt-4 text-muted-foreground text-sm md:max-w-xs">
              Simplifying the rental journey with transparent, modern tools for both renters and property owners. Find your perfect home or tenant with ease.
            </p>

            <div className="mt-6 flex flex-wrap gap-3">
              <Button variant="outline" size="icon" className="rounded-full h-9 w-9 bg-background/50 hover:bg-background">
                <Facebook className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Facebook</span>
              </Button>
              <Button variant="outline" size="icon" className="rounded-full h-9 w-9 bg-background/50 hover:bg-background">
                <Twitter className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Twitter</span>
              </Button>
              <Button variant="outline" size="icon" className="rounded-full h-9 w-9 bg-background/50 hover:bg-background">
                <Instagram className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Instagram</span>
              </Button>
              <Button variant="outline" size="icon" className="rounded-full h-9 w-9 bg-background/50 hover:bg-background">
                <Linkedin className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">LinkedIn</span>
              </Button>
              <Button variant="outline" size="icon" className="rounded-full h-9 w-9 bg-background/50 hover:bg-background">
                <Youtube className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">YouTube</span>
              </Button>
            </div>

            <div className="mt-8 pt-6 border-t border-border/40">
              <Badge variant="outline" className="mb-3 bg-primary/5">Stay Updated</Badge>
              <div className="flex max-w-sm">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="rounded-l-lg rounded-r-none focus-visible:ring-0 focus-visible:ring-transparent border-r-0"
                />
                <Button type="submit" className="rounded-l-none rounded-r-lg">
                  <span>Subscribe</span>
                  <Send className="h-4 w-4 ml-2" />
                </Button>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">
                We'll send you updates on new properties and market trends.
              </p>
            </div>
          </div>

          <div className="space-y-5">
            <h3 className="font-montserrat text-sm font-bold uppercase tracking-wide text-foreground/80">Explore</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/properties"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Browse Properties
                </Link>
              </li>
              <li>
                <Link
                  href="/market-insights"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Market Insights
                </Link>
              </li>
              <li>
                <Link
                  href="/pricing"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  href="/communities"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Communities
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-5">
            <h3 className="font-montserrat text-sm font-bold uppercase tracking-wide text-foreground/80">For Landlords</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/landlord/post-property"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  List a Property
                </Link>
              </li>
              <li>
                <Link
                  href="/landlord/resources"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Resources
                </Link>
              </li>
              <li>
                <Link
                  href="/landlord/tools"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Tools
                </Link>
              </li>
              <li>
                <Link
                  href="/pricing"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center gap-2">
                  <span className="h-0.5 w-0 bg-primary group-hover:w-2 transition-all duration-300"></span>
                  Pricing Plans
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-5">
            <h3 className="font-montserrat text-sm font-bold uppercase tracking-wide text-foreground/80">Contact</h3>
            <ul className="space-y-4">
              <li className="flex items-start gap-3 text-sm text-muted-foreground group hover:text-foreground transition-colors">
                <div className="mt-0.5 h-7 w-7 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 text-primary">
                  <MapPin className="h-3.5 w-3.5" />
                </div>
                <span>123 Rental Street, Vancouver, BC V6B 4Y8, Canada</span>
              </li>
              <li className="flex items-center gap-3 text-sm text-muted-foreground group hover:text-foreground transition-colors">
                <div className="h-7 w-7 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 text-primary">
                  <Phone className="h-3.5 w-3.5" />
                </div>
                <span>+****************</span>
              </li>
              <li className="flex items-center gap-3 text-sm text-muted-foreground group hover:text-foreground transition-colors">
                <div className="h-7 w-7 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 text-primary">
                  <Mail className="h-3.5 w-3.5" />
                </div>
                <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors"><EMAIL></a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <Separator className="opacity-30" />
      <div className="container py-6 flex flex-col md:flex-row justify-between items-center text-xs text-muted-foreground">
        <p>© {new Date().getFullYear()} RentCentral. All rights reserved.</p>

        <div className="flex flex-wrap gap-6 mt-4 md:mt-0 justify-center">
          <Link href="/privacy" className="hover:text-primary transition-colors">
            Privacy Policy
          </Link>
          <Link href="/terms" className="hover:text-primary transition-colors">
            Terms of Service
          </Link>
          <Link href="/cookies" className="hover:text-primary transition-colors">
            Cookie Policy
          </Link>
          <Link href="/help" className="hover:text-primary transition-colors">
            Help Center
          </Link>
        </div>
      </div>
    </footer>
  );
}
