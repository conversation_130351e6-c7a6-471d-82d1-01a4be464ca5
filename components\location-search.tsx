"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MapPin, Search, Loader2 } from "lucide-react"
import { fetchPropertiesByLocation } from "@/app/actions/rentcast-actions"
import { toast } from "@/components/ui/use-toast"

export default function LocationSearch() {
  const router = useRouter()
  const [postalCode, setPostalCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!postalCode.trim()) return

    setIsLoading(true)
    try {
      const result = await fetchPropertiesByLocation(postalCode)
      if (result.error) {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      } else {
        router.push(`/properties?postalCode=${encodeURIComponent(postalCode)}`)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to search properties. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="flex w-full max-w-sm items-center space-x-2">
      <div className="relative flex-1">
        <MapPin className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Enter postal code or city"
          className="pl-8"
          value={postalCode}
          onChange={(e) => setPostalCode(e.target.value)}
          disabled={isLoading}
        />
      </div>
      <Button type="submit" disabled={isLoading}>
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Searching...
          </>
        ) : (
          <>
            <Search className="mr-2 h-4 w-4" />
            Search
          </>
        )}
      </Button>
    </form>
  )
}
