"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResol<PERSON> } from "@hookform/resolvers/zod"
import { useForm, type SubmitHandler } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { propertiesApi } from "@/lib/aws/api"
import PropertyUpload from "@/components/property-upload"
import { useAuth } from "@/components/auth-provider"
import { Loader2, AlertCircle, CheckCircle2, XCircle, Info } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { formatCurrency } from "@/lib/utils"

// Define the form schema with Zod
const propertyFormSchema = z.object({
  title: z.string().min(5, {
    message: "Title must be at least 5 characters.",
  }),
  description: z.string().min(20, {
    message: "Description must be at least 20 characters.",
  }),
  address: z.string().min(5, {
    message: "Address is required.",
  }),
  city: z.string().min(2, {
    message: "City is required.",
  }),
  province: z.string().min(2, {
    message: "Province is required.",
  }),
  postal_code: z.string().min(5, {
    message: "Postal code must be at least 5 characters.",
  }),
  property_type: z.string({
    required_error: "Please select a property type.",
  }),
  bedrooms: z.coerce.number().min(0, {
    message: "Bedrooms must be a positive number.",
  }),
  bathrooms: z.coerce.number().min(0, {
    message: "Bathrooms must be a positive number.",
  }),
  square_feet: z.coerce.number().min(1, {
    message: "Square feet must be a positive number.",
  }),
  price: z.coerce.number().min(1, {
    message: "Price must be a positive number.",
  }),
  available_from: z.string().min(1, {
    message: "Available date is required.",
  }),
  amenities: z.array(z.string()).optional(),
  images: z.array(z.string()).min(1, {
    message: "At least one image is required.",
  }),
  termsAgreed: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and conditions.",
  }),
  status: z.enum(['pending', 'approved', 'declined', 'appealed']).default('pending'),
  // New fields
  parking_spots: z.coerce.number().min(0, {
    message: "Parking spots must be a positive number.",
  }).default(0),
  year_built: z.coerce.number().min(1800, {
    message: "Year built must be after 1800.",
  }).max(new Date().getFullYear(), {
    message: "Year built cannot be in the future.",
  }).optional(),
  pets_allowed: z.boolean().default(false),
  smoking_allowed: z.boolean().default(false),
  furnished: z.boolean().default(false),
  utilities_included: z.boolean().default(false),
  lease_term: z.enum(['monthly', '6_months', '12_months', '24_months']).default('12_months'),
  security_deposit: z.coerce.number().min(0, {
    message: "Security deposit must be a positive number.",
  }).default(0),
})

type PropertyFormValues = z.infer<typeof propertyFormSchema>

// Property types
const propertyTypes = [
  { value: "apartment", label: "Apartment" },
  { value: "house", label: "House" },
  { value: "condo", label: "Condo" },
  { value: "townhouse", label: "Townhouse" },
  { value: "studio", label: "Studio" },
  { value: "basement", label: "Basement Suite" },
]

// Canadian provinces
const provinces = [
  { value: "AB", label: "Alberta" },
  { value: "BC", label: "British Columbia" },
  { value: "MB", label: "Manitoba" },
  { value: "NB", label: "New Brunswick" },
  { value: "NL", label: "Newfoundland and Labrador" },
  { value: "NS", label: "Nova Scotia" },
  { value: "ON", label: "Ontario" },
  { value: "PE", label: "Prince Edward Island" },
  { value: "QC", label: "Quebec" },
  { value: "SK", label: "Saskatchewan" },
  { value: "NT", label: "Northwest Territories" },
  { value: "NU", label: "Nunavut" },
  { value: "YT", label: "Yukon" },
]

// Lease terms
const leaseTerms = [
  { value: "monthly", label: "Monthly" },
  { value: "6_months", label: "6 Months" },
  { value: "12_months", label: "12 Months" },
  { value: "24_months", label: "24 Months" },
]

// Amenities list
const amenitiesList = [
  { id: "in-unit-laundry", label: "In-unit Laundry", category: "Appliances" },
  { id: "dishwasher", label: "Dishwasher", category: "Appliances" },
  { id: "air-conditioning", label: "Air Conditioning", category: "Climate Control" },
  { id: "balcony", label: "Balcony", category: "Exterior" },
  { id: "fitness-center", label: "Fitness Center", category: "Recreation" },
  { id: "parking", label: "Parking", category: "Exterior" },
  { id: "pet-friendly", label: "Pet Friendly", category: "Policies" },
  { id: "storage", label: "Storage", category: "Storage" },
  { id: "pool", label: "Pool", category: "Recreation" },
  { id: "elevator", label: "Elevator", category: "Accessibility" },
  { id: "security", label: "Security System", category: "Security" },
  { id: "furnished", label: "Furnished", category: "Interior" },
  { id: "hardwood-floors", label: "Hardwood Floors", category: "Interior" },
  { id: "stainless-steel", label: "Stainless Steel Appliances", category: "Appliances" },
  { id: "granite-counters", label: "Granite Countertops", category: "Interior" },
]

// Group amenities by category
const amenitiesByCategory = amenitiesList.reduce((acc: Record<string, typeof amenitiesList>, amenity) => {
  if (!acc[amenity.category]) {
    acc[amenity.category] = []
  }
  acc[amenity.category].push(amenity)
  return acc
}, {})

interface PropertyFormProps {
  propertyId?: string // Optional for editing existing properties
  onSuccess?: (propertyId: string) => void
}

export function PropertyForm({ propertyId, onSuccess }: PropertyFormProps) {
  const { toast } = useToast()
  const router = useRouter()
  const { user } = useAuth()
  const [uploadStep, setUploadStep] = useState<"details" | "images" | "amenities" | "preview">("details")
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingProperty, setIsLoadingProperty] = useState(!!propertyId)
  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formProgress, setFormProgress] = useState(0)
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)

  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertyFormSchema) as any,
    defaultValues: {
      title: "",
      description: "",
      address: "",
      city: "",
      province: "",
      postal_code: "",
      property_type: "",
      bedrooms: 0,
      bathrooms: 0,
      square_feet: 0,
      price: 0,
      available_from: "",
      amenities: [],
      images: [],
      termsAgreed: false,
      status: "pending",
      parking_spots: 0,
      year_built: undefined,
      pets_allowed: false,
      smoking_allowed: false,
      furnished: false,
      utilities_included: false,
      lease_term: "12_months",
      security_deposit: 0,
    },
  })

  // Calculate form completion progress
  useEffect(() => {
    const values = form.getValues()
    const totalFields = Object.keys(propertyFormSchema.shape).length
    const filledFields = Object.entries(values).filter(([key, value]) => {
      if (key === 'status') return true // Skip status as it's auto-set
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'boolean') return true // Boolean fields are always considered filled
      if (typeof value === 'number') return value > 0
      return !!value
    }).length

    setFormProgress(Math.round((filledFields / totalFields) * 100))
  }, [form.watch()])

  // Fetch property data if editing
  useEffect(() => {
    if (propertyId) {
      const fetchProperty = async () => {
        try {
          const property = await propertiesApi.getPropertyById(propertyId)

          // Format the data for the form
          form.reset({
            title: property.title,
            description: property.description,
            address: property.address,
            city: property.city,
            province: property.province,
            postal_code: property.postal_code,
            property_type: property.property_type,
            bedrooms: property.bedrooms,
            bathrooms: property.bathrooms,
            square_feet: property.square_feet,
            price: property.price,
            available_from: new Date(property.available_from).toISOString().split("T")[0],
            amenities: property.amenities?.map((a: any) => a.id) || [],
            images: property.images?.map((img: any) => img.image_url) || [],
            termsAgreed: true,
            parking_spots: property.parking_spots || 0,
            year_built: property.year_built,
            pets_allowed: property.pets_allowed || false,
            smoking_allowed: property.smoking_allowed || false,
            furnished: property.furnished || false,
            utilities_included: property.utilities_included || false,
            lease_term: property.lease_term || "12_months",
            security_deposit: property.security_deposit || 0,
          })

          // Set uploaded images
          setUploadedImages(property.images?.map((img: any) => img.image_url) || [])
        } catch (error) {
          console.error("Error fetching property:", error)
          toast({
            title: "Error",
            description: "Failed to load property data. Please try again.",
            variant: "destructive",
          })
        } finally {
          setIsLoadingProperty(false)
        }
      }

      fetchProperty()
    }
  }, [propertyId, form])

  // Handle image upload completion
  const handleUploadComplete = (urls: string[]) => {
    setUploadedImages(urls)
    form.setValue("images", urls)
    form.clearErrors("images")
  }

  const onSubmit: SubmitHandler<PropertyFormValues> = async (data) => {
    try {
      setIsSubmitting(true)
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to submit a property.",
          variant: "destructive",
        })
        return
      }

      const propertyData = {
        ...data,
        userId: user.uid,
        status: "pending",
        propertyType: data.property_type, // Add this mapping for the API
      }

      console.log("Submitting property data:", propertyData)

      if (propertyId) {
        // Use direct fetch for updating
        const response = await fetch(`/api/properties/${propertyId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(propertyData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to update property')
        }

        toast({
          title: "Success",
          description: "Property updated successfully.",
        })
      } else {
        // Use direct fetch for creating
        const response = await fetch('/api/properties', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(propertyData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create property')
        }

        const result = await response.json()
        const newPropertyId = result.id

        toast({
          title: "Success",
          description: "Property submitted for approval.",
        })

        if (onSuccess) {
          onSuccess(newPropertyId)
        }
      }

      setShowSuccessAlert(true)
      setTimeout(() => {
        router.refresh()
      }, 2000)
    } catch (error) {
      console.error("Error submitting property:", error)
      toast({
        title: "Error",
        description: "Failed to submit property. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Helper function to check for errors in a section
  const hasErrorsInSection = (section: string) => {
    switch (section) {
      case 'details':
        return !!form.formState.errors.title ||
               !!form.formState.errors.description ||
               !!form.formState.errors.address ||
               !!form.formState.errors.city ||
               !!form.formState.errors.province ||
               !!form.formState.errors.postal_code ||
               !!form.formState.errors.property_type ||
               !!form.formState.errors.bedrooms ||
               !!form.formState.errors.bathrooms ||
               !!form.formState.errors.square_feet ||
               !!form.formState.errors.price ||
               !!form.formState.errors.available_from;
      case 'images':
        return !!form.formState.errors.images;
      case 'amenities':
        return !!form.formState.errors.amenities;
      case 'preview':
        return !!form.formState.errors.termsAgreed;
      default:
        return false;
    }
  };

  // Show loading state while fetching property data
  if (isLoadingProperty) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading property data...</span>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {showSuccessAlert && (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Success!</AlertTitle>
            <AlertDescription className="text-green-700">
              Your property has been {propertyId ? "updated" : "submitted"} successfully.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">
            {propertyId ? "Edit Property" : "Add New Property"}
          </h2>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Form completion:</span>
            <Progress value={formProgress} className="w-24" />
            <span className="text-sm font-medium">{formProgress}%</span>
          </div>
        </div>

        <Tabs value={uploadStep} onValueChange={(value) => setUploadStep(value as any)} className="mb-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details" className="relative">
              1. Property Details
              {hasErrorsInSection('details') && (
                <span className="absolute text-red-500 text-sm" style={{ top: 0, right: 2 }}>*</span>
              )}
            </TabsTrigger>
            <TabsTrigger value="images" className="relative">
              2. Images
              {hasErrorsInSection('images') && (
                <span className="absolute text-red-500 text-sm" style={{ top: 0, right: 2 }}>*</span>
              )}
            </TabsTrigger>
            <TabsTrigger value="amenities" className="relative">
              3. Amenities
              {hasErrorsInSection('amenities') && (
                <span className="absolute text-red-500 text-sm" style={{ top: 0, right: 2 }}>*</span>
              )}
            </TabsTrigger>
            <TabsTrigger value="preview" className="relative">
              4. Review & Submit
              {hasErrorsInSection('preview') && (
                <span className="absolute text-red-500 text-sm" style={{ top: 0, right: 2 }}>*</span>
              )}
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Property Details</CardTitle>
                  <CardDescription>Enter the basic information about your property.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Property Title</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Modern Downtown Apartment" {...field} />
                        </FormControl>
                        <FormDescription>
                          Create a catchy title that highlights the best features of your property.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe your property in detail..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Include details about the property, neighborhood, and any special features.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader>
                  <CardTitle>Location</CardTitle>
                  <CardDescription>Provide the location details of your property.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., 123 Main Street" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Vancouver" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="province"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Province</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select province" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {provinces.map((province) => (
                                <SelectItem key={province.value} value={province.value}>
                                  {province.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="postal_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., V6B 2W9" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader>
                  <CardTitle>Property Details</CardTitle>
                  <CardDescription>Provide specific details about your property.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="property_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Property Type</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select property type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {propertyTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="bedrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bedrooms</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bathrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bathrooms</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="0.5"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="square_feet"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Square Feet</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Monthly Rent (CAD)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="available_from"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Available From</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* New fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="parking_spots"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Parking Spots</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="year_built"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Year Built</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1800"
                              max={new Date().getFullYear()}
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="lease_term"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Lease Term</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select lease term" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {leaseTerms.map((term) => (
                                <SelectItem key={term.value} value={term.value}>
                                  {term.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="security_deposit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Security Deposit (CAD)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="1"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="pets_allowed"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Pets Allowed
                              </FormLabel>
                              <FormDescription>
                                Check if pets are allowed in this property
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="smoking_allowed"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Smoking Allowed
                              </FormLabel>
                              <FormDescription>
                                Check if smoking is allowed in this property
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="furnished"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Furnished
                              </FormLabel>
                              <FormDescription>
                                Check if the property comes furnished
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="utilities_included"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Utilities Included
                              </FormLabel>
                              <FormDescription>
                                Check if utilities are included in the rent
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button type="button" onClick={() => setUploadStep("images")}>
                    Next: Upload Images
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="images">
              <Card>
                <CardHeader>
                  <CardTitle>Property Images</CardTitle>
                  <CardDescription>
                    Upload high-quality images of your property. The first image will be the main image shown in search
                    results.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="images"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <PropertyUpload
                            onUploadComplete={handleUploadComplete}
                            maxFiles={10}
                            initialImages={uploadedImages}
                          />
                        </FormControl>
                        <FormDescription>
                          Upload at least one image of your property. You can upload up to 10 images.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button type="button" variant="outline" onClick={() => setUploadStep("details")}>
                    Back: Property Details
                  </Button>
                  <Button type="button" onClick={() => setUploadStep("amenities")}>
                    Next: Amenities
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="amenities">
              <Card>
                <CardHeader>
                  <CardTitle>Amenities</CardTitle>
                  <CardDescription>Select the amenities available at your property.</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="amenities"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-6">
                          {Object.entries(amenitiesByCategory).map(([category, amenities]) => (
                            <div key={category} className="space-y-2">
                              <h3 className="font-medium">{category}</h3>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                                {amenities.map((amenity) => (
                                  <FormItem key={amenity.id} className="flex flex-row items-start space-x-2 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(amenity.id)}
                                        onCheckedChange={(checked) => {
                                          const updatedValue = checked
                                            ? [...(field.value || []), amenity.id]
                                            : (field.value || []).filter((value) => value !== amenity.id)
                                          field.onChange(updatedValue)
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="font-normal text-sm">{amenity.label}</FormLabel>
                                  </FormItem>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button type="button" variant="outline" onClick={() => setUploadStep("images")}>
                    Back: Images
                  </Button>
                  <Button type="button" onClick={() => setUploadStep("preview")}>
                    Next: Review & Submit
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="preview">
              <Card>
                <CardHeader>
                  <CardTitle>Review Your Listing</CardTitle>
                  <CardDescription>Please review your property listing details before submitting.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium mb-2">Property Information</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Title:</span>
                          <span className="font-medium">{form.getValues("title")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Type:</span>
                          <span className="font-medium capitalize">{form.getValues("property_type")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Price:</span>
                          <span className="font-medium">{formatCurrency(form.getValues("price"))}/month</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Bedrooms:</span>
                          <span className="font-medium">{form.getValues("bedrooms")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Bathrooms:</span>
                          <span className="font-medium">{form.getValues("bathrooms")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Square Feet:</span>
                          <span className="font-medium">{form.getValues("square_feet")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Available From:</span>
                          <span className="font-medium">{form.getValues("available_from")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Parking Spots:</span>
                          <span className="font-medium">{form.getValues("parking_spots")}</span>
                        </div>
                        {form.getValues("year_built") && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Year Built:</span>
                            <span className="font-medium">{form.getValues("year_built")}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Lease Term:</span>
                          <span className="font-medium">
                            {leaseTerms.find(t => t.value === form.getValues("lease_term"))?.label || "12 Months"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Security Deposit:</span>
                          <span className="font-medium">{formatCurrency(form.getValues("security_deposit"))}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-2">Location</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Address:</span>
                          <span className="font-medium">{form.getValues("address")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">City:</span>
                          <span className="font-medium">{form.getValues("city")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Province:</span>
                          <span className="font-medium">
                            {provinces.find(p => p.value === form.getValues("province"))?.label || form.getValues("province")}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Postal Code:</span>
                          <span className="font-medium">{form.getValues("postal_code")}</span>
                        </div>
                      </div>

                      <h3 className="font-medium mb-2 mt-4">Policies</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Pets Allowed:</span>
                          <span className="font-medium">{form.getValues("pets_allowed") ? "Yes" : "No"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Smoking Allowed:</span>
                          <span className="font-medium">{form.getValues("smoking_allowed") ? "Yes" : "No"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Furnished:</span>
                          <span className="font-medium">{form.getValues("furnished") ? "Yes" : "No"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Utilities Included:</span>
                          <span className="font-medium">{form.getValues("utilities_included") ? "Yes" : "No"}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Description</h3>
                    <p className="text-sm text-muted-foreground">{form.getValues("description")}</p>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Images</h3>
                    <div className="grid grid-cols-5 gap-2">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative aspect-square rounded-md overflow-hidden border">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`Property ${index + 1}`}
                            className="object-cover w-full h-full"
                          />
                          {index === 0 && (
                            <div className="absolute bottom-1 left-1 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                              Primary
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Amenities</h3>
                    <div className="flex flex-wrap gap-2">
                      {form.getValues("amenities")?.map((amenityId) => {
                        const amenity = amenitiesList.find((a) => a.id === amenityId)
                        return amenity ? (
                          <Badge key={amenityId} variant="outline">
                            {amenity.label}
                          </Badge>
                        ) : null
                      })}
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="termsAgreed"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>I agree to the terms and conditions</FormLabel>
                          <FormDescription>
                            By submitting this listing, you agree to our terms of service and privacy policy.
                          </FormDescription>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button type="button" variant="outline" onClick={() => setUploadStep("amenities")}>
                    Back: Amenities
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Property'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </form>
    </Form>
  )
}
