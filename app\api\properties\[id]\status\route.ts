import { NextRequest, NextResponse } from 'next/server';
import clientPromise from "@/lib/mongodb/client";
import { ObjectId } from "mongodb";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the request body
    const { status } = await request.json();

    if (!status || !['approved', 'pending', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'rentcentral');
    const propertiesCollection = db.collection('properties');

    // Check if property exists
    let property;
    try {
      property = await propertiesCollection.findOne({
        _id: new ObjectId(params.id)
      });
    } catch (error) {
      console.error(`Error parsing ObjectId: ${params.id}`, error);
      return NextResponse.json({ error: "Invalid property ID format" }, { status: 400 });
    }

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Update property status
    const result = await propertiesCollection.updateOne(
      { _id: new ObjectId(params.id) },
      { $set: { status } }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Get updated property
    const updatedProperty = await propertiesCollection.findOne({
      _id: new ObjectId(params.id)
    });

    return NextResponse.json({
      success: true,
      property: {
        ...updatedProperty,
        id: updatedProperty?._id.toString(),
        _id: undefined
      }
    });
  } catch (error) {
    console.error('Error updating property status:', error);
    return NextResponse.json(
      { error: 'Failed to update property status' },
      { status: 500 }
    );
  }
}