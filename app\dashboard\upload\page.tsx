"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/components/ui/use-toast"
import { ImageUploader } from "@/components/image-uploader"
import { submitProperty } from "@/app/actions/property-actions"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle } from "lucide-react"

const propertyFormSchema = z.object({
  title: z.string().min(5, {
    message: "Title must be at least 5 characters.",
  }),
  description: z.string().min(20, {
    message: "Description must be at least 20 characters.",
  }),
  address: z.string().min(5, {
    message: "Address is required.",
  }),
  city: z.string().min(2, {
    message: "City is required.",
  }),
  province: z.string().min(2, {
    message: "Province is required.",
  }),
  postalCode: z.string().min(5, {
    message: "Postal code must be at least 5 characters.",
  }),
  propertyType: z.string({
    required_error: "Please select a property type.",
  }),
  bedrooms: z.coerce.number().min(0, {
    message: "Bedrooms must be a positive number.",
  }),
  bathrooms: z.coerce.number().min(0, {
    message: "Bathrooms must be a positive number.",
  }),
  squareFeet: z.coerce.number().min(1, {
    message: "Square feet must be a positive number.",
  }),
  price: z.coerce.number().min(1, {
    message: "Price must be a positive number.",
  }),
  availableFrom: z.string().min(1, {
    message: "Available date is required.",
  }),
  amenities: z.array(z.string()).optional(),
  images: z.array(z.string()).min(1, {
    message: "At least one image is required.",
  }),
  termsAgreed: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and conditions.",
  }),
})

type PropertyFormValues = z.infer<typeof propertyFormSchema>

const amenitiesList = [
  { id: "in-unit-laundry", label: "In-unit Laundry" },
  { id: "dishwasher", label: "Dishwasher" },
  { id: "air-conditioning", label: "Air Conditioning" },
  { id: "balcony", label: "Balcony" },
  { id: "fitness-center", label: "Fitness Center" },
  { id: "parking", label: "Parking" },
  { id: "pet-friendly", label: "Pet Friendly" },
  { id: "storage", label: "Storage" },
  { id: "pool", label: "Pool" },
  { id: "elevator", label: "Elevator" },
  { id: "security", label: "Security System" },
  { id: "furnished", label: "Furnished" },
]

export default function UploadPropertyPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [uploadStep, setUploadStep] = useState<"details" | "images" | "preview" | "success">("details")

  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: {
      title: "",
      description: "",
      address: "",
      city: "",
      province: "",
      postalCode: "",
      propertyType: "",
      bedrooms: 1,
      bathrooms: 1,
      squareFeet: 0,
      price: 0,
      availableFrom: new Date().toISOString().split("T")[0],
      amenities: [],
      images: [],
      termsAgreed: false,
    },
  })

  const onImagesUploaded = (imageUrls: string[]) => {
    setUploadedImages(imageUrls)
    form.setValue("images", imageUrls)
  }

  async function onSubmit(data: PropertyFormValues) {
    setIsLoading(true)

    try {
      // In a real app, you would call your API to submit the property
      const result = await submitProperty(data)

      if (result.success) {
        setUploadStep("success")
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to submit property. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to submit property. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="container px-4 py-8 md:px-6 md:py-12">
        <div className="mb-8 space-y-4">
          <h1 className="text-3xl font-bold tracking-tight md:text-4xl">Upload Property</h1>
          <p className="text-muted-foreground">
            List your property for rent. All listings are subject to approval before being published.
          </p>
        </div>

        {uploadStep === "success" ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Property Submitted Successfully
              </CardTitle>
              <CardDescription>Your property has been submitted and is pending approval.</CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Approval Required</AlertTitle>
                <AlertDescription>
                  Your property listing will be reviewed by our team before being published. This usually takes 1-2
                  business days.
                </AlertDescription>
              </Alert>
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button onClick={() => router.push("/dashboard/my-properties")}>View My Properties</Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    form.reset()
                    setUploadedImages([])
                    setUploadStep("details")
                  }}
                >
                  Upload Another Property
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Property Details</CardTitle>
              <CardDescription>
                Provide accurate information about your property to attract potential tenants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={uploadStep} onValueChange={(value) => setUploadStep(value as any)} className="mb-6">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">1. Property Details</TabsTrigger>
                  <TabsTrigger value="images" disabled={!form.formState.isValid}>
                    2. Upload Images
                  </TabsTrigger>
                  <TabsTrigger value="preview" disabled={!form.formState.isValid || uploadedImages.length === 0}>
                    3. Preview & Submit
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                  <TabsContent value="details" className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Basic Information</h3>
                      <div className="grid gap-4 sm:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Property Title</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., Modern Downtown Apartment" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="propertyType"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Property Type</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select property type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="apartment">Apartment</SelectItem>
                                  <SelectItem value="house">House</SelectItem>
                                  <SelectItem value="condo">Condo</SelectItem>
                                  <SelectItem value="townhouse">Townhouse</SelectItem>
                                  <SelectItem value="studio">Studio</SelectItem>
                                  <SelectItem value="basement">Basement Suite</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe your property in detail..."
                                className="min-h-[120px]"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Include details about the property, neighborhood, and any special features.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Location</h3>
                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Street Address</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., 123 Main Street" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="grid gap-4 sm:grid-cols-3">
                        <FormField
                          control={form.control}
                          name="city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>City</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., Vancouver" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="province"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Province</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select province" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="AB">Alberta</SelectItem>
                                  <SelectItem value="BC">British Columbia</SelectItem>
                                  <SelectItem value="MB">Manitoba</SelectItem>
                                  <SelectItem value="NB">New Brunswick</SelectItem>
                                  <SelectItem value="NL">Newfoundland and Labrador</SelectItem>
                                  <SelectItem value="NS">Nova Scotia</SelectItem>
                                  <SelectItem value="ON">Ontario</SelectItem>
                                  <SelectItem value="PE">Prince Edward Island</SelectItem>
                                  <SelectItem value="QC">Quebec</SelectItem>
                                  <SelectItem value="SK">Saskatchewan</SelectItem>
                                  <SelectItem value="NT">Northwest Territories</SelectItem>
                                  <SelectItem value="NU">Nunavut</SelectItem>
                                  <SelectItem value="YT">Yukon</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="postalCode"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Postal Code</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., V6B 2W9" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Property Details</h3>
                      <div className="grid gap-4 sm:grid-cols-4">
                        <FormField
                          control={form.control}
                          name="bedrooms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bedrooms</FormLabel>
                              <FormControl>
                                <Input type="number" min="0" step="1" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="bathrooms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bathrooms</FormLabel>
                              <FormControl>
                                <Input type="number" min="0" step="0.5" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="squareFeet"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Square Feet</FormLabel>
                              <FormControl>
                                <Input type="number" min="1" step="1" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Monthly Rent ($)</FormLabel>
                              <FormControl>
                                <Input type="number" min="1" step="1" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={form.control}
                        name="availableFrom"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Available From</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Amenities</h3>
                      <FormField
                        control={form.control}
                        name="amenities"
                        render={() => (
                          <FormItem>
                            <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
                              {amenitiesList.map((amenity) => (
                                <FormField
                                  key={amenity.id}
                                  control={form.control}
                                  name="amenities"
                                  render={({ field }) => {
                                    return (
                                      <FormItem
                                        key={amenity.id}
                                        className="flex flex-row items-start space-x-2 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            checked={field.value?.includes(amenity.id)}
                                            onCheckedChange={(checked) => {
                                              return checked
                                                ? field.onChange([...(field.value || []), amenity.id])
                                                : field.onChange(field.value?.filter((value) => value !== amenity.id))
                                            }}
                                          />
                                        </FormControl>
                                        <FormLabel className="font-normal">{amenity.label}</FormLabel>
                                      </FormItem>
                                    )
                                  }}
                                />
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button type="button" onClick={() => setUploadStep("images")}>
                        Next: Upload Images
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="images" className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Property Images</h3>
                      <FormField
                        control={form.control}
                        name="images"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Upload Images</FormLabel>
                            <FormControl>
                              <ImageUploader
                                onImagesUploaded={onImagesUploaded}
                                maxImages={10}
                                initialImages={uploadedImages}
                              />
                            </FormControl>
                            <FormDescription>
                              Upload at least one image of your property. You can upload up to 10 images.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-between">
                      <Button type="button" variant="outline" onClick={() => setUploadStep("details")}>
                        Back: Property Details
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setUploadStep("preview")}
                        disabled={uploadedImages.length === 0}
                      >
                        Next: Preview & Submit
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Preview Your Listing</h3>
                      <div className="rounded-lg border p-4">
                        <div className="grid gap-6 md:grid-cols-[300px_1fr]">
                          <div>
                            {uploadedImages.length > 0 && (
                              <div className="relative aspect-[4/3] w-full overflow-hidden rounded-md">
                                <img
                                  src={uploadedImages[0] || "/placeholder.svg"}
                                  alt="Property preview"
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            )}
                            <div className="mt-4 grid grid-cols-3 gap-2">
                              {uploadedImages.slice(1, 4).map((image, index) => (
                                <div key={index} className="relative aspect-square w-full overflow-hidden rounded-md">
                                  <img
                                    src={image || "/placeholder.svg"}
                                    alt={`Property image ${index + 2}`}
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <h2 className="text-xl font-bold">{form.getValues("title")}</h2>
                              <p className="text-muted-foreground">
                                {form.getValues("address")}, {form.getValues("city")}, {form.getValues("province")}{" "}
                                {form.getValues("postalCode")}
                              </p>
                            </div>
                            <div className="flex items-center gap-4 text-sm">
                              <span>${form.getValues("price")}/month</span>
                              <span>{form.getValues("bedrooms")} Bed</span>
                              <span>{form.getValues("bathrooms")} Bath</span>
                              <span>{form.getValues("squareFeet")} sqft</span>
                            </div>
                            <div>
                              <h3 className="font-medium">Description</h3>
                              <p className="text-sm text-muted-foreground">{form.getValues("description")}</p>
                            </div>
                            <div>
                              <h3 className="font-medium">Amenities</h3>
                              <div className="mt-2 flex flex-wrap gap-2">
                                {form.getValues("amenities")?.map((amenityId) => {
                                  const amenity = amenitiesList.find((a) => a.id === amenityId)
                                  return (
                                    <span
                                      key={amenityId}
                                      className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                                    >
                                      {amenity?.label}
                                    </span>
                                  )
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="termsAgreed"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>I agree to the terms and conditions</FormLabel>
                            <FormDescription>
                              By submitting this listing, you agree to our terms of service and privacy policy.
                            </FormDescription>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-between">
                      <Button type="button" variant="outline" onClick={() => setUploadStep("images")}>
                        Back: Upload Images
                      </Button>
                      <Button type="submit" disabled={isLoading || !form.formState.isValid}>
                        {isLoading ? "Submitting..." : "Submit Property for Review"}
                      </Button>
                    </div>
                  </TabsContent>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="flex justify-between border-t px-6 py-4">
              <p className="text-sm text-muted-foreground">
                All properties are subject to review before being listed publicly.
              </p>
            </CardFooter>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
