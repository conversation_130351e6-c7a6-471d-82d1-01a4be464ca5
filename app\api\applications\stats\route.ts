import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET() {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { db } = await connectToDatabase()
    
    // Get all properties owned by the landlord
    const properties = await db.collection('properties')
      .find({ landlordId: new ObjectId(session.user.id) })
      .project({ _id: 1 })
      .toArray()
    
    const propertyIds = properties.map(p => p._id)

    // Get applications for these properties
    const applications = await db.collection('applications')
      .find({ propertyId: { $in: propertyIds } })
      .toArray()

    const stats = {
      total: applications.length,
      pending: applications.filter(a => a.status === 'pending').length,
      approved: applications.filter(a => a.status === 'approved').length,
      rejected: applications.filter(a => a.status === 'rejected').length,
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching application stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch application stats' },
      { status: 500 }
    )
  }
} 