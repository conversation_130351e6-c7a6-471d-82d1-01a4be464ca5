import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET() {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { db } = await connectToDatabase()
    const properties = await db.collection('properties')
      .find({ landlordId: new ObjectId(session.user.id) })
      .toArray()

    const stats = {
      total: properties.length,
      approved: properties.filter(p => p.status === 'approved').length,
      pending: properties.filter(p => p.status === 'pending').length,
      rented: properties.filter(p => p.status === 'rented').length,
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching property stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch property stats' },
      { status: 500 }
    )
  }
} 